const axios = require('axios');

async function testWorkStartedField() {
  console.log('🔍 TESTING WORKSTARTED FIELD IN API RESPONSE');
  console.log('='.repeat(60));
  
  try {
    // Step 1: Login to get session
    console.log('\n1. Logging in...');
    const loginResponse = await axios.post('http://localhost:8080/api/auth/login', {
      department: 'FINANCE',
      username: 'FELIX AYISI',
      password: 'felix123'
    }, {
      withCredentials: true
    });

    console.log('✅ Login successful');

    // Step 2: Get vouchers from API
    console.log('\n2. Fetching vouchers from API...');
    const vouchersResponse = await axios.get('http://localhost:8080/api/vouchers', {
      withCredentials: true
    });

    console.log(`📊 Received ${vouchersResponse.data.length} vouchers from API`);

    // Step 3: Find Addy Sabah voucher
    const addySabahVoucher = vouchersResponse.data.find(v => 
      v.claimant && v.claimant.includes('ADDY SABAH')
    );

    if (!addySabahVoucher) {
      console.log('❌ Addy Sabah voucher not found in API response');
      return;
    }

    console.log('\n3. ADDY SABAH VOUCHER API RESPONSE:');
    console.log('   🆔 Voucher ID:', addySabahVoucher.voucherId);
    console.log('   👤 Claimant:', addySabahVoucher.claimant);
    console.log('   🏢 Department:', addySabahVoucher.department);
    console.log('   🏢 Original Department:', addySabahVoucher.originalDepartment);
    console.log('   📊 Status:', addySabahVoucher.status);
    console.log('   📥 Received by Audit:', addySabahVoucher.receivedByAudit);
    console.log('   🔧 Work Started (NEW):', addySabahVoucher.workStarted);
    console.log('   💰 Pre-audited Amount:', addySabahVoucher.preAuditedAmount);
    console.log('   👨‍💼 Pre-audited By:', addySabahVoucher.preAuditedBy);
    console.log('   📤 Dispatched:', addySabahVoucher.dispatched);

    // Step 4: Check if workStarted field is present and correct
    console.log('\n4. WORKSTARTED FIELD ANALYSIS:');
    if (addySabahVoucher.workStarted === undefined) {
      console.log('❌ workStarted field is MISSING from API response');
    } else if (addySabahVoucher.workStarted === true) {
      console.log('✅ workStarted field is PRESENT and TRUE');
      console.log('✅ Voucher should appear in PENDING DISPATCH tab');
    } else if (addySabahVoucher.workStarted === false) {
      console.log('⚠️  workStarted field is PRESENT but FALSE');
      console.log('⚠️  Voucher should appear in NEW VOUCHERS tab');
    } else {
      console.log('❓ workStarted field has unexpected value:', addySabahVoucher.workStarted);
    }

    // Step 5: Check all FINANCE vouchers
    console.log('\n5. ALL FINANCE VOUCHERS WORKSTARTED STATUS:');
    const financeVouchers = vouchersResponse.data.filter(v => 
      v.originalDepartment === 'FINANCE' && 
      v.department === 'AUDIT' && 
      v.status === 'AUDIT: PROCESSING'
    );

    console.log(`📊 Found ${financeVouchers.length} FINANCE vouchers in AUDIT:`);
    financeVouchers.forEach((v, index) => {
      const tab = v.workStarted === true ? 'PENDING DISPATCH' : 'NEW VOUCHERS';
      console.log(`   ${index + 1}. ${v.voucherId} - ${v.claimant} - workStarted: ${v.workStarted} - Tab: ${tab}`);
    });

    // Step 6: Count by tab
    const newVouchersCount = financeVouchers.filter(v => v.workStarted !== true).length;
    const pendingDispatchCount = financeVouchers.filter(v => v.workStarted === true).length;

    console.log('\n6. TAB COUNTS:');
    console.log(`   📋 NEW VOUCHERS: ${newVouchersCount}`);
    console.log(`   📤 PENDING DISPATCH: ${pendingDispatchCount}`);
    console.log(`   📊 Total: ${newVouchersCount + pendingDispatchCount}`);

    console.log('\n✅ WORKSTARTED FIELD TEST COMPLETE!');

  } catch (error) {
    console.error('❌ Test error:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the test
testWorkStartedField().catch(console.error);
