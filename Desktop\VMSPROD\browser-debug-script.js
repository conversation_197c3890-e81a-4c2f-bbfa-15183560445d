// 🔍 BROWSER CONSOLE DEBUGGING SCRIPT
// Co<PERSON> and paste this into the browser console after logging in as FINANCE user

console.log('🔍 STARTING BROWSER-BASED VOUCHER DEBUGGING');

// Function to analyze voucher store
function analyzeVoucherStore() {
  // Try to access the store from different possible locations
  let store = null;
  
  if (window.useVoucherStore) {
    store = window.useVoucherStore.getState();
  } else if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
    console.log('Trying to access store via React DevTools...');
    // Try to find store in React DevTools
  } else {
    console.log('❌ Cannot access Zustand store directly');
    return;
  }
  
  if (!store) {
    console.log('❌ Store not found');
    return;
  }
  
  const vouchers = store.vouchers || [];
  console.log(`📊 Store Analysis: ${vouchers.length} vouchers found`);
  
  if (vouchers.length === 0) {
    console.log('❌ No vouchers in store');
    return;
  }
  
  // Analyze first 5 vouchers in detail
  console.log('\n🔍 DETAILED VOUCHER ANALYSIS:');
  vouchers.slice(0, 5).forEach((v, i) => {
    console.log(`\n${i+1}. Voucher: ${v.voucherId || v.voucher_id || 'NO_ID'}`);
    console.log(`   department: "${v.department}"`);
    console.log(`   originalDepartment: "${v.originalDepartment}"`);
    console.log(`   original_department: "${v.original_department}"`);
    console.log(`   sentToAudit: ${v.sentToAudit} (${typeof v.sentToAudit})`);
    console.log(`   sent_to_audit: ${v.sent_to_audit} (${typeof v.sent_to_audit})`);
    
    // Check if this voucher would match FINANCE filter
    const currentDeptMatch = v.department === 'FINANCE';
    const originalDeptMatch = v.originalDepartment === 'FINANCE';
    const matches = currentDeptMatch || originalDeptMatch;
    console.log(`   FINANCE FILTER: ${matches ? '✅ MATCHES' : '❌ NO MATCH'} (current: ${currentDeptMatch}, original: ${originalDeptMatch})`);
  });
  
  // Test the exact filtering logic
  console.log('\n🧪 TESTING FILTERING LOGIC:');
  const department = 'FINANCE';
  
  const allRelevantVouchers = vouchers.filter(v =>
    v.department === department || v.originalDepartment === department
  );
  console.log(`   All relevant vouchers: ${allRelevantVouchers.length}`);
  
  const pendingVouchers = allRelevantVouchers.filter(v =>
    v.department === department && !v.sentToAudit
  );
  console.log(`   Pending vouchers: ${pendingVouchers.length}`);
  
  const processingVouchers = allRelevantVouchers.filter(v =>
    v.originalDepartment === department && v.sentToAudit === true
  );
  console.log(`   Processing vouchers: ${processingVouchers.length}`);
  
  // Show breakdown by department
  console.log('\n📊 VOUCHER BREAKDOWN BY DEPARTMENT:');
  const byDept = {};
  vouchers.forEach(v => {
    if (!byDept[v.department]) byDept[v.department] = [];
    byDept[v.department].push(v);
  });
  
  Object.keys(byDept).forEach(dept => {
    console.log(`   ${dept}: ${byDept[dept].length} vouchers`);
  });
  
  // Show breakdown by original department
  console.log('\n📊 VOUCHER BREAKDOWN BY ORIGINAL DEPARTMENT:');
  const byOrigDept = {};
  vouchers.forEach(v => {
    const origDept = v.originalDepartment || 'UNDEFINED';
    if (!byOrigDept[origDept]) byOrigDept[origDept] = [];
    byOrigDept[origDept].push(v);
  });
  
  Object.keys(byOrigDept).forEach(dept => {
    console.log(`   ${dept}: ${byOrigDept[dept].length} vouchers`);
  });
  
  // Final diagnosis
  console.log('\n🎯 DIAGNOSIS:');
  if (allRelevantVouchers.length === 14) {
    console.log('✅ Filtering logic is working correctly');
  } else {
    console.log(`❌ Filtering logic issue: Expected 14, got ${allRelevantVouchers.length}`);
    
    // Check for common issues
    const withOriginalDept = vouchers.filter(v => v.originalDepartment !== undefined).length;
    const withOriginal_dept = vouchers.filter(v => v.original_department !== undefined).length;
    
    console.log(`   Vouchers with originalDepartment: ${withOriginalDept}/${vouchers.length}`);
    console.log(`   Vouchers with original_department: ${withOriginal_dept}/${vouchers.length}`);
    
    if (withOriginalDept < vouchers.length) {
      console.log('❌ ISSUE: Some vouchers missing originalDepartment field');
    }
  }
}

// Function to test API directly
async function testAPIDirectly() {
  console.log('\n🌐 TESTING API DIRECTLY:');
  
  try {
    const response = await fetch('/api/vouchers', {
      credentials: 'include'
    });
    
    if (response.ok) {
      const apiVouchers = await response.json();
      console.log(`✅ API returned ${apiVouchers.length} vouchers`);
      
      // Check first voucher
      if (apiVouchers.length > 0) {
        const sample = apiVouchers[0];
        console.log('\n📋 SAMPLE API VOUCHER:');
        console.log(`   voucherId: ${sample.voucherId}`);
        console.log(`   department: "${sample.department}"`);
        console.log(`   originalDepartment: "${sample.originalDepartment}"`);
        console.log(`   original_department: "${sample.original_department}"`);
        console.log(`   sentToAudit: ${sample.sentToAudit} (${typeof sample.sentToAudit})`);
        console.log(`   sent_to_audit: ${sample.sent_to_audit} (${typeof sample.sent_to_audit})`);
      }
      
      // Test filtering on API data
      const financeRelevant = apiVouchers.filter(v => 
        v.department === 'FINANCE' || v.originalDepartment === 'FINANCE'
      );
      console.log(`   FINANCE-relevant vouchers from API: ${financeRelevant.length}`);
      
    } else {
      console.log(`❌ API failed: ${response.status}`);
    }
  } catch (error) {
    console.log(`❌ API error: ${error.message}`);
  }
}

// Run the analysis
console.log('🚀 Running voucher store analysis...');
analyzeVoucherStore();

console.log('\n🚀 Testing API directly...');
testAPIDirectly();

console.log('\n📝 INSTRUCTIONS:');
console.log('1. Copy this entire script');
console.log('2. Open browser console (F12)');
console.log('3. Login as FINANCE user first');
console.log('4. Paste and run this script');
console.log('5. Share the complete output');
