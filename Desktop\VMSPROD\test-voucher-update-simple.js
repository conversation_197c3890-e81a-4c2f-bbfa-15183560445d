/**
 * SIMPLE TEST: Test voucher update API directly
 * 
 * This script tests the voucher update endpoint that's failing
 * during dispatch operations.
 */

import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: './server/.env' });

const connection = await mysql.createConnection({
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'vms@2025@1989',
  database: process.env.DB_NAME || 'vms_production'
});

console.log('🧪 SIMPLE VOUCHER UPDATE TEST');
console.log('==============================');

try {
  // Step 1: Find a voucher in AUDIT department
  console.log('\n1. Finding vouchers in AUDIT department...');
  
  const [auditVouchers] = await connection.execute(`
    SELECT id, voucher_id, status, department, audit_dispatched_by, dispatched
    FROM vouchers 
    WHERE department = 'AUDIT' AND deleted = FALSE 
    LIMIT 5
  `);

  console.log(`Found ${auditVouchers.length} vouchers in AUDIT`);
  
  if (auditVouchers.length === 0) {
    console.log('❌ No vouchers in AUDIT department for testing');
    process.exit(1);
  }

  console.table(auditVouchers);

  const testVoucher = auditVouchers[0];
  console.log(`\n📋 Testing with voucher: ${testVoucher.voucher_id} (${testVoucher.id})`);

  // Step 2: Test direct database update (what the API should do)
  console.log('\n2. Testing direct database update...');
  
  const updateData = {
    audit_dispatched_by: 'SAMUEL ASIEDU',
    dispatched: 1,
    status: 'VOUCHER CERTIFIED',
    audit_dispatch_time: new Date().toISOString()
  };

  console.log('📤 Update data:', updateData);

  try {
    const [updateResult] = await connection.execute(`
      UPDATE vouchers 
      SET audit_dispatched_by = ?, dispatched = ?, status = ?, audit_dispatch_time = ?
      WHERE id = ?
    `, [
      updateData.audit_dispatched_by,
      updateData.dispatched,
      updateData.status,
      updateData.audit_dispatch_time,
      testVoucher.id
    ]);

    console.log('✅ Direct database update successful');
    console.log('📊 Affected rows:', updateResult.affectedRows);

    // Verify the update
    const [updatedVoucher] = await connection.execute(`
      SELECT id, voucher_id, status, audit_dispatched_by, dispatched, audit_dispatch_time
      FROM vouchers 
      WHERE id = ?
    `, [testVoucher.id]);

    console.log('📋 Updated voucher state:');
    console.table(updatedVoucher);

  } catch (dbError) {
    console.log('❌ Direct database update failed:', dbError.message);
  }

  // Step 3: Check field mapping issues
  console.log('\n3. Checking field mapping issues...');
  
  // These are the fields the frontend might be sending
  const frontendFields = [
    'auditDispatchedBy',
    'dispatched', 
    'status',
    'auditDispatchTime',
    'isReturned',
    'pendingReturn'
  ];

  // These are the corresponding database columns
  const fieldMapping = {
    'auditDispatchedBy': 'audit_dispatched_by',
    'dispatched': 'dispatched',
    'status': 'status',
    'auditDispatchTime': 'audit_dispatch_time',
    'isReturned': 'is_returned',
    'pendingReturn': 'pending_return'
  };

  console.log('📋 Field mapping check:');
  frontendFields.forEach(field => {
    const dbColumn = fieldMapping[field];
    console.log(`   ${field} → ${dbColumn || 'UNMAPPED!'}`);
  });

  // Step 4: Check if all database columns exist
  console.log('\n4. Checking database schema...');
  
  const [columns] = await connection.execute(`
    SELECT COLUMN_NAME 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'vouchers'
    AND COLUMN_NAME IN ('audit_dispatched_by', 'dispatched', 'audit_dispatch_time', 'is_returned', 'pending_return')
  `, [process.env.DB_NAME || 'vms_production']);

  console.log('📊 Required columns in database:');
  const requiredColumns = ['audit_dispatched_by', 'dispatched', 'audit_dispatch_time', 'is_returned', 'pending_return'];
  requiredColumns.forEach(col => {
    const exists = columns.some(c => c.COLUMN_NAME === col);
    console.log(`   ${col}: ${exists ? '✅ EXISTS' : '❌ MISSING'}`);
  });

  // Step 5: Test the exact update that might be failing
  console.log('\n5. Testing problematic update scenario...');
  
  // This simulates what the frontend might be sending
  const problematicUpdate = {
    auditDispatchedBy: 'SAMUEL ASIEDU',
    dispatched: true,
    status: 'VOUCHER CERTIFIED',
    // This might be the problematic field
    someUnknownField: 'test'
  };

  console.log('📤 Problematic update data:', problematicUpdate);

  // Simulate the server-side field mapping logic
  const serverFieldMapping = {
    'auditDispatchedBy': 'audit_dispatched_by',
    'audit_dispatched_by': 'audit_dispatched_by',
    'dispatched': 'dispatched',
    'status': 'status',
    'auditDispatchTime': 'audit_dispatch_time',
    'audit_dispatch_time': 'audit_dispatch_time',
    'isReturned': 'is_returned',
    'is_returned': 'is_returned',
    'pendingReturn': 'pending_return',
    'pending_return': 'pending_return'
  };

  const updates = [];
  const updateParams = [];
  const unknownFields = [];

  for (const [key, value] of Object.entries(problematicUpdate)) {
    if (key === 'id' || key === 'lastUpdated') {
      continue;
    }

    const columnName = serverFieldMapping[key];
    if (columnName) {
      updates.push(`${columnName} = ?`);
      updateParams.push(value);
      console.log(`   ✅ Mapped: ${key} → ${columnName}`);
    } else {
      unknownFields.push(key);
      console.log(`   ❌ Unknown field: ${key}`);
    }
  }

  if (unknownFields.length > 0) {
    console.log(`\n⚠️  Unknown fields detected: ${unknownFields.join(', ')}`);
    console.log('This might cause the "No valid updates provided" error');
  }

  if (updates.length === 0) {
    console.log('\n❌ NO VALID UPDATES - This would cause 400 Bad Request!');
  } else {
    console.log(`\n✅ Valid updates: ${updates.length}`);
    console.log('Query would be:', `UPDATE vouchers SET ${updates.join(', ')} WHERE id = ?`);
  }

} catch (error) {
  console.error('❌ Test execution failed:', error);
} finally {
  await connection.end();
  console.log('\n🔌 Database connection closed');
}
