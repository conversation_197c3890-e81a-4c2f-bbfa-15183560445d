const mysql = require('mysql2/promise');

async function testDuplicatePrevention() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root', 
    password: 'vms@2025@1989',
    database: 'vms_production'
  });
  
  console.log('🧪 TESTING DUPLICATE BATCH PREVENTION');
  console.log('=====================================');
  
  // Clean up any existing duplicate batches first
  console.log('\n1. CLEANING UP EXISTING DUPLICATE BATCHES:');
  console.log('-------------------------------------------');
  
  // Find duplicate batches (same voucher in multiple batches)
  const [duplicateBatches] = await connection.execute(`
    SELECT 
      bv1.voucher_id,
      bv1.batch_id as batch1,
      bv2.batch_id as batch2,
      vb1.sent_time as time1,
      vb2.sent_time as time2
    FROM batch_vouchers bv1
    JOIN batch_vouchers bv2 ON bv1.voucher_id = bv2.voucher_id AND bv1.batch_id != bv2.batch_id
    JOIN voucher_batches vb1 ON bv1.batch_id = vb1.id
    JOIN voucher_batches vb2 ON bv2.batch_id = vb2.id
    WHERE vb1.department = 'FINANCE' 
    AND vb2.department = 'FINANCE'
    AND vb1.from_audit = TRUE 
    AND vb2.from_audit = TRUE
    ORDER BY bv1.voucher_id, vb1.sent_time
  `);
  
  if (duplicateBatches.length > 0) {
    console.log(`📊 Found ${duplicateBatches.length} duplicate batch entries`);
    
    // Group by voucher to see which vouchers have duplicates
    const voucherDuplicates = {};
    duplicateBatches.forEach(dup => {
      if (!voucherDuplicates[dup.voucher_id]) {
        voucherDuplicates[dup.voucher_id] = [];
      }
      voucherDuplicates[dup.voucher_id].push({
        batch: dup.batch1,
        time: dup.time1
      });
      voucherDuplicates[dup.voucher_id].push({
        batch: dup.batch2,
        time: dup.time2
      });
    });
    
    console.log('\n📋 Vouchers with duplicate batches:');
    for (const [voucherId, batches] of Object.entries(voucherDuplicates)) {
      console.log(`   Voucher ${voucherId}:`);
      
      // Remove duplicates and sort by time
      const uniqueBatches = Array.from(new Set(batches.map(b => JSON.stringify(b))))
        .map(b => JSON.parse(b))
        .sort((a, b) => new Date(a.time) - new Date(b.time));
      
      uniqueBatches.forEach((batch, index) => {
        const time = new Date(batch.time).toLocaleString();
        console.log(`     ${index + 1}. Batch ${batch.batch} - ${time}`);
      });
      
      // Keep only the first (oldest) batch, remove the rest
      if (uniqueBatches.length > 1) {
        const batchesToRemove = uniqueBatches.slice(1);
        console.log(`   🗑️ Will remove ${batchesToRemove.length} duplicate batches`);
        
        for (const batchToRemove of batchesToRemove) {
          // Remove voucher from duplicate batch
          await connection.execute('DELETE FROM batch_vouchers WHERE batch_id = ? AND voucher_id = ?', 
            [batchToRemove.batch, voucherId]);
          
          // Check if batch is now empty and remove it
          const [remainingVouchers] = await connection.execute('SELECT COUNT(*) as count FROM batch_vouchers WHERE batch_id = ?', 
            [batchToRemove.batch]);
          
          if (remainingVouchers[0].count === 0) {
            await connection.execute('DELETE FROM voucher_batches WHERE id = ?', [batchToRemove.batch]);
            console.log(`     ✅ Removed empty batch ${batchToRemove.batch}`);
          }
        }
      }
    }
  } else {
    console.log('✅ No duplicate batches found');
  }
  
  console.log('\n2. CURRENT STATE AFTER CLEANUP:');
  console.log('--------------------------------');
  
  // Check current state
  const [currentBatches] = await connection.execute(`
    SELECT 
      vb.id,
      vb.department,
      vb.sent_by,
      vb.sent_time,
      vb.received,
      COUNT(bv.voucher_id) as voucher_count
    FROM voucher_batches vb
    LEFT JOIN batch_vouchers bv ON vb.id = bv.batch_id
    WHERE vb.department = 'FINANCE' 
    AND vb.from_audit = TRUE
    GROUP BY vb.id
    ORDER BY vb.sent_time DESC
    LIMIT 5
  `);
  
  console.log(`📊 Current FINANCE batches from audit: ${currentBatches.length}`);
  currentBatches.forEach((batch, index) => {
    const time = new Date(batch.sent_time).toLocaleString();
    console.log(`   ${index + 1}. Batch ${batch.id} - ${time} - ${batch.voucher_count} vouchers - Received: ${batch.received ? 'YES' : 'NO'}`);
  });
  
  console.log('\n✅ Duplicate batch cleanup completed');
  console.log('💡 The frontend duplicate prevention should now prevent future duplicates');
  
  await connection.end();
}

testDuplicatePrevention().catch(console.error);
