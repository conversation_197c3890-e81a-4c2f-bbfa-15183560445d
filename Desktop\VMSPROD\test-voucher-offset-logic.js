const mysql = require('mysql2/promise');

async function testVoucherOffsetLogic() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root', 
    password: 'vms@2025@1989',
    database: 'vms_production'
  });
  
  console.log('🧪 TESTING VOUCHER OFFSET LOGIC AND TAB FILTERING');
  console.log('==================================================');
  
  // Get all FINANCE vouchers to understand current state
  const [allFinanceVouchers] = await connection.execute(`
    SELECT 
      id,
      voucher_id,
      claimant,
      status,
      department,
      original_department,
      sent_to_audit,
      certified_by,
      audit_dispatched_by,
      department_receipt_time,
      deleted,
      created_at
    FROM vouchers 
    WHERE (department = 'FINANCE' OR original_department = 'FINANCE')
    AND deleted = FALSE
    ORDER BY created_at DESC
  `);
  
  console.log(`📊 Found ${allFinanceVouchers.length} FINANCE vouchers:`);
  console.log('');
  
  // Group vouchers by status for tab analysis
  const vouchersByStatus = {};
  allFinanceVouchers.forEach(voucher => {
    const status = voucher.status;
    if (!vouchersByStatus[status]) {
      vouchersByStatus[status] = [];
    }
    vouchersByStatus[status].push(voucher);
  });
  
  console.log('📋 VOUCHERS BY STATUS:');
  Object.keys(vouchersByStatus).forEach(status => {
    console.log(`   ${status}: ${vouchersByStatus[status].length} vouchers`);
  });
  console.log('');
  
  // Test tab filtering logic (simulating frontend logic)
  console.log('🔍 TESTING TAB FILTERING LOGIC:');
  console.log('-------------------------------');
  
  // PENDING SUBMISSION: department = FINANCE, not sent to audit, status = PENDING
  const pendingSubmission = allFinanceVouchers.filter(v =>
    v.department === 'FINANCE' &&
    !v.sent_to_audit &&
    (v.status === 'PENDING' || v.status === 'PENDING SUBMISSION')
  );
  
  // PROCESSING: originalDepartment = FINANCE, sent to audit, but NOT final statuses
  const processing = allFinanceVouchers.filter(v =>
    v.original_department === 'FINANCE' && 
    v.sent_to_audit === true &&
    v.status !== 'VOUCHER CERTIFIED' &&
    v.status !== 'VOUCHER REJECTED' &&
    v.status !== 'VOUCHER RETURNED'
  );
  
  // CERTIFIED: originalDepartment = FINANCE, status = VOUCHER CERTIFIED
  const certified = allFinanceVouchers.filter(v =>
    v.original_department === 'FINANCE' && 
    v.status === 'VOUCHER CERTIFIED'
  );
  
  // REJECTED: originalDepartment = FINANCE, status = VOUCHER REJECTED
  const rejected = allFinanceVouchers.filter(v =>
    v.original_department === 'FINANCE' && 
    v.status === 'VOUCHER REJECTED'
  );
  
  // RETURNED: originalDepartment = FINANCE, status = VOUCHER RETURNED
  const returned = allFinanceVouchers.filter(v =>
    v.original_department === 'FINANCE' && 
    v.status === 'VOUCHER RETURNED'
  );
  
  console.log(`📑 TAB FILTERING RESULTS:`);
  console.log(`   PENDING SUBMISSION: ${pendingSubmission.length} vouchers`);
  console.log(`   PROCESSING: ${processing.length} vouchers`);
  console.log(`   CERTIFIED: ${certified.length} vouchers`);
  console.log(`   REJECTED: ${rejected.length} vouchers`);
  console.log(`   RETURNED: ${returned.length} vouchers`);
  console.log('');
  
  // Show details for each tab
  if (certified.length > 0) {
    console.log('✅ CERTIFIED TAB VOUCHERS:');
    certified.forEach((voucher, idx) => {
      console.log(`   ${idx + 1}. ${voucher.voucher_id} - ${voucher.claimant}`);
      console.log(`      Status: ${voucher.status}`);
      console.log(`      Certified By: ${voucher.certified_by || 'N/A'}`);
      console.log(`      Audit Dispatched By: ${voucher.audit_dispatched_by || 'N/A'}`);
      console.log(`      Department Receipt: ${voucher.department_receipt_time || 'NOT YET'}`);
      console.log('');
    });
  }
  
  if (processing.length > 0) {
    console.log('🔄 PROCESSING TAB VOUCHERS:');
    processing.forEach((voucher, idx) => {
      console.log(`   ${idx + 1}. ${voucher.voucher_id} - ${voucher.claimant}`);
      console.log(`      Status: ${voucher.status}`);
      console.log(`      Sent to Audit: ${voucher.sent_to_audit}`);
      console.log(`      Department Receipt: ${voucher.department_receipt_time || 'NOT YET'}`);
      console.log('');
    });
  }
  
  // Check for potential offset issues
  console.log('🔍 CHECKING FOR OFFSET ISSUES:');
  console.log('------------------------------');
  
  // Group vouchers by voucher_id to find duplicates
  const voucherGroups = {};
  allFinanceVouchers.forEach(voucher => {
    const voucherId = voucher.voucher_id;
    if (!voucherGroups[voucherId]) {
      voucherGroups[voucherId] = [];
    }
    voucherGroups[voucherId].push(voucher);
  });
  
  // Find voucher IDs with multiple entries
  const duplicateVoucherIds = Object.keys(voucherGroups).filter(voucherId => 
    voucherGroups[voucherId].length > 1
  );
  
  if (duplicateVoucherIds.length > 0) {
    console.log(`⚠️ Found ${duplicateVoucherIds.length} voucher IDs with multiple entries:`);
    duplicateVoucherIds.forEach(voucherId => {
      const vouchers = voucherGroups[voucherId];
      console.log(`   ${voucherId}: ${vouchers.length} entries`);
      vouchers.forEach((voucher, idx) => {
        console.log(`      ${idx + 1}. Status: ${voucher.status}, Dept: ${voucher.department}, Created: ${voucher.created_at}`);
      });
      console.log('');
    });
  } else {
    console.log('✅ No duplicate voucher IDs found - offset logic working correctly');
  }
  
  await connection.end();
}

testVoucherOffsetLogic().catch(console.error);
