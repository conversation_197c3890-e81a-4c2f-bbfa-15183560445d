const mysql = require('mysql2/promise');

async function fixDatabaseSchema() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });

  console.log('🔧 FIXING DATABASE SCHEMA ERRORS...\n');

  try {
    // Check current vouchers table structure
    console.log('📊 Checking current vouchers table structure...');
    const [columns] = await connection.execute(`DESCRIBE vouchers`);
    const existingColumns = columns.map(col => col.Field);
    console.log('Existing columns:', existingColumns.join(', '));

    // Add missing columns to vouchers table
    const missingColumns = [
      { name: 'flags', definition: 'TEXT DEFAULT NULL' },
      { name: 'created_at', definition: 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP' },
      { name: 'received_by_audit', definition: 'BOOLEAN DEFAULT FALSE' },
      { name: 'work_started', definition: 'BOOLEAN DEFAULT FALSE' }
    ];

    for (const column of missingColumns) {
      if (!existingColumns.includes(column.name)) {
        console.log(`➕ Adding missing column: ${column.name}`);
        await connection.execute(`ALTER TABLE vouchers ADD COLUMN ${column.name} ${column.definition}`);
        console.log(`✅ Added column: ${column.name}`);
      } else {
        console.log(`✓ Column ${column.name} already exists`);
      }
    }

    // Check active_sessions table
    console.log('\n📊 Checking active_sessions table structure...');
    try {
      const [sessionColumns] = await connection.execute(`DESCRIBE active_sessions`);
      const existingSessionColumns = sessionColumns.map(col => col.Field);
      console.log('Existing session columns:', existingSessionColumns.join(', '));

      if (!existingSessionColumns.includes('role')) {
        console.log('➕ Adding missing role column to active_sessions');
        await connection.execute(`ALTER TABLE active_sessions ADD COLUMN role VARCHAR(50) DEFAULT 'User'`);
        console.log('✅ Added role column to active_sessions');
      } else {
        console.log('✓ Role column already exists in active_sessions');
      }
    } catch (error) {
      console.log('⚠️ active_sessions table might not exist, skipping...');
    }

    // Check voucher_batches table
    console.log('\n📊 Checking voucher_batches table structure...');
    try {
      const [batchColumns] = await connection.execute(`DESCRIBE voucher_batches`);
      const existingBatchColumns = batchColumns.map(col => col.Field);
      console.log('Existing batch columns:', existingBatchColumns.join(', '));

      if (!existingBatchColumns.includes('voucher_ids')) {
        console.log('➕ Adding missing voucher_ids column to voucher_batches');
        await connection.execute(`ALTER TABLE voucher_batches ADD COLUMN voucher_ids TEXT DEFAULT NULL`);
        console.log('✅ Added voucher_ids column to voucher_batches');
      } else {
        console.log('✓ voucher_ids column already exists in voucher_batches');
      }
    } catch (error) {
      console.log('⚠️ voucher_batches table might not exist, skipping...');
    }

    // Update created_at for existing records that don't have it
    console.log('\n🔄 Updating created_at for existing records...');
    await connection.execute(`
      UPDATE vouchers 
      SET created_at = COALESCE(created_at, NOW()) 
      WHERE created_at IS NULL OR created_at = '0000-00-00 00:00:00'
    `);
    console.log('✅ Updated created_at for existing records');

    console.log('\n🎉 DATABASE SCHEMA FIXES COMPLETED!');

  } catch (error) {
    console.error('❌ Error fixing database schema:', error);
    throw error;
  } finally {
    await connection.end();
  }
}

fixDatabaseSchema().catch(console.error);
