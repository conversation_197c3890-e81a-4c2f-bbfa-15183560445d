{"level":"info","message":"🎉 VMS Server - Production Grade Startup Complete!","service":"vms-server","timestamp":"2025-07-11 21:27:33"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-07-11 21:27:33"}
{"level":"info","message":"🚀 Server Status: RUNNING (Port 8080)","service":"vms-server","timestamp":"2025-07-11 21:27:33"}
{"level":"info","message":"🔌 WebSocket Status: ACTIVE (Enterprise Configuration)","service":"vms-server","timestamp":"2025-07-11 21:27:33"}
{"level":"info","message":"💾 Database Status: CONNECTED","service":"vms-server","timestamp":"2025-07-11 21:27:33"}
{"level":"info","message":"🌐 Environment: PRODUCTION","service":"vms-server","timestamp":"2025-07-11 21:27:33"}
{"level":"info","message":"📊 Process ID: 1516","service":"vms-server","timestamp":"2025-07-11 21:27:33"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-07-11 21:27:33"}
{"level":"info","message":"📡 Service Endpoints:","service":"vms-server","timestamp":"2025-07-11 21:27:33"}
{"level":"info","message":"   🌐 WebSocket: http://localhost:8080/socket.io/","service":"vms-server","timestamp":"2025-07-11 21:27:33"}
{"level":"info","message":"   📊 Health Check: http://localhost:8080/health","service":"vms-server","timestamp":"2025-07-11 21:27:33"}
{"level":"info","message":"   📈 Detailed Health: http://localhost:8080/health/detailed","service":"vms-server","timestamp":"2025-07-11 21:27:33"}
{"level":"info","message":"   🔧 API Base: http://localhost:8080/api","service":"vms-server","timestamp":"2025-07-11 21:27:33"}
{"level":"info","message":"✅ Enterprise tasks DISABLED to prevent infinite loops","service":"vms-server","timestamp":"2025-07-11 21:27:33"}
{"level":"info","message":"✅ Production monitoring DISABLED for stability","service":"vms-server","timestamp":"2025-07-11 21:27:33"}
{"level":"info","message":"🌍 Enterprise Network Access:","service":"vms-server","timestamp":"2025-07-11 21:27:33"}
{"level":"info","message":"   🖥️  Server: http://************:8080","service":"vms-server","timestamp":"2025-07-11 21:27:33"}
{"level":"info","message":"   💻 Client: http://************:3000","service":"vms-server","timestamp":"2025-07-11 21:27:33"}
{"level":"info","message":"   🔌 WebSocket: http://************:8080/socket.io/","service":"vms-server","timestamp":"2025-07-11 21:27:33"}
{"level":"info","message":"   🖥️  Server: http://***************:8080","service":"vms-server","timestamp":"2025-07-11 21:27:33"}
{"level":"info","message":"   💻 Client: http://***************:3000","service":"vms-server","timestamp":"2025-07-11 21:27:33"}
{"level":"info","message":"   🔌 WebSocket: http://***************:8080/socket.io/","service":"vms-server","timestamp":"2025-07-11 21:27:33"}
{"level":"info","message":"📊 System Resources:","service":"vms-server","timestamp":"2025-07-11 21:27:33"}
{"level":"info","message":"   💾 Memory: 60MB RSS","service":"vms-server","timestamp":"2025-07-11 21:27:33"}
{"level":"info","message":"   🔄 Uptime: 2s","service":"vms-server","timestamp":"2025-07-11 21:27:33"}
{"level":"info","message":"🎯 VMS Service Discovery started - Broadcasting on all network interfaces","service":"vms-server","timestamp":"2025-07-11 21:27:33"}
{"level":"info","message":"📊 Service Info: {\n  \"serviceName\": \"VMS-Server\",\n  \"host\": \"************\",\n  \"port\": 8080,\n  \"version\": \"3.0.0\",\n  \"timestamp\": 1752269253289,\n  \"capabilities\": [\n    \"voucher-management\",\n    \"real-time-updates\",\n    \"multi-user\"\n  ]\n}","service":"vms-server","timestamp":"2025-07-11 21:27:33"}
{"level":"info","message":"📡 Service Discovery: ACTIVE - Clients can auto-discover this server","service":"vms-server","timestamp":"2025-07-11 21:27:33"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-07-11 21:27:33"}
{"level":"info","message":"🎯 VMS Server - Ready for Production Operations!","service":"vms-server","timestamp":"2025-07-11 21:27:33"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-07-11 21:27:33"}
{"level":"info","message":"📡 Service discovery broadcast started on port 45454","service":"vms-server","timestamp":"2025-07-11 21:27:33"}
{"level":"info","message":"🔍 Service discovery listener started on port 45455","service":"vms-server","timestamp":"2025-07-11 21:27:33"}
{"level":"info","message":"🚀 Starting VMS Server - Production Grade Architecture...","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"✅ Found available preferred port: 3000","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"🎯 Using dynamic port: 3000","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"✅ Single WebSocket system initialized - No duplicates","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"🎯 Duplicate broadcast prevention: Only socketHandlers active","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"🔄 Initializing database connection with enterprise retry logic...","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"Database connection pool initialized","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"Connected to MySQL database","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"Database 'vms_production' created or already exists","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"Database tables created or already exist","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"Users already exist (3 users found)","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"✅ Database connected successfully - Production ready","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"🔍 Testing database connectivity...","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"📊 Database health monitoring DISABLED to prevent infinite loops","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"🎉 VMS Server - Production Grade Startup Complete!","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"🚀 Server Status: RUNNING (Port 3000)","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"🔌 WebSocket Status: ACTIVE (Enterprise Configuration)","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"💾 Database Status: CONNECTED","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"🌐 Environment: PRODUCTION","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"📊 Process ID: 13812","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"📡 Service Endpoints:","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"   🌐 WebSocket: http://localhost:3000/socket.io/","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"   📊 Health Check: http://localhost:3000/health","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"   📈 Detailed Health: http://localhost:3000/health/detailed","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"   🔧 API Base: http://localhost:3000/api","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"✅ Enterprise tasks DISABLED to prevent infinite loops","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"✅ Production monitoring DISABLED for stability","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"🌍 Enterprise Network Access:","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"   🖥️  Server: http://************:3000","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"   💻 Client: http://************:3000","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"   🔌 WebSocket: http://************:3000/socket.io/","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"   🖥️  Server: http://***************:3000","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"   💻 Client: http://***************:3000","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"   🔌 WebSocket: http://***************:3000/socket.io/","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"📊 System Resources:","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"   💾 Memory: 64MB RSS","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"   🔄 Uptime: 1s","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"🎯 VMS Service Discovery started - Broadcasting on all network interfaces","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"📊 Service Info: {\n  \"serviceName\": \"VMS-Server\",\n  \"host\": \"************\",\n  \"port\": 3000,\n  \"version\": \"3.0.0\",\n  \"timestamp\": 1752269365221,\n  \"capabilities\": [\n    \"voucher-management\",\n    \"real-time-updates\",\n    \"multi-user\"\n  ]\n}","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"📡 Service Discovery: ACTIVE - Clients can auto-discover this server","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"🎯 VMS Server - Ready for Production Operations!","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"📡 Service discovery broadcast started on port 45454","service":"vms-server","timestamp":"2025-07-11 21:29:25"}
{"level":"info","message":"🚀 Starting VMS Server - Production Grade Architecture...","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"✅ Found available preferred port: 3000","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"🎯 Using dynamic port: 3000","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"✅ Single WebSocket system initialized - No duplicates","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"🎯 Duplicate broadcast prevention: Only socketHandlers active","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"🔄 Initializing database connection with enterprise retry logic...","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"Database connection pool initialized","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"Connected to MySQL database","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"Database 'vms_production' created or already exists","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"Database tables created or already exist","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"Users already exist (3 users found)","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"✅ Database connected successfully - Production ready","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"🔍 Testing database connectivity...","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"📊 Database health monitoring DISABLED to prevent infinite loops","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"🎉 VMS Server - Production Grade Startup Complete!","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"🚀 Server Status: RUNNING (Port 3000)","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"🔌 WebSocket Status: ACTIVE (Enterprise Configuration)","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"💾 Database Status: CONNECTED","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"🌐 Environment: PRODUCTION","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"📊 Process ID: 9984","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"📡 Service Endpoints:","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"   🌐 WebSocket: http://localhost:3000/socket.io/","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"   📊 Health Check: http://localhost:3000/health","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"   📈 Detailed Health: http://localhost:3000/health/detailed","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"   🔧 API Base: http://localhost:3000/api","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"✅ Enterprise tasks DISABLED to prevent infinite loops","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"✅ Production monitoring DISABLED for stability","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"🌍 Enterprise Network Access:","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"   🖥️  Server: http://************:3000","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"   💻 Client: http://************:3000","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"   🔌 WebSocket: http://************:3000/socket.io/","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"   🖥️  Server: http://***************:3000","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"   💻 Client: http://***************:3000","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"   🔌 WebSocket: http://***************:3000/socket.io/","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"📊 System Resources:","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"   💾 Memory: 60MB RSS","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"   🔄 Uptime: 1s","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"🎯 VMS Service Discovery started - Broadcasting on all network interfaces","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"📊 Service Info: {\n  \"serviceName\": \"VMS-Server\",\n  \"host\": \"************\",\n  \"port\": 3000,\n  \"version\": \"3.0.0\",\n  \"timestamp\": 1752269479247,\n  \"capabilities\": [\n    \"voucher-management\",\n    \"real-time-updates\",\n    \"multi-user\"\n  ]\n}","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"📡 Service Discovery: ACTIVE - Clients can auto-discover this server","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"🎯 VMS Server - Ready for Production Operations!","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"📡 Service discovery broadcast started on port 45454","service":"vms-server","timestamp":"2025-07-11 21:31:19"}
{"level":"info","message":"🚀 Starting VMS Server - Production Grade Architecture...","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"✅ Found available preferred port: 3001","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"🎯 Using dynamic port: 3001","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"✅ Single WebSocket system initialized - No duplicates","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"🎯 Duplicate broadcast prevention: Only socketHandlers active","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"🔄 Initializing database connection with enterprise retry logic...","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"Connected to MySQL database","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"Database connection pool initialized","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"Database 'vms_production' created or already exists","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"Database tables created or already exist","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"Users already exist (3 users found)","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"✅ Database connected successfully - Production ready","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"🔍 Testing database connectivity...","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"📊 Database health monitoring DISABLED to prevent infinite loops","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"🎉 VMS Server - Production Grade Startup Complete!","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"🚀 Server Status: RUNNING (Port 3001)","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"🔌 WebSocket Status: ACTIVE (Enterprise Configuration)","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"💾 Database Status: CONNECTED","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"🌐 Environment: PRODUCTION","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"📊 Process ID: 17012","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"📡 Service Endpoints:","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"   🌐 WebSocket: http://localhost:3001/socket.io/","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"   📊 Health Check: http://localhost:3001/health","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"   📈 Detailed Health: http://localhost:3001/health/detailed","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"   🔧 API Base: http://localhost:3001/api","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"✅ Enterprise tasks DISABLED to prevent infinite loops","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"✅ Production monitoring DISABLED for stability","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"🌍 Enterprise Network Access:","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"   🖥️  Server: http://************:3001","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"   💻 Client: http://************:3000","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"   🔌 WebSocket: http://************:3001/socket.io/","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"   🖥️  Server: http://***************:3001","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"   💻 Client: http://***************:3000","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"   🔌 WebSocket: http://***************:3001/socket.io/","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"📊 System Resources:","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"   💾 Memory: 64MB RSS","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"   🔄 Uptime: 1s","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"🎯 VMS Service Discovery started - Broadcasting on all network interfaces","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"📊 Service Info: {\n  \"serviceName\": \"VMS-Server\",\n  \"host\": \"************\",\n  \"port\": 3001,\n  \"version\": \"3.0.0\",\n  \"timestamp\": 1752269563989,\n  \"capabilities\": [\n    \"voucher-management\",\n    \"real-time-updates\",\n    \"multi-user\"\n  ]\n}","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"📡 Service Discovery: ACTIVE - Clients can auto-discover this server","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-07-11 21:32:43"}
{"level":"info","message":"🎯 VMS Server - Ready for Production Operations!","service":"vms-server","timestamp":"2025-07-11 21:32:44"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-07-11 21:32:44"}
{"level":"info","message":"📡 Service discovery broadcast started on port 45454","service":"vms-server","timestamp":"2025-07-11 21:32:44"}
{"level":"info","message":"::1 - - [11/Jul/2025:21:35:21 +0000] \"GET /health HTTP/1.1\" 200 287 \"-\" \"curl/8.13.0\"","service":"vms-server","timestamp":"2025-07-11 21:35:21"}
