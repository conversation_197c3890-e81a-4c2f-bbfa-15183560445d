const mysql = require('mysql2/promise');

async function checkVoucherDisplayIssue() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });

  console.log('🔍 DIAGNOSING VOUCHER DISPLAY ISSUE...\n');
  
  try {
    // Check total vouchers
    const [countResult] = await connection.execute('SELECT COUNT(*) as total FROM vouchers WHERE deleted = FALSE');
    console.log(`📊 Total vouchers in database: ${countResult[0].total}`);
    
    // Check recent vouchers
    const [vouchers] = await connection.execute(`
      SELECT id, voucher_id, claimant, department, status, sent_to_audit, created_at, deleted
      FROM vouchers 
      ORDER BY created_at DESC 
      LIMIT 10
    `);
    
    console.log('\n📋 Recent vouchers:');
    vouchers.forEach((v, index) => {
      console.log(`${index + 1}. ID: ${v.id}`);
      console.log(`   Voucher ID: ${v.voucher_id}`);
      console.log(`   Claimant: ${v.claimant}`);
      console.log(`   Department: ${v.department}`);
      console.log(`   Status: ${v.status}`);
      console.log(`   Sent to Audit: ${v.sent_to_audit}`);
      console.log(`   Created: ${v.created_at}`);
      console.log(`   Deleted: ${v.deleted}`);
      console.log('   ---');
    });

    // Check by department
    console.log('\n📊 Vouchers by department:');
    const [deptCount] = await connection.execute(`
      SELECT department, COUNT(*) as count 
      FROM vouchers 
      WHERE deleted = FALSE 
      GROUP BY department
    `);
    
    deptCount.forEach(dept => {
      console.log(`   ${dept.department}: ${dept.count} vouchers`);
    });

    // Check by status
    console.log('\n📊 Vouchers by status:');
    const [statusCount] = await connection.execute(`
      SELECT status, COUNT(*) as count 
      FROM vouchers 
      WHERE deleted = FALSE 
      GROUP BY status
    `);
    
    statusCount.forEach(status => {
      console.log(`   ${status.status}: ${status.count} vouchers`);
    });

    // Check the latest voucher (ID 14)
    console.log('\n🔍 Latest voucher details:');
    const [latestVoucher] = await connection.execute(`
      SELECT * FROM vouchers 
      ORDER BY created_at DESC 
      LIMIT 1
    `);
    
    if (latestVoucher.length > 0) {
      const v = latestVoucher[0];
      console.log('Latest voucher:');
      Object.keys(v).forEach(key => {
        console.log(`   ${key}: ${v[key]}`);
      });
    }

  } catch (error) {
    console.error('❌ Error checking vouchers:', error);
  } finally {
    await connection.end();
  }
}

checkVoucherDisplayIssue().catch(console.error);
