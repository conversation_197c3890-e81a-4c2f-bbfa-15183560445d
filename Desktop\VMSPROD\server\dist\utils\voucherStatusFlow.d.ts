/**
 * This file defines the standard voucher status flow and transitions
 * to ensure consistency across the application.
 */
import { TransactionStatus } from '../types.js';
export declare const VOUCHER_STATUSES: {
    readonly PENDING: TransactionStatus;
    readonly PENDING_SUBMISSION: TransactionStatus;
    readonly PENDING_RECEIPT: TransactionStatus;
    readonly VOUCHER_PROCESSING: TransactionStatus;
    readonly AUDIT_PROCESSING: TransactionStatus;
    readonly VOUCHER_CERTIFIED: TransactionStatus;
    readonly VOUCHER_REJECTED: TransactionStatus;
    readonly REJECTED_PENDING_DISPATCH: TransactionStatus;
    readonly VOUCHER_RETURNED: TransactionStatus;
    readonly VOUCHER_PENDING_RETURN: TransactionStatus;
    readonly OFFSET_BY_AUDIT: TransactionStatus;
};
export interface VoucherFlags {
    sentToAudit: boolean;
    dispatched: boolean;
    certified: boolean;
    rejected: boolean;
    isReturned: boolean;
    pendingReturn: boolean;
    receivedByAudit: boolean;
}
export declare const VALID_STATUS_TRANSITIONS: {
    readonly [VOUCHER_STATUSES.PENDING]: {
        readonly allowedTransitions: readonly [TransactionStatus];
        readonly requiredFlags: Partial<VoucherFlags>;
        readonly clearFlags: readonly [];
        readonly requiredRole: readonly ["admin", "USER"];
    };
    readonly [VOUCHER_STATUSES.PENDING_SUBMISSION]: {
        readonly allowedTransitions: readonly [TransactionStatus];
        readonly requiredFlags: Partial<VoucherFlags>;
        readonly clearFlags: readonly [];
        readonly requiredRole: readonly ["admin", "USER"];
    };
    readonly [VOUCHER_STATUSES.PENDING_RECEIPT]: {
        readonly allowedTransitions: readonly [TransactionStatus];
        readonly requiredFlags: Partial<VoucherFlags>;
        readonly clearFlags: readonly [];
        readonly requiredRole: readonly ["AUDIT", "SYSTEM ADMIN"];
    };
    readonly [VOUCHER_STATUSES.AUDIT_PROCESSING]: {
        readonly allowedTransitions: readonly [TransactionStatus, TransactionStatus, TransactionStatus, TransactionStatus, TransactionStatus];
        readonly requiredFlags: Partial<VoucherFlags>;
        readonly clearFlags: readonly [];
        readonly requiredRole: readonly ["AUDIT", "SYSTEM ADMIN"];
    };
    readonly [VOUCHER_STATUSES.VOUCHER_PENDING_RETURN]: {
        readonly allowedTransitions: readonly [TransactionStatus];
        readonly requiredFlags: Partial<VoucherFlags>;
        readonly clearFlags: readonly ["pendingReturn"];
        readonly requiredRole: readonly ["AUDIT", "SYSTEM ADMIN"];
    };
    readonly [VOUCHER_STATUSES.VOUCHER_CERTIFIED]: {
        readonly allowedTransitions: readonly [TransactionStatus];
        readonly requiredFlags: Partial<VoucherFlags>;
        readonly clearFlags: readonly [];
        readonly requiredRole: readonly ["admin", "USER"];
    };
    readonly [VOUCHER_STATUSES.VOUCHER_REJECTED]: {
        readonly allowedTransitions: readonly [TransactionStatus];
        readonly requiredFlags: Partial<VoucherFlags>;
        readonly clearFlags: readonly [];
        readonly requiredRole: readonly ["admin", "USER"];
    };
    readonly [VOUCHER_STATUSES.REJECTED_PENDING_DISPATCH]: {
        readonly allowedTransitions: readonly [TransactionStatus];
        readonly requiredFlags: Partial<VoucherFlags>;
        readonly clearFlags: readonly [];
        readonly requiredRole: readonly ["AUDIT", "SYSTEM ADMIN"];
    };
    readonly [VOUCHER_STATUSES.VOUCHER_RETURNED]: {
        readonly allowedTransitions: readonly [TransactionStatus];
        readonly requiredFlags: Partial<VoucherFlags>;
        readonly clearFlags: readonly [];
        readonly requiredRole: readonly ["admin", "USER"];
    };
    readonly [VOUCHER_STATUSES.VOUCHER_PROCESSING]: {
        readonly allowedTransitions: readonly [TransactionStatus];
        readonly requiredFlags: Partial<VoucherFlags>;
        readonly clearFlags: readonly ["sentToAudit", "dispatched", "certified", "rejected", "isReturned", "pendingReturn", "receivedByAudit"];
        readonly requiredRole: readonly ["admin", "USER"];
    };
};
export declare function isValidStatusTransition(currentStatus: TransactionStatus, newStatus: TransactionStatus, userRole: string, flags: VoucherFlags, userDepartment?: string): {
    isValid: boolean;
    reason?: string;
};
export declare function getFlagsForStatus(status: TransactionStatus): VoucherFlags;
export declare function synchronizeVoucherFlags(voucher: {
    status: TransactionStatus;
    flags?: Partial<VoucherFlags>;
}): {
    status: TransactionStatus;
    flags: VoucherFlags;
};
export declare function getNextAllowedStatuses(currentStatus: TransactionStatus, userRole: string, flags: VoucherFlags): TransactionStatus[];
export declare function logStatusTransition(voucherId: string, fromStatus: TransactionStatus, toStatus: TransactionStatus, userId: string, reason?: string): void;
