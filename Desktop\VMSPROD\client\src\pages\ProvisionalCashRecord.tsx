import { useEffect, useState } from 'react';
import { formatNumberWithCommas, formatCurrentDate, formatDate } from '@/utils/formatUtils';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Search, Plus } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useAppStore } from '@/lib/store';
import { NotificationsMenu } from '@/components/notifications';
import { UserNav } from '@/components/user-nav';
import { ModeToggle } from '@/components/mode-toggle';
import { ExitButton } from '@/components/exit-button';
import { AuditNavigation } from '@/components/audit-dashboard/audit-navigation';
import { DashboardFooter } from '@/components/dashboard/dashboard-footer';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { toast } from '@/hooks/use-toast';
import { ProvisionalCashRecord, Voucher, Department } from '@/lib/types';
import { calculateClearanceRemark } from '@/lib/data';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export default function ProvisionalCashRecordPage() {
  const navigate = useNavigate();
  const currentUser = useAppStore((state) => state.currentUser);
  const vouchers = useAppStore((state) => state.vouchers);
  const provisionalCashRecords = useAppStore((state) => state.provisionalCashRecords);
  const addProvisionalCashRecord = useAppStore((state) => state.addProvisionalCashRecord);
  const updateProvisionalCashRecord = useAppStore((state) => state.updateProvisionalCashRecord);

  const [selectedVoucherId, setSelectedVoucherId] = useState('');
  const [amountRetired, setAmountRetired] = useState('');
  const [comment, setComment] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddForm, setShowAddForm] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [currentRecordId, setCurrentRecordId] = useState('');
  const [selectedDepartment, setSelectedDepartment] = useState<Department>('FINANCE');

  const certifiedVouchers = vouchers.filter(v =>
    v.status === 'VOUCHER CERTIFIED' &&
    !provisionalCashRecords.some(pcr => pcr.voucherId === v.id)
  );

  // Filter records by department and search term
  const filteredRecords = provisionalCashRecords.filter(record => {
    // Find the associated voucher to get department information
    const associatedVoucher = vouchers.find(v => v.id === record.voucherId);
    const voucherDepartment = associatedVoucher?.originalDepartment || associatedVoucher?.department;

    // Filter by department
    const matchesDepartment = voucherDepartment === selectedDepartment;

    // Filter by search term
    const matchesSearch = searchTerm === '' ||
      record.voucherRef.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.claimant.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.description.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesDepartment && matchesSearch;
  });

  useEffect(() => {
    if (!currentUser) {
      navigate('/');
      return;
    }

    if (currentUser.department !== 'AUDIT') {
      navigate('/dashboard');
    }
  }, [currentUser, navigate]);

  // PRODUCTION FIX: Load provisional cash records from API
  useEffect(() => {
    const loadProvisionalCashRecords = async () => {
      try {
        console.log('🔄 PROVISIONAL CASH: Loading records from API...');
        const response = await fetch('/api/provisional-cash', {
          credentials: 'include'
        });

        if (response.ok) {
          const records = await response.json();
          console.log(`✅ PROVISIONAL CASH: Loaded ${records.length} records from API`);

          // Clear local store and load fresh data
          useAppStore.setState({ provisionalCashRecords: records });
        } else {
          console.error('❌ PROVISIONAL CASH: Failed to load records from API');
        }
      } catch (error) {
        console.error('❌ PROVISIONAL CASH: API error:', error);
      }
    };

    if (currentUser?.department === 'AUDIT') {
      loadProvisionalCashRecords();
    }
  }, [currentUser]);

  const getClearanceClass = (remark?: string) => {
    if (!remark) return "";

    switch (remark) {
      case "CLEARED":
        return "text-emerald-600 dark:text-emerald-400";
      case "REFUNDED TO CHEST":
        return "text-blue-600 dark:text-blue-400";
      case "DUE STAFF":
        return "text-amber-600 dark:text-amber-400";
      default:
        return "";
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedVoucherId || !amountRetired) {
      toast({
        title: 'Required fields missing',
        description: 'Please select a voucher and enter amount retired',
        variant: 'destructive',
      });
      return;
    }

    if (isNaN(Number(amountRetired)) || Number(amountRetired) <= 0) {
      toast({
        title: 'Invalid amount',
        description: 'Amount retired must be a positive number',
        variant: 'destructive',
      });
      return;
    }

    const selectedVoucher = vouchers.find(v => v.id === selectedVoucherId);

    if (!selectedVoucher) {
      toast({
        title: 'Voucher not found',
        description: 'The selected voucher could not be found',
        variant: 'destructive',
      });
      return;
    }

    const amountRetiredValue = parseFloat(amountRetired);
    const { remark } = calculateClearanceRemark(selectedVoucher.amount, amountRetiredValue);

    // Use formatCurrentDate to ensure consistent date format with NEW VOUCHERS tab
    const currentDate = formatCurrentDate();

    if (editMode && currentRecordId) {
      updateProvisionalCashRecord(currentRecordId, {
        amountRetired: amountRetiredValue,
        clearanceRemark: remark,
        dateRetired: formatCurrentDate(),
        clearedBy: currentUser?.name,
        comment
      });

      toast({
        title: 'Record Updated',
        description: `Cash record for voucher ${selectedVoucher.voucherId} has been updated`,
      });
    } else {
      try {
        await addProvisionalCashRecord({
          voucherId: selectedVoucher.id,
          voucherRef: selectedVoucher.voucherId,
          claimant: selectedVoucher.claimant,
          description: selectedVoucher.description,
          mainAmount: selectedVoucher.amount,
          currency: selectedVoucher.currency,
          amountRetired: amountRetiredValue,
          clearanceRemark: remark,
          dateRetired: formatCurrentDate(),
          clearedBy: currentUser?.name,
          comment,
          date: currentDate
        });

        toast({
          title: 'Record Created',
          description: `Cash record for voucher ${selectedVoucher.voucherId} has been created`,
        });
      } catch (error) {
        console.error('Failed to create provisional cash record:', error);
        toast({
          title: 'Error',
          description: 'Failed to create provisional cash record. Please try again.',
          variant: 'destructive',
        });
        return;
      }
    }

    setSelectedVoucherId('');
    setAmountRetired('');
    setComment('');
    setShowAddForm(false);
    setEditMode(false);
    setCurrentRecordId('');
  };

  const handleEditRecord = (record: ProvisionalCashRecord) => {
    const voucher = vouchers.find(v => v.id === record.voucherId);

    if (!voucher) {
      toast({
        title: 'Error',
        description: 'Could not find the associated voucher',
        variant: 'destructive',
      });
      return;
    }

    setSelectedVoucherId(voucher.id);
    setAmountRetired(record.amountRetired?.toString() || '');
    setComment(record.comment || '');
    setEditMode(true);
    setCurrentRecordId(record.id);
    setShowAddForm(true);
  };

  const handleBack = () => {
    navigate('/audit-dashboard');
  };

  if (!currentUser) {
    return null;
  }

  const renderTableBody = () => {
    if (filteredRecords.length === 0) {
      return (
        <TableRow>
          <TableCell colSpan={7} className="h-24 text-center">
            No records found.
          </TableCell>
        </TableRow>
      );
    }

    return filteredRecords.map((record) => (
      <TableRow key={record.id}>
        <TableCell className="font-medium">{record.voucherRef}</TableCell>
        <TableCell>{record.claimant}</TableCell>
        <TableCell className="hidden md:table-cell max-w-xs truncate">
          {record.description}
        </TableCell>
        <TableCell>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <span className="block truncate">
                  {formatNumberWithCommas(record.mainAmount)} {record.currency}
                </span>
              </TooltipTrigger>
              <TooltipContent>
                <p>{formatNumberWithCommas(record.mainAmount)} {record.currency}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </TableCell>
        <TableCell>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <span className="block truncate">
                  {record.amountRetired ? `${formatNumberWithCommas(record.amountRetired)} ${record.currency}` : '—'}
                </span>
              </TooltipTrigger>
              <TooltipContent>
                <p>{record.amountRetired ? `${formatNumberWithCommas(record.amountRetired)} ${record.currency}` : '—'}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </TableCell>
        <TableCell>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <span className="block truncate">
                  {record.clearanceRemark ? (
                    <span className={getClearanceClass(record.clearanceRemark)}>
                      {record.clearanceRemark}
                      {record.clearanceRemark !== "CLEARED" && record.amountRetired !== undefined ? (
                        ` (${formatNumberWithCommas(Math.abs(record.mainAmount - record.amountRetired))} ${record.currency})`
                      ) : ''}
                    </span>
                  ) : '—'}
                </span>
              </TooltipTrigger>
              <TooltipContent>
                <p>
                  {record.clearanceRemark ? (
                    <span className={getClearanceClass(record.clearanceRemark)}>
                      {record.clearanceRemark}
                      {record.clearanceRemark !== "CLEARED" && record.amountRetired !== undefined ? (
                        ` (${formatNumberWithCommas(Math.abs(record.mainAmount - record.amountRetired))} ${record.currency})`
                      ) : ''}
                    </span>
                  ) : '—'}
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </TableCell>
        <TableCell className="text-right">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEditRecord(record)}
          >
            Edit
          </Button>
        </TableCell>
      </TableRow>
    ));
  };

  return (
    <div className="flex flex-col min-h-screen">
      <header className="border-b">
        <div className="container flex h-16 items-center px-4 sm:px-6">
          <Button variant="outline" size="icon" onClick={handleBack} className="border-primary hover:bg-primary/10">
            <ArrowLeft className="h-5 w-5 text-primary" />
          </Button>
          <h1 className="text-lg font-semibold ml-2">
            Provisional Cash Records
          </h1>
          <div className="ml-auto flex items-center space-x-4">
            <NotificationsMenu />
            <ModeToggle />
            <UserNav />
            <ExitButton />
          </div>
        </div>
      </header>

      <main className="flex-1 py-6 px-4 sm:px-6 flex flex-col">
        <AuditNavigation />
        <div className="container space-y-6 flex flex-col h-full">
          {/* Department Tabs */}
          <Tabs value={selectedDepartment} onValueChange={(value) => setSelectedDepartment(value as Department)} className="flex flex-col h-full">
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="FINANCE">FINANCE</TabsTrigger>
              <TabsTrigger value="MINISTRIES">MINISTRIES</TabsTrigger>
              <TabsTrigger value="PENSIONS">PENSIONS</TabsTrigger>
              <TabsTrigger value="PENTMEDIA">PENTMEDIA</TabsTrigger>
              <TabsTrigger value="MISSIONS">MISSIONS</TabsTrigger>
              <TabsTrigger value="PENTSOS">PENTSOS</TabsTrigger>
            </TabsList>

            {/* Search and controls */}
            <div className="flex flex-col sm:flex-row gap-4 justify-between items-center shrink-0">
              <div className="relative w-full sm:w-auto">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder={`Search ${selectedDepartment} records...`}
                  className="pl-8 w-full sm:w-[300px]"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              {/* Add Cash Record button removed */}
            </div>

          {/* METHOD 3 DEACTIVATED: Manual provisional cash creation disabled */}
          {false && showAddForm && (
            <Card className="shrink-0">
              <CardHeader>
                <CardTitle>{editMode ? 'Edit Cash Record' : 'Add New Cash Record'}</CardTitle>
                <CardDescription>
                  {editMode
                    ? 'Update the retirement details for this voucher'
                    : 'Create a new provisional cash record for a certified voucher'}
                </CardDescription>
              </CardHeader>
              <form onSubmit={handleSubmit}>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="voucher">Select Voucher</Label>
                    <Select
                      value={selectedVoucherId}
                      onValueChange={setSelectedVoucherId}
                      disabled={editMode}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select a certified voucher" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="" className="uppercase">SELECT VOUCHER</SelectItem>
                        {certifiedVouchers.map((voucher) => (
                          <SelectItem key={voucher.id} value={voucher.id}>
                            {voucher.voucherId} - {voucher.claimant} (Amount: {voucher.amount.toFixed(2)} {voucher.currency})
                          </SelectItem>
                        ))}
                        {certifiedVouchers.length === 0 && (
                          <SelectItem value="none" disabled>
                            No eligible vouchers available
                          </SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                  </div>

                  {selectedVoucherId && (
                    <>
                      <div className="space-y-2">
                        <Label htmlFor="amount-retired">Amount Retired</Label>
                        <Input
                          id="amount-retired"
                          type="number"
                          placeholder="Enter amount retired"
                          value={amountRetired}
                          onChange={(e) => setAmountRetired(e.target.value)}
                          step="0.01"
                          min="0"
                          required
                        />
                        {selectedVoucherId && amountRetired && !isNaN(Number(amountRetired)) && (
                          <div className="text-sm mt-2">
                            <span className="text-muted-foreground">Clearance status: </span>
                            <span className={
                              getClearanceClass(
                                calculateClearanceRemark(
                                  vouchers.find(v => v.id === selectedVoucherId)?.amount || 0,
                                  Number(amountRetired)
                                ).remark
                              )
                            }>
                              {calculateClearanceRemark(
                                vouchers.find(v => v.id === selectedVoucherId)?.amount || 0,
                                Number(amountRetired)
                              ).remark}
                              {calculateClearanceRemark(
                                vouchers.find(v => v.id === selectedVoucherId)?.amount || 0,
                                Number(amountRetired)
                              ).difference > 0 ? (
                                ` (${calculateClearanceRemark(
                                  vouchers.find(v => v.id === selectedVoucherId)?.amount || 0,
                                  Number(amountRetired)
                                ).difference.toFixed(2)} ${vouchers.find(v => v.id === selectedVoucherId)?.currency || 'GHS'})`
                              ) : ''}
                            </span>
                          </div>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="comment">Comment</Label>
                        <Textarea
                          id="comment"
                          placeholder="Enter optional comment"
                          value={comment}
                          onChange={(e) => setComment(e.target.value)}
                        />
                      </div>
                    </>
                  )}
                </CardContent>
                <CardFooter>
                  <Button type="submit" disabled={!selectedVoucherId || !amountRetired || isNaN(Number(amountRetired))}>
                    {editMode ? 'Update Record' : 'Create Record'}
                  </Button>
                </CardFooter>
              </form>
            </Card>
          )}

            {/* Tab Content for each department */}
            {(['FINANCE', 'MINISTRIES', 'PENSIONS', 'PENTMEDIA', 'MISSIONS', 'PENTSOS'] as Department[]).map((dept) => (
              <TabsContent key={dept} value={dept} className="flex-1 flex flex-col min-h-0">
                <Card className="flex-1 flex flex-col min-h-0">
                  <CardHeader className="shrink-0">
                    <CardTitle>{dept} PROVISIONAL CASH RECORDS</CardTitle>
                    <CardDescription>
                      Voucher retirement records for {dept} department
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="p-0 flex-1">
                    <Table maxHeight="calc(100vh - 350px)" minWidth="1500px">
                      <TableHeader>
                        <TableRow>
                            <TableHead>VOUCHER ID</TableHead>
                            <TableHead>CLAIMANT</TableHead>
                            <TableHead className="hidden md:table-cell">DESCRIPTION</TableHead>
                            <TableHead>MAIN AMOUNT</TableHead>
                            <TableHead>RETIRED AMOUNT</TableHead>
                            <TableHead>STATUS</TableHead>
                            <TableHead className="text-right">ACTIONS</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {renderTableBody()}
                        </TableBody>
                      </Table>
                  </CardContent>
                </Card>
              </TabsContent>
            ))}
          </Tabs>
        </div>
      </main>

      <DashboardFooter />
    </div>
  );
}
