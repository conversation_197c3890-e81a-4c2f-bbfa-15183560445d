const mysql = require('mysql2/promise');

/**
 * Clean database - Remove all vouchers, batches, and test data for fresh start
 */
async function cleanDatabase() {
  console.log('🧹 CLEANING DATABASE FOR FRESH START...\n');

  let connection;
  
  try {
    // Create database connection
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'vms@2025@1989',
      database: 'vms_production'
    });

    console.log('✅ Connected to database');

    // First, check what tables exist
    console.log('\n🔍 Checking existing tables...');
    const [tables] = await connection.execute('SHOW TABLES');
    const tableNames = tables.map(row => Object.values(row)[0]);
    console.log('📋 Existing tables:', tableNames.join(', '));

    // Step 1: Clean vouchers table
    if (tableNames.includes('vouchers')) {
      console.log('\n1. Cleaning vouchers table...');
      const [vouchersResult] = await connection.execute('DELETE FROM vouchers');
      console.log(`✅ Deleted ${vouchersResult.affectedRows} vouchers`);
    } else {
      console.log('\n1. ℹ️  vouchers table does not exist (skipping)');
    }

    // Step 2: Clean voucher_batches table
    if (tableNames.includes('voucher_batches')) {
      console.log('\n2. Cleaning voucher_batches table...');
      const [batchesResult] = await connection.execute('DELETE FROM voucher_batches');
      console.log(`✅ Deleted ${batchesResult.affectedRows} voucher batches`);
    } else {
      console.log('\n2. ℹ️  voucher_batches table does not exist (skipping)');
    }

    // Step 3: Clean batch_vouchers table
    if (tableNames.includes('batch_vouchers')) {
      console.log('\n3. Cleaning batch_vouchers table...');
      const [batchVouchersResult] = await connection.execute('DELETE FROM batch_vouchers');
      console.log(`✅ Deleted ${batchVouchersResult.affectedRows} batch_voucher relationships`);
    } else {
      console.log('\n3. ℹ️  batch_vouchers table does not exist (skipping)');
    }

    // Step 4: Clean notifications table
    if (tableNames.includes('notifications')) {
      console.log('\n4. Cleaning notifications table...');
      const [notificationsResult] = await connection.execute('DELETE FROM notifications');
      console.log(`✅ Deleted ${notificationsResult.affectedRows} notifications`);
    } else {
      console.log('\n4. ℹ️  notifications table does not exist (skipping)');
    }

    // Step 5: Clean active_sessions table
    if (tableNames.includes('active_sessions')) {
      console.log('\n5. Cleaning active_sessions table...');
      const [sessionsResult] = await connection.execute('DELETE FROM active_sessions');
      console.log(`✅ Deleted ${sessionsResult.affectedRows} active sessions`);
    } else {
      console.log('\n5. ℹ️  active_sessions table does not exist (skipping)');
    }

    // Step 6: Clean user_sessions table
    if (tableNames.includes('user_sessions')) {
      console.log('\n6. Cleaning user_sessions table...');
      const [userSessionsResult] = await connection.execute('DELETE FROM user_sessions');
      console.log(`✅ Deleted ${userSessionsResult.affectedRows} user sessions`);
    } else {
      console.log('\n6. ℹ️  user_sessions table does not exist (skipping)');
    }

    // Step 7: Reset AUTO_INCREMENT counters
    console.log('\n7. Resetting AUTO_INCREMENT counters...');
    if (tableNames.includes('vouchers')) {
      await connection.execute('ALTER TABLE vouchers AUTO_INCREMENT = 1');
    }
    if (tableNames.includes('voucher_batches')) {
      await connection.execute('ALTER TABLE voucher_batches AUTO_INCREMENT = 1');
    }
    if (tableNames.includes('notifications')) {
      await connection.execute('ALTER TABLE notifications AUTO_INCREMENT = 1');
    }
    console.log('✅ Reset AUTO_INCREMENT counters for existing tables');

    // Step 8: Show current database state
    console.log('\n8. Verifying clean database state...');

    const voucherCount = tableNames.includes('vouchers') ?
      (await connection.execute('SELECT COUNT(*) as count FROM vouchers'))[0][0].count : 0;
    const batchCount = tableNames.includes('voucher_batches') ?
      (await connection.execute('SELECT COUNT(*) as count FROM voucher_batches'))[0][0].count : 0;
    const userCount = tableNames.includes('users') ?
      (await connection.execute('SELECT COUNT(*) as count FROM users'))[0][0].count : 0;
    const notificationCount = tableNames.includes('notifications') ?
      (await connection.execute('SELECT COUNT(*) as count FROM notifications'))[0][0].count : 0;

    console.log('📊 Current database state:');
    console.log(`   📄 Vouchers: ${voucherCount}`);
    console.log(`   📦 Batches: ${batchCount}`);
    console.log(`   👥 Users: ${userCount}`);
    console.log(`   🔔 Notifications: ${notificationCount}`);

    // Step 9: Show remaining users
    if (tableNames.includes('users')) {
      console.log('\n9. Current users in system:');
      const [users] = await connection.execute('SELECT name, department, role FROM users ORDER BY department, name');
      users.forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.name} (${user.department}) - ${user.role}`);
      });
    } else {
      console.log('\n9. ℹ️  users table does not exist');
    }

    console.log('\n🎉 DATABASE CLEANING COMPLETED!');
    console.log('✅ All vouchers, batches, and test data removed');
    console.log('✅ System ready for fresh testing');

    return { success: true };

  } catch (error) {
    console.error('\n❌ Database cleaning failed:', error.message);
    return { success: false, error: error.message };
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the cleaning
cleanDatabase().then(result => {
  if (result.success) {
    console.log('\n🚀 READY FOR FRESH TESTING!');
    console.log('The database is now clean and ready for production testing.');
  } else {
    console.log('\n❌ CLEANING FAILED!');
    console.log('❌ Issue:', result.error);
  }
});
