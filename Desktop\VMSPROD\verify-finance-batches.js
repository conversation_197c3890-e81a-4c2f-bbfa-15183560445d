const mysql = require('mysql2/promise');

async function verifyFinanceBatches() {
  console.log('🔍 Verifying Finance Dashboard Batches');
  console.log('='.repeat(50));

  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'vms@2025@1989',
      database: 'vms_production'
    });
    
    console.log('✅ Connected to database');

    // Check what Finance dashboard should show
    console.log('\n1. BATCHES THAT FINANCE DASHBOARD SHOULD SHOW:');
    console.log('-'.repeat(50));
    
    const [financeBatches] = await connection.execute(`
      SELECT 
        id,
        department,
        sent_by,
        sent_time,
        from_audit,
        received
      FROM voucher_batches 
      WHERE department = 'FINANCE' 
        AND from_audit = 1 
        AND received = 0
      ORDER BY sent_time DESC
    `);

    if (financeBatches.length === 0) {
      console.log('❌ No batches found for Finance dashboard');
      console.log('   This means Finance users will not see any "NEWLY ARRIVED VOUCHERS" section');
    } else {
      console.log(`✅ Found ${financeBatches.length} batches that should appear in Finance dashboard:`);
      financeBatches.forEach((batch, index) => {
        console.log(`   ${index + 1}. Batch ID: ${batch.id.substring(0, 8)}...`);
        console.log(`      Sent By: ${batch.sent_by} (from AUDIT)`);
        console.log(`      Sent Time: ${batch.sent_time}`);
        console.log(`      Status: UNRECEIVED`);
        console.log('      ---');
      });
    }

    // Check vouchers in these batches
    console.log('\n2. VOUCHERS IN FINANCE BATCHES:');
    console.log('-'.repeat(50));
    
    for (const batch of financeBatches.slice(0, 2)) { // Check first 2 batches
      const [vouchers] = await connection.execute(`
        SELECT v.voucher_id, v.status, v.claimant, v.amount
        FROM vouchers v
        JOIN batch_vouchers bv ON v.id = bv.voucher_id
        WHERE bv.batch_id = ?
      `, [batch.id]);

      console.log(`📋 Batch ${batch.id.substring(0, 8)}... contains ${vouchers.length} vouchers:`);
      vouchers.forEach((voucher, index) => {
        console.log(`   ${index + 1}. ${voucher.voucher_id} - ${voucher.claimant} (${voucher.amount})`);
        console.log(`      Status: ${voucher.status}`);
      });
      console.log('   ---');
    }

    // Instructions for user
    console.log('\n3. WHAT YOU SHOULD SEE IN FINANCE DASHBOARD:');
    console.log('-'.repeat(50));
    console.log('🎯 Expected in Finance Dashboard:');
    console.log('   1. Login as Finance user (FINANCE / FELIX AYISI / 123)');
    console.log('   2. You should see a prominent section:');
    console.log('      "📥 NEWLY ARRIVED VOUCHERS FROM AUDIT"');
    console.log(`   3. This section should show ${financeBatches.length} batch(es) to receive`);
    console.log('   4. Each batch should have a "RECEIVE VOUCHERS" button');
    console.log('');
    console.log('🔧 If you don\'t see this:');
    console.log('   1. Hard refresh browser (Ctrl + F5)');
    console.log('   2. Clear browser cache');
    console.log('   3. Try incognito/private window');
    console.log('   4. Check browser console for errors');

    console.log('\n4. TROUBLESHOOTING:');
    console.log('-'.repeat(50));
    console.log('🔍 If batches still don\'t appear:');
    console.log('   1. Check browser developer tools (F12)');
    console.log('   2. Look for JavaScript errors in console');
    console.log('   3. Check Network tab for failed API calls');
    console.log('   4. Verify you\'re logged in as Finance user');

  } catch (error) {
    console.error('❌ Verification failed:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n✅ Database connection closed');
    }
  }
}

verifyFinanceBatches();
