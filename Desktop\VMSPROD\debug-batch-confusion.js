const mysql = require('mysql2/promise');

async function debugBatchConfusion() {
  console.log('🔍 DEBUGGING BATCH CONFUSION ISSUE');
  console.log('='.repeat(50));

  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'vms@2025@1989',
      database: 'vms_production'
    });
    
    console.log('✅ Connected to database');

    // Check all current batches with detailed analysis
    console.log('\n1. ALL CURRENT BATCHES ANALYSIS:');
    console.log('-'.repeat(50));
    
    const [allBatches] = await connection.execute(`
      SELECT 
        id,
        department,
        sent_by,
        sent_time,
        received,
        from_audit,
        CASE 
          WHEN from_audit = 1 THEN 'AUDIT → DEPARTMENT'
          ELSE 'DEPARTMENT → AUDIT'
        END as flow_direction,
        CASE 
          WHEN from_audit = 1 AND received = 0 THEN 'SHOULD APPEAR IN DEPARTMENT DASHBOARD'
          WHEN from_audit = 0 AND received = 0 THEN 'SHOULD APPEAR IN AUDIT DASHBOARD'
          ELSE 'ALREADY RECEIVED'
        END as expected_location
      FROM voucher_batches 
      ORDER BY sent_time DESC
    `);

    console.log(`📦 Total batches in system: ${allBatches.length}`);
    console.log('\nBATCH BREAKDOWN:');
    allBatches.forEach((batch, index) => {
      console.log(`   ${index + 1}. ${batch.id.substring(0, 8)}...`);
      console.log(`      Department: ${batch.department}`);
      console.log(`      Sent By: ${batch.sent_by}`);
      console.log(`      Flow: ${batch.flow_direction}`);
      console.log(`      Received: ${batch.received ? 'YES' : 'NO'}`);
      console.log(`      Expected Location: ${batch.expected_location}`);
      console.log('      ---');
    });

    // Analyze what each dashboard should show
    console.log('\n2. DASHBOARD FILTERING ANALYSIS:');
    console.log('-'.repeat(50));
    
    // What AUDIT dashboard should show (batches FROM departments TO audit)
    const auditShouldShow = allBatches.filter(b => !b.from_audit && !b.received);
    console.log(`🏛️  AUDIT DASHBOARD should show: ${auditShouldShow.length} batches`);
    auditShouldShow.forEach((batch, index) => {
      console.log(`   ${index + 1}. FROM ${batch.department} (sent by ${batch.sent_by})`);
    });

    // What FINANCE dashboard should show (batches FROM audit TO finance)
    const financeShouldShow = allBatches.filter(b => 
      b.department === 'FINANCE' && b.from_audit && !b.received
    );
    console.log(`\n💰 FINANCE DASHBOARD should show: ${financeShouldShow.length} batches`);
    financeShouldShow.forEach((batch, index) => {
      console.log(`   ${index + 1}. FROM AUDIT (sent by ${batch.sent_by})`);
    });

    // Check for the confusion issue
    console.log('\n3. CONFUSION ISSUE ANALYSIS:');
    console.log('-'.repeat(50));
    
    const confusedBatches = allBatches.filter(b => 
      b.department === 'FINANCE' && b.from_audit && !b.received
    );
    
    if (confusedBatches.length > 0) {
      console.log('🚨 ISSUE IDENTIFIED:');
      console.log(`   ${confusedBatches.length} batches are FROM AUDIT TO FINANCE`);
      console.log('   These should appear in FINANCE DASHBOARD, not AUDIT DASHBOARD');
      console.log('');
      console.log('   Current Problem:');
      console.log('   ❌ These batches are appearing in: AUDIT Dashboard → Finance Voucher Hub');
      console.log('   ✅ These batches should appear in: Finance Department Dashboard');
      console.log('');
      console.log('   Root Cause:');
      console.log('   - System is confusing Finance Voucher Hub (inside Audit) with Finance Department');
      console.log('   - Batch filtering logic needs to distinguish between these two contexts');
    }

    // Check what's currently wrong
    console.log('\n4. CURRENT WRONG BEHAVIOR:');
    console.log('-'.repeat(50));
    console.log('❌ WRONG: Audit Dashboard shows "FROM FINANCE" batches');
    console.log('   - These are batches that Audit sent TO Finance');
    console.log('   - They should appear in Finance Dashboard, not Audit Dashboard');
    console.log('');
    console.log('✅ CORRECT: Finance Dashboard should show "FROM AUDIT" batches');
    console.log('   - These are batches that Audit sent TO Finance');
    console.log('   - Finance users should receive these batches');

    console.log('\n5. SOLUTION NEEDED:');
    console.log('-'.repeat(50));
    console.log('🔧 Fix Required:');
    console.log('   1. Audit Dashboard should ONLY show batches with from_audit=0');
    console.log('   2. Finance Dashboard should ONLY show batches with from_audit=1 AND department=FINANCE');
    console.log('   3. Finance Voucher Hub (inside Audit) should NOT show these batches');
    console.log('   4. Clear separation between Finance Voucher Hub vs Finance Department Dashboard');

  } catch (error) {
    console.error('❌ Debug failed:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n✅ Database connection closed');
    }
  }
}

debugBatchConfusion();
