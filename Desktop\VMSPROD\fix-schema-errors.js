/**
 * CRITICAL DATABASE SCHEMA FIX
 * 
 * This script fixes the missing database columns that are causing 400 Bad Request errors
 * and preventing proper voucher batch workflow.
 * 
 * Issues being fixed:
 * 1. Missing 'voucher_ids' column in voucher_batches table
 * 2. Missing 'received_by_audit' column in vouchers table
 * 3. Missing 'work_started' column in vouchers table
 */

import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

// Load environment variables from server directory
dotenv.config({ path: './server/.env' });

const connection = await mysql.createConnection({
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'vms@2025@1989',
  database: process.env.DB_NAME || 'vms_production'
});

console.log('🔧 CRITICAL DATABASE SCHEMA FIX');
console.log('================================');
console.log('Fixing missing columns that cause 400 Bad Request errors...\n');

try {
  // Step 1: Check and add voucher_ids column to voucher_batches table
  console.log('1. Checking voucher_batches table...');
  
  const [batchColumns] = await connection.execute(`
    SELECT COLUMN_NAME 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'voucher_batches'
  `, [process.env.DB_NAME || 'vms_production']);
  
  const hasVoucherIds = batchColumns.some(col => col.COLUMN_NAME === 'voucher_ids');
  
  if (!hasVoucherIds) {
    console.log('   ❌ Missing voucher_ids column - ADDING...');
    await connection.execute(`
      ALTER TABLE voucher_batches 
      ADD COLUMN voucher_ids JSON DEFAULT NULL
    `);
    console.log('   ✅ Added voucher_ids column');
  } else {
    console.log('   ✅ voucher_ids column already exists');
  }

  // Step 2: Check and add received_by_audit column to vouchers table
  console.log('\n2. Checking vouchers table for received_by_audit...');
  
  const [voucherColumns] = await connection.execute(`
    SELECT COLUMN_NAME 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'vouchers'
  `, [process.env.DB_NAME || 'vms_production']);
  
  const hasReceivedByAudit = voucherColumns.some(col => col.COLUMN_NAME === 'received_by_audit');
  
  if (!hasReceivedByAudit) {
    console.log('   ❌ Missing received_by_audit column - ADDING...');
    await connection.execute(`
      ALTER TABLE vouchers 
      ADD COLUMN received_by_audit BOOLEAN DEFAULT FALSE
    `);
    console.log('   ✅ Added received_by_audit column');
  } else {
    console.log('   ✅ received_by_audit column already exists');
  }

  // Step 3: Check and add work_started column to vouchers table
  console.log('\n3. Checking vouchers table for work_started...');
  
  const hasWorkStarted = voucherColumns.some(col => col.COLUMN_NAME === 'work_started');
  
  if (!hasWorkStarted) {
    console.log('   ❌ Missing work_started column - ADDING...');
    await connection.execute(`
      ALTER TABLE vouchers 
      ADD COLUMN work_started BOOLEAN DEFAULT FALSE
    `);
    console.log('   ✅ Added work_started column');
  } else {
    console.log('   ✅ work_started column already exists');
  }

  // Step 4: Update existing data to ensure consistency
  console.log('\n4. Updating existing data for consistency...');
  
  // Set received_by_audit = TRUE for vouchers that are in AUDIT department
  const [auditVouchers] = await connection.execute(`
    UPDATE vouchers 
    SET received_by_audit = TRUE 
    WHERE department = 'AUDIT' AND received_by_audit = FALSE
  `);
  console.log(`   ✅ Updated ${auditVouchers.affectedRows} vouchers with received_by_audit = TRUE`);

  // Set work_started = TRUE for vouchers that have been processed (have pre_audited_by or certified_by)
  const [processedVouchers] = await connection.execute(`
    UPDATE vouchers 
    SET work_started = TRUE 
    WHERE (pre_audited_by IS NOT NULL OR certified_by IS NOT NULL) AND work_started = FALSE
  `);
  console.log(`   ✅ Updated ${processedVouchers.affectedRows} vouchers with work_started = TRUE`);

  console.log('\n🎉 DATABASE SCHEMA FIX COMPLETE!');
  console.log('✅ All missing columns have been added');
  console.log('✅ Existing data has been updated for consistency');
  console.log('✅ The 400 Bad Request errors should now be resolved');
  console.log('✅ Voucher batch workflow should work properly');

} catch (error) {
  console.error('\n❌ DATABASE SCHEMA FIX FAILED:', error);
  console.error('Stack:', error.stack);
} finally {
  await connection.end();
  console.log('\n🔌 Database connection closed');
}
