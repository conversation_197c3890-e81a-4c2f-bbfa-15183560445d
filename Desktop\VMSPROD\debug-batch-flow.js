const mysql = require('mysql2/promise');

async function debugBatchFlow() {
  console.log('🔍 DEBUGGING BATCH FLOW ISSUE');
  console.log('='.repeat(50));

  let connection;
  
  try {
    // Connect to database
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'vms@2025@1989',
      database: 'vms_production'
    });
    
    console.log('✅ Connected to database');

    // Step 1: Check all current batches with detailed info
    console.log('\n1. ALL CURRENT BATCHES:');
    console.log('-'.repeat(40));
    
    const [allBatches] = await connection.execute(`
      SELECT 
        id,
        department,
        sent_by,
        sent_time,
        received,
        from_audit,
        CASE 
          WHEN from_audit = 1 THEN 'FROM AUDIT TO DEPARTMENT'
          ELSE 'FROM DEPARTMENT TO AUDIT'
        END as flow_direction
      FROM voucher_batches 
      ORDER BY sent_time DESC
    `);

    if (allBatches.length === 0) {
      console.log('📦 No batches found in system');
    } else {
      console.log(`📦 Found ${allBatches.length} batches:`);
      allBatches.forEach((batch, index) => {
        console.log(`   ${index + 1}. ID: ${batch.id.substring(0, 8)}...`);
        console.log(`      Department: ${batch.department}`);
        console.log(`      Sent By: ${batch.sent_by}`);
        console.log(`      From Audit: ${batch.from_audit ? 'YES' : 'NO'}`);
        console.log(`      Received: ${batch.received ? 'YES' : 'NO'}`);
        console.log(`      Flow: ${batch.flow_direction}`);
        console.log(`      Sent Time: ${batch.sent_time}`);
        console.log('      ---');
      });
    }

    // Step 2: Simulate frontend filtering logic
    console.log('\n2. FRONTEND FILTERING SIMULATION:');
    console.log('-'.repeat(40));
    
    // Simulate AUDIT department filtering
    const auditBatches = allBatches.filter(batch => !batch.from_audit);
    console.log(`🏛️  AUDIT Dashboard would show: ${auditBatches.length} batches`);
    auditBatches.forEach((batch, index) => {
      console.log(`   ${index + 1}. FROM ${batch.department} (${batch.sent_by})`);
    });

    // Simulate FINANCE department filtering
    const financeBatches = allBatches.filter(batch => 
      batch.department === 'FINANCE' && batch.from_audit === 1
    );
    console.log(`💰 FINANCE Dashboard would show: ${financeBatches.length} batches`);
    financeBatches.forEach((batch, index) => {
      console.log(`   ${index + 1}. FROM AUDIT (${batch.sent_by})`);
    });

    // Step 3: Check vouchers in batches
    console.log('\n3. VOUCHERS IN BATCHES:');
    console.log('-'.repeat(40));
    
    for (const batch of allBatches.slice(0, 3)) { // Check first 3 batches
      const [vouchersInBatch] = await connection.execute(`
        SELECT v.voucher_id, v.status, v.department, v.original_department
        FROM vouchers v
        JOIN batch_vouchers bv ON v.id = bv.voucher_id
        WHERE bv.batch_id = ?
      `, [batch.id]);

      console.log(`📋 Batch ${batch.id.substring(0, 8)}... contains ${vouchersInBatch.length} vouchers:`);
      vouchersInBatch.forEach((voucher, index) => {
        console.log(`   ${index + 1}. ${voucher.voucher_id} (${voucher.status})`);
        console.log(`      Current Dept: ${voucher.department}`);
        console.log(`      Original Dept: ${voucher.original_department || 'N/A'}`);
      });
      console.log('   ---');
    }

    // Step 4: Check what should happen
    console.log('\n4. EXPECTED BEHAVIOR ANALYSIS:');
    console.log('-'.repeat(40));
    
    const unreceivedFromAudit = allBatches.filter(batch => 
      batch.from_audit === 1 && batch.received === 0
    );
    
    if (unreceivedFromAudit.length > 0) {
      console.log('🚨 ISSUE IDENTIFIED:');
      console.log(`   ${unreceivedFromAudit.length} unreceived batches FROM AUDIT found:`);
      unreceivedFromAudit.forEach((batch, index) => {
        console.log(`   ${index + 1}. Batch for ${batch.department} department`);
        console.log(`      Should appear in: ${batch.department} Dashboard`);
        console.log(`      Currently appears in: AUDIT Dashboard (WRONG!)`);
        console.log(`      Fix needed: Frontend filtering logic`);
      });
    } else {
      console.log('✅ No unreceived batches from audit found');
    }

    // Step 5: Provide solution
    console.log('\n5. SOLUTION ANALYSIS:');
    console.log('-'.repeat(40));
    console.log('🔧 CURRENT ISSUE:');
    console.log('   Batches with from_audit=1 are appearing in AUDIT dashboard');
    console.log('   They should appear in the DEPARTMENT dashboard instead');
    console.log('');
    console.log('🎯 REQUIRED FIX:');
    console.log('   1. AUDIT dashboard: Show batches with from_audit=0 (FROM departments TO audit)');
    console.log('   2. DEPARTMENT dashboard: Show batches with from_audit=1 AND department=THIS_DEPT');
    console.log('');
    console.log('📍 FRONTEND FILES TO CHECK:');
    console.log('   - client/src/lib/store/slices/department-slice.ts');
    console.log('   - client/src/components/audit-dashboard/audit-dashboard-content-new.tsx');
    console.log('   - client/src/hooks/use-dashboard-state.ts');

  } catch (error) {
    console.error('❌ Debug failed:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n✅ Database connection closed');
    }
  }
}

debugBatchFlow();
