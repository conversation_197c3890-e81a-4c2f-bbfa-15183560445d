import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Voucher } from '@/lib/types';
import { formatNumberWithCommas } from '@/utils/formatUtils';

interface VoucherDetailsDialogProps {
  voucher: Voucher;
  isOpen: boolean;
  onClose: () => void;
  onDelete?: (voucherId: string) => void;
}

export function VoucherDetailsDialog({
  voucher,
  isOpen,
  onClose,
  onDelete
}: VoucherDetailsDialogProps) {
  const handleDelete = () => {
    if (onDelete) {
      onDelete(voucher.id);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={() => onClose()}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="uppercase">VOUCHER DETAILS</DialogTitle>
          <DialogDescription className="uppercase">
            View detailed information about this voucher
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-2 gap-4 py-4">
          <div>
            <p className="font-semibold">Voucher ID:</p>
            <p>{voucher.voucherId}</p>
          </div>
          <div>
            <p className="font-semibold">Date:</p>
            <p>{voucher.date}</p>
          </div>

          <div>
            <p className="font-semibold">Claimant:</p>
            <p>{voucher.claimant}</p>
          </div>
          <div>
            <p className="font-semibold">Status:</p>
            <p>{voucher.status}</p>
          </div>

          <div className="col-span-2">
            <p className="font-semibold">Description:</p>
            <p>{voucher.description}</p>
          </div>

          <div>
            <p className="font-semibold">Amount:</p>
            <p>{formatNumberWithCommas(voucher.amount)} {voucher.currency}</p>
          </div>
          <div>
            <p className="font-semibold">Department:</p>
            <p>{voucher.department}</p>
          </div>

          <div className="col-span-2">
            <p className="font-semibold">Dispatched By:</p>
            <p>{voucher.dispatchedBy}</p>
          </div>

          {/* Always show rejection reason for rejected vouchers */}
          {voucher.status === "VOUCHER REJECTED" && (
            <div className="col-span-2">
              <p className="font-semibold">Rejection Reason:</p>
              <p>{voucher.comment || "No reason provided"}</p>
            </div>
          )}
        </div>

        <DialogFooter className="flex justify-between">
          {onDelete && (
            <Button variant="destructive" onClick={handleDelete}>
              Delete
            </Button>
          )}
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
