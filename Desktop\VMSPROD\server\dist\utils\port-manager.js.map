{"version": 3, "file": "port-manager.js", "sourceRoot": "", "sources": ["../../src/utils/port-manager.ts"], "names": [], "mappings": ";;;;;AAuBA,gDAeC;AAKD,8CAgCC;AAKD,sCA0BC;AAKD,kDAmCC;AAKD,0DA8BC;AAKD,kCAaC;AAvMD,8CAAsB;AACtB,qCAAkC;AAmBlC;;GAEG;AACH,SAAgB,kBAAkB,CAAC,IAAY,EAAE,OAAe,SAAS;IACvE,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;QAC7B,MAAM,MAAM,GAAG,aAAG,CAAC,YAAY,EAAE,CAAC;QAElC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE;YAC7B,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE;gBACxB,OAAO,CAAC,IAAI,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC;YACH,MAAM,CAAC,KAAK,EAAE,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;YACtB,OAAO,CAAC,KAAK,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,iBAAiB,CAAC,MAAkB;IACxD,MAAM,EAAE,aAAa,EAAE,aAAa,EAAE,UAAU,EAAE,GAAG,MAAM,CAAC;IAE5D,2BAA2B;IAC3B,eAAM,CAAC,IAAI,CAAC,+BAA+B,aAAa,EAAE,CAAC,CAAC;IAC5D,IAAI,MAAM,kBAAkB,CAAC,aAAa,CAAC,EAAE,CAAC;QAC5C,eAAM,CAAC,IAAI,CAAC,oBAAoB,aAAa,eAAe,CAAC,CAAC;QAC9D,OAAO,aAAa,CAAC;IACvB,CAAC;IAED,eAAM,CAAC,IAAI,CAAC,qBAAqB,aAAa,sCAAsC,CAAC,CAAC;IAEtF,qBAAqB;IACrB,KAAK,MAAM,IAAI,IAAI,aAAa,EAAE,CAAC;QACjC,eAAM,CAAC,IAAI,CAAC,8BAA8B,IAAI,EAAE,CAAC,CAAC;QAClD,IAAI,MAAM,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC;YACnC,eAAM,CAAC,IAAI,CAAC,mBAAmB,IAAI,eAAe,CAAC,CAAC;YACpD,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,4BAA4B;IAC5B,eAAM,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;IACpE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;QACpC,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QACpE,IAAI,MAAM,kBAAkB,CAAC,UAAU,CAAC,EAAE,CAAC;YACzC,eAAM,CAAC,IAAI,CAAC,iBAAiB,UAAU,eAAe,CAAC,CAAC;YACxD,OAAO,UAAU,CAAC;QACpB,CAAC;IACH,CAAC;IAED,MAAM,IAAI,KAAK,CAAC,yCAAyC,UAAU,WAAW,CAAC,CAAC;AAClF,CAAC;AAED;;GAEG;AACH,SAAgB,aAAa,CAAC,IAAY;IACxC,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IACzB,MAAM,iBAAiB,GAAG,EAAE,CAAC,iBAAiB,EAAE,CAAC;IAEjD,4EAA4E;IAC5E,IAAI,SAAS,GAAG,WAAW,CAAC;IAE5B,KAAK,MAAM,aAAa,IAAI,iBAAiB,EAAE,CAAC;QAC9C,MAAM,SAAS,GAAG,iBAAiB,CAAC,aAAa,CAAC,CAAC;QACnD,IAAI,SAAS,EAAE,CAAC;YACd,KAAK,MAAM,OAAO,IAAI,SAAS,EAAE,CAAC;gBAChC,uCAAuC;gBACvC,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;oBACnD,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC;oBAC5B,MAAM;gBACR,CAAC;YACH,CAAC;YACD,IAAI,SAAS,KAAK,WAAW;gBAAE,MAAM;QACvC,CAAC;IACH,CAAC;IAED,OAAO;QACL,IAAI;QACJ,IAAI,EAAE,SAAS;QACf,GAAG,EAAE,UAAU,SAAS,IAAI,IAAI,EAAE;KACnC,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAgB,mBAAmB,CAAC,UAAsB;IACxD,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;IAC/B,MAAM,MAAM,GAAG,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;IAE1C,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC;QAC7B,IAAI,EAAE,sBAAsB;QAC5B,UAAU;QACV,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;QACrB,OAAO,EAAE,OAAO;KACjB,CAAC,CAAC;IAEH,6BAA6B;IAC7B,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE;QACf,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QAE1B,kCAAkC;QAClC,MAAM,iBAAiB,GAAG,WAAW,CAAC,GAAG,EAAE;YACzC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,OAAO,CAAC,MAAM,EAAE,IAAI,EAAE,iBAAiB,EAAE,CAAC,GAAQ,EAAE,EAAE;gBAC5E,IAAI,GAAG,EAAE,CAAC;oBACR,eAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE,GAAG,CAAC,CAAC;gBAC1C,CAAC;qBAAM,CAAC;oBACN,eAAM,CAAC,IAAI,CAAC,gCAAgC,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC;gBAChE,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,EAAE,KAAK,CAAC,CAAC;QAEV,oBAAoB;QACpB,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,OAAO,CAAC,MAAM,EAAE,IAAI,EAAE,iBAAiB,CAAC,CAAC;QAEjE,0BAA0B;QAC1B,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;YACxB,aAAa,CAAC,iBAAiB,CAAC,CAAC;YACjC,MAAM,CAAC,KAAK,EAAE,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;GAEG;AACH,SAAgB,uBAAuB,CAAC,UAAsB;IAC5D,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IACzB,MAAM,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IAE7B,MAAM,cAAc,GAAG;QACrB,GAAG,UAAU;QACb,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;QACrC,GAAG,EAAE,OAAO,CAAC,GAAG;KACjB,CAAC;IAEF,IAAI,CAAC;QACH,4CAA4C;QAC5C,MAAM,SAAS,GAAG;YAChB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,kBAAkB,CAAC;YAC5C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE,sBAAsB,CAAC;YAC1D,yCAAyC;YACzC,qCAAqC;SACtC,CAAC;QAEF,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC3B,IAAI,CAAC;gBACH,EAAE,CAAC,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;gBACpE,eAAM,CAAC,IAAI,CAAC,4BAA4B,QAAQ,EAAE,CAAC,CAAC;YACtD,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACb,eAAM,CAAC,IAAI,CAAC,wBAAwB,QAAQ,GAAG,EAAG,GAAa,CAAC,OAAO,CAAC,CAAC;YAC3E,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;IACrD,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAgB,WAAW,CAAC,IAAY,EAAE,eAA2B;IACnE,MAAM,aAAa,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;QAC3C,MAAM,WAAW,GAAG,MAAM,kBAAkB,CAAC,IAAI,CAAC,CAAC;QACnD,IAAI,WAAW,EAAE,CAAC;YAChB,eAAM,CAAC,KAAK,CAAC,UAAU,IAAI,qCAAqC,CAAC,CAAC;YAClE,aAAa,CAAC,aAAa,CAAC,CAAC;YAC7B,eAAe,EAAE,CAAC;QACpB,CAAC;IACH,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,qBAAqB;IAEhC,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;QACxB,aAAa,CAAC,aAAa,CAAC,CAAC;IAC/B,CAAC,CAAC,CAAC;AACL,CAAC"}