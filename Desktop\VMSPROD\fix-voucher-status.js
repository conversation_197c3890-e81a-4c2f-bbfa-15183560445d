const mysql = require('mysql2/promise');

async function fixVoucher() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root', 
    password: 'vms@2025@1989',
    database: 'vms_production'
  });
  
  console.log('🔧 Manually updating voucher to simulate proper batch receiving...');
  
  // Update voucher as if it was properly received by Finance from audit
  await connection.execute(`
    UPDATE vouchers SET
      status = 'VOUCHER CERTIFIED',
      department = 'FINANCE',
      department_receipt_time = NOW(),
      sent_to_audit = FALSE,
      certified_by = 'SAMUEL ASIEDU',
      audit_dispatched_by = 'SAMUEL ASIEDU',
      audit_dispatch_time = NOW()
    WHERE id = 'd34ca623-d30c-4fd9-97e3-90ca0193bf84'
  `);
  
  console.log('✅ Voucher updated - should now appear in Finance Certified tab');
  
  // Check the result
  const [vouchers] = await connection.execute('SELECT voucher_id, department, status, sent_to_audit, certified_by, audit_dispatched_by FROM vouchers WHERE id = ?', ['d34ca623-d30c-4fd9-97e3-90ca0193bf84']);
  const voucher = vouchers[0];
  console.log('📄 Updated voucher:');
  console.log('   Voucher ID:', voucher.voucher_id);
  console.log('   Department:', voucher.department);
  console.log('   Status:', voucher.status);
  console.log('   Sent to Audit:', voucher.sent_to_audit);
  console.log('   Certified By:', voucher.certified_by);
  console.log('   Audit Dispatched By:', voucher.audit_dispatched_by);
  
  await connection.end();
}

fixVoucher().catch(console.error);
