const mysql = require('mysql2/promise');

async function testOffsetLogic() {
  console.log('🧪 Testing Voucher Offset Logic');
  console.log('='.repeat(50));

  let connection;
  
  try {
    // Connect to database
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'vms@2025@1989',
      database: 'vms_production'
    });
    
    console.log('✅ Connected to database');

    // Step 1: Check current vouchers with reference_id
    console.log('\n1. VOUCHERS WITH REFERENCE_ID:');
    console.log('-'.repeat(40));
    
    const [vouchersWithRef] = await connection.execute(`
      SELECT voucher_id, status, department, reference_id, sent_to_audit, deleted
      FROM vouchers 
      WHERE reference_id IS NOT NULL
      ORDER BY voucher_id
    `);

    if (vouchersWithRef.length === 0) {
      console.log('📋 No vouchers with reference_id found');
      console.log('   This means no vouchers have been sent to audit yet with the new offset logic');
    } else {
      console.log(`📋 Found ${vouchersWithRef.length} vouchers with reference_id:`);
      vouchersWithRef.forEach((voucher, index) => {
        console.log(`   ${index + 1}. ${voucher.voucher_id}`);
        console.log(`      Status: ${voucher.status}`);
        console.log(`      Department: ${voucher.department}`);
        console.log(`      Reference ID: ${voucher.reference_id}`);
        console.log(`      Sent to Audit: ${voucher.sent_to_audit ? 'YES' : 'NO'}`);
        console.log(`      Deleted: ${voucher.deleted ? 'YES' : 'NO'}`);
        console.log('      ---');
      });
    }

    // Step 2: Check for offset vouchers
    console.log('\n2. OFFSET VOUCHERS:');
    console.log('-'.repeat(40));
    
    const [offsetVouchers] = await connection.execute(`
      SELECT voucher_id, status, department, deleted, deletion_time
      FROM vouchers 
      WHERE status = 'OFFSET_BY_AUDIT'
      ORDER BY voucher_id
    `);

    if (offsetVouchers.length === 0) {
      console.log('📋 No offset vouchers found');
      console.log('   This means no vouchers have been offset yet');
    } else {
      console.log(`📋 Found ${offsetVouchers.length} offset vouchers:`);
      offsetVouchers.forEach((voucher, index) => {
        console.log(`   ${index + 1}. ${voucher.voucher_id}`);
        console.log(`      Status: ${voucher.status}`);
        console.log(`      Department: ${voucher.department}`);
        console.log(`      Deleted: ${voucher.deleted ? 'YES' : 'NO'}`);
        console.log(`      Deletion Time: ${voucher.deletion_time || 'N/A'}`);
        console.log('      ---');
      });
    }

    // Step 3: Check for processed vouchers from audit
    console.log('\n3. PROCESSED VOUCHERS FROM AUDIT:');
    console.log('-'.repeat(40));
    
    const [processedVouchers] = await connection.execute(`
      SELECT voucher_id, status, department, original_department, certified_by, pre_audited_amount, reference_id
      FROM vouchers 
      WHERE status IN ('VOUCHER CERTIFIED', 'VOUCHER PROCESSING', 'VOUCHER REJECTED')
        AND reference_id IS NOT NULL
      ORDER BY voucher_id
    `);

    if (processedVouchers.length === 0) {
      console.log('📋 No processed vouchers from audit found');
    } else {
      console.log(`📋 Found ${processedVouchers.length} processed vouchers from audit:`);
      processedVouchers.forEach((voucher, index) => {
        console.log(`   ${index + 1}. ${voucher.voucher_id}`);
        console.log(`      Status: ${voucher.status}`);
        console.log(`      Department: ${voucher.department}`);
        console.log(`      Original Department: ${voucher.original_department}`);
        console.log(`      Reference ID: ${voucher.reference_id}`);
        console.log(`      Certified By: ${voucher.certified_by || 'N/A'}`);
        console.log(`      Pre-audited Amount: ${voucher.pre_audited_amount || 'N/A'}`);
        console.log('      ---');
      });
    }

    // Step 4: Verify offset logic integrity
    console.log('\n4. OFFSET LOGIC VERIFICATION:');
    console.log('-'.repeat(40));
    
    // Check for any vouchers that should have been offset but weren't
    const [shouldBeOffset] = await connection.execute(`
      SELECT DISTINCT v1.voucher_id as original_voucher, v2.voucher_id as processed_voucher
      FROM vouchers v1
      JOIN vouchers v2 ON v1.voucher_id = v2.reference_id
      WHERE v1.status NOT IN ('OFFSET_BY_AUDIT', 'VOUCHER CERTIFIED', 'VOUCHER REJECTED', 'VOUCHER PROCESSING')
        AND v2.status IN ('VOUCHER CERTIFIED', 'VOUCHER PROCESSING', 'VOUCHER REJECTED')
        AND v1.deleted = FALSE
    `);

    if (shouldBeOffset.length === 0) {
      console.log('✅ Offset logic integrity check passed');
      console.log('   No vouchers found that should have been offset but weren\'t');
    } else {
      console.log('🚨 OFFSET LOGIC ISSUES FOUND:');
      shouldBeOffset.forEach((pair, index) => {
        console.log(`   ${index + 1}. Original: ${pair.original_voucher} should be offset by Processed: ${pair.processed_voucher}`);
      });
    }

    console.log('\n5. EXPECTED BEHAVIOR:');
    console.log('-'.repeat(40));
    console.log('✅ CORRECT OFFSET WORKFLOW:');
    console.log('   1. Original voucher sent to audit gets reference_id = voucher_id');
    console.log('   2. When processed voucher returns from audit:');
    console.log('      - Original voucher status → OFFSET_BY_AUDIT');
    console.log('      - Original voucher deleted = TRUE');
    console.log('      - Processed voucher appears in Certified tab');
    console.log('      - NO duplication in Processing or Certified tabs');
    
    console.log('\n🎯 SUMMARY:');
    if (vouchersWithRef.length === 0 && offsetVouchers.length === 0 && processedVouchers.length === 0) {
      console.log('   System is clean - no offset operations have occurred yet');
      console.log('   Ready to test the offset logic with new voucher workflow');
    } else {
      console.log('   Offset logic is active and working');
      console.log(`   - ${vouchersWithRef.length} vouchers have reference_id set`);
      console.log(`   - ${offsetVouchers.length} vouchers have been offset`);
      console.log(`   - ${processedVouchers.length} processed vouchers from audit`);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n✅ Database connection closed');
    }
  }
}

testOffsetLogic();
