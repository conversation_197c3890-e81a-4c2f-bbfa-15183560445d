const mysql = require('mysql2/promise');
const http = require('http');

async function verifyAPIFix() {
  console.log('🔍 VERIFYING API FIX AFTER SERVER RESTART\n');
  
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });

  try {
    // 1. Database verification
    console.log('1. 📊 DATABASE STATE:');
    const [dbVouchers] = await connection.execute(`
      SELECT voucher_id, department, original_department, sent_to_audit
      FROM vouchers WHERE deleted = FALSE ORDER BY created_at DESC
    `);
    
    const financeTotal = dbVouchers.filter(v => v.department === 'FINANCE' || v.original_department === 'FINANCE').length;
    console.log(`   Total vouchers: ${dbVouchers.length}`);
    console.log(`   FINANCE-related: ${financeTotal}`);

    // 2. API test
    console.log('\n2. 🌐 API TEST:');
    
    // Login
    const loginData = JSON.stringify({
      department: 'FINANCE',
      username: 'FELIX AYISI', 
      password: '123'
    });

    const loginResponse = await makeRequest({
      hostname: 'localhost',
      port: 8080,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(loginData)
      }
    }, loginData);

    if (loginResponse.statusCode === 200) {
      console.log('   ✅ Login successful');
      
      const cookies = loginResponse.headers['set-cookie'] || [];
      const cookieHeader = cookies.map(c => c.split(';')[0]).join('; ');
      
      // Get vouchers
      const vouchersResponse = await makeRequest({
        hostname: 'localhost',
        port: 8080,
        path: '/api/vouchers',
        method: 'GET',
        headers: { 'Cookie': cookieHeader }
      });

      if (vouchersResponse.statusCode === 200) {
        const apiVouchers = JSON.parse(vouchersResponse.data);
        console.log(`   ✅ API returned ${apiVouchers.length} vouchers`);
        
        // Check critical fields
        const withOriginalDept = apiVouchers.filter(v => v.originalDepartment !== undefined).length;
        const withSentToAudit = apiVouchers.filter(v => v.sentToAudit !== undefined).length;
        
        console.log(`   originalDepartment field: ${withOriginalDept}/${apiVouchers.length}`);
        console.log(`   sentToAudit field: ${withSentToAudit}/${apiVouchers.length}`);
        
        // Test filtering
        const financeRelevant = apiVouchers.filter(v => 
          v.department === 'FINANCE' || v.originalDepartment === 'FINANCE'
        );
        console.log(`   FINANCE-relevant vouchers: ${financeRelevant.length}`);
        
        // Show first voucher details
        if (apiVouchers.length > 0) {
          const sample = apiVouchers[0];
          console.log('\n   📋 SAMPLE VOUCHER:');
          console.log(`     voucherId: ${sample.voucherId}`);
          console.log(`     department: "${sample.department}"`);
          console.log(`     originalDepartment: "${sample.originalDepartment}"`);
          console.log(`     sentToAudit: ${sample.sentToAudit} (${typeof sample.sentToAudit})`);
        }
        
        // Final verdict
        console.log('\n3. 🎯 VERDICT:');
        if (withOriginalDept === apiVouchers.length && financeRelevant === financeTotal) {
          console.log('   ✅ FIX SUCCESSFUL: All vouchers have originalDepartment field');
          console.log('   ✅ FILTERING WORKS: Correct number of FINANCE vouchers found');
        } else {
          console.log('   ❌ FIX INCOMPLETE:');
          if (withOriginalDept < apiVouchers.length) {
            console.log(`     - Missing originalDepartment in ${apiVouchers.length - withOriginalDept} vouchers`);
          }
          if (financeRelevant !== financeTotal) {
            console.log(`     - Expected ${financeTotal} FINANCE vouchers, got ${financeRelevant}`);
          }
        }
        
      } else {
        console.log(`   ❌ Vouchers API failed: ${vouchersResponse.statusCode}`);
      }
    } else {
      console.log(`   ❌ Login failed: ${loginResponse.statusCode}`);
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await connection.end();
  }
}

function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => responseData += chunk);
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: responseData
        });
      });
    });
    
    req.on('error', reject);
    if (data) req.write(data);
    req.end();
  });
}

verifyAPIFix().catch(console.error);
