const mysql = require('mysql2/promise');

// Database configuration (matching server config)
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: 'vms@2025@1989',
  database: 'vms_clean_db'
};

async function fixDatabaseSchema() {
  let connection;
  
  try {
    console.log('🔧 Fixing VMS database schema...');
    
    // Connect to MySQL server (not specific database)
    const serverConfig = { ...dbConfig };
    delete serverConfig.database;
    
    connection = await mysql.createConnection(serverConfig);
    console.log('✅ Connected to MySQL server');

    // Drop the problematic database
    console.log('\n🗑️ Dropping existing database with schema issues...');
    await connection.query('DROP DATABASE IF EXISTS vms_clean_db');
    console.log('✅ Database dropped successfully');

    // Recreate the database
    console.log('\n🏗️ Creating fresh database...');
    await connection.query('CREATE DATABASE vms_clean_db');
    console.log('✅ Fresh database created');

    console.log('\n🎉 DATABASE SCHEMA FIX COMPLETED!');
    console.log('📝 Summary:');
    console.log('   - Dropped old database with schema errors');
    console.log('   - Created fresh database');
    console.log('   - Server will now initialize with correct schema');
    console.log('\n🚀 Restart the VMS server to apply the fix!');

  } catch (error) {
    console.error('\n❌ Error fixing database schema:', error.message);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the fix
fixDatabaseSchema()
  .then(() => {
    console.log('\n✨ Database schema fix completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Database schema fix failed:', error.message);
    process.exit(1);
  });
