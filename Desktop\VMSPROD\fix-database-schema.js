const mysql = require('mysql2/promise');

async function fixDatabaseSchema() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });

  console.log('🔧 Fixing database schema...\n');
  
  try {
    // Add missing columns to vouchers table
    console.log('📊 Adding missing columns to vouchers table...');
    
    // Add received_by_audit column
    try {
      await connection.execute(`
        ALTER TABLE vouchers 
        ADD COLUMN received_by_audit BOOLEAN DEFAULT FALSE
      `);
      console.log('✅ Added received_by_audit column');
    } catch (error) {
      if (error.code === 'ER_DUP_FIELDNAME') {
        console.log('ℹ️ received_by_audit column already exists');
      } else {
        throw error;
      }
    }
    
    // Add original_department column
    try {
      await connection.execute(`
        ALTER TABLE vouchers 
        ADD COLUMN original_department VARCHAR(50)
      `);
      console.log('✅ Added original_department column');
    } catch (error) {
      if (error.code === 'ER_DUP_FIELDNAME') {
        console.log('ℹ️ original_department column already exists');
      } else {
        throw error;
      }
    }
    
    // Add flags column
    try {
      await connection.execute(`
        ALTER TABLE vouchers 
        ADD COLUMN flags TEXT
      `);
      console.log('✅ Added flags column');
    } catch (error) {
      if (error.code === 'ER_DUP_FIELDNAME') {
        console.log('ℹ️ flags column already exists');
      } else {
        throw error;
      }
    }
    
    // Add created_at column
    try {
      await connection.execute(`
        ALTER TABLE vouchers 
        ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      `);
      console.log('✅ Added created_at column');
    } catch (error) {
      if (error.code === 'ER_DUP_FIELDNAME') {
        console.log('ℹ️ created_at column already exists');
      } else {
        throw error;
      }
    }
    
    // Update existing vouchers to set original_department = department where null
    console.log('\n📊 Updating existing vouchers...');
    const [updateResult] = await connection.execute(`
      UPDATE vouchers 
      SET original_department = department 
      WHERE original_department IS NULL
    `);
    console.log(`✅ Updated ${updateResult.affectedRows} vouchers with original_department`);
    
    // Add missing columns to voucher_batches table
    console.log('\n📊 Adding missing columns to voucher_batches table...');
    
    // Add voucher_ids column
    try {
      await connection.execute(`
        ALTER TABLE voucher_batches 
        ADD COLUMN voucher_ids TEXT
      `);
      console.log('✅ Added voucher_ids column to voucher_batches');
    } catch (error) {
      if (error.code === 'ER_DUP_FIELDNAME') {
        console.log('ℹ️ voucher_ids column already exists in voucher_batches');
      } else {
        throw error;
      }
    }
    
    console.log('\n🎉 Database schema fixed successfully!');
    
  } catch (error) {
    console.error('❌ Error fixing database schema:', error);
  } finally {
    await connection.end();
  }
}

fixDatabaseSchema();
