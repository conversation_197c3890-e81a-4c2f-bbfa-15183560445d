const mysql = require('mysql2/promise');

async function fixAuthenticationSchema() {
  console.log('🔧 FIXING AUTHENTICATION SCHEMA ISSUES...\n');
  
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });

  try {
    // Step 1: Check current users table structure
    console.log('1. CHECKING USERS TABLE STRUCTURE...');
    const [userColumns] = await connection.execute('DESCRIBE users');
    console.log('Current users table columns:');
    userColumns.forEach(col => {
      console.log(`   ${col.Field} - ${col.Type}`);
    });

    // Step 2: Create departments table if missing
    console.log('\n2. CREATING DEPARTMENTS TABLE...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS departments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL UNIQUE,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ departments table created/verified');

    // Step 3: Insert department data
    console.log('\n3. INSERTING DEPARTMENT DATA...');
    const departments = [
      { id: 1, name: 'FINANCE', description: 'Finance Department' },
      { id: 2, name: 'AUDIT', description: 'Audit Department' },
      { id: 3, name: 'SYSTEM ADMIN', description: 'System Administration' },
      { id: 4, name: 'HR', description: 'Human Resources' },
      { id: 5, name: 'PROCUREMENT', description: 'Procurement Department' }
    ];

    for (const dept of departments) {
      await connection.execute(`
        INSERT IGNORE INTO departments (id, name, description) 
        VALUES (?, ?, ?)
      `, [dept.id, dept.name, dept.description]);
    }
    console.log('✅ Department data inserted');

    // Step 4: Check if users table has department_id column
    const hasDepId = userColumns.some(col => col.Field === 'department_id');
    if (!hasDepId) {
      console.log('\n4. ADDING department_id COLUMN TO USERS...');
      await connection.execute(`
        ALTER TABLE users 
        ADD COLUMN department_id INT,
        ADD FOREIGN KEY (department_id) REFERENCES departments(id)
      `);
      console.log('✅ department_id column added');
    } else {
      console.log('\n4. department_id column already exists');
    }

    // Step 5: Update users with department_id based on department name
    console.log('\n5. UPDATING USERS WITH DEPARTMENT_ID...');
    const [users] = await connection.execute('SELECT id, name, department FROM users');
    
    for (const user of users) {
      // Find department ID
      const [deptResult] = await connection.execute(
        'SELECT id FROM departments WHERE name = ?', 
        [user.department]
      );
      
      if (deptResult.length > 0) {
        await connection.execute(
          'UPDATE users SET department_id = ? WHERE id = ?',
          [deptResult[0].id, user.id]
        );
        console.log(`✅ Updated ${user.name} with department_id ${deptResult[0].id}`);
      } else {
        console.log(`⚠️ No department found for ${user.name} (${user.department})`);
      }
    }

    // Step 6: Test authentication query
    console.log('\n6. TESTING AUTHENTICATION QUERY...');
    try {
      const [testResult] = await connection.execute(`
        SELECT u.*, d.name as department_name
        FROM users u
        LEFT JOIN departments d ON u.department_id = d.id
        WHERE UPPER(u.name) = ? AND UPPER(d.name) = ?
      `, ['FELIX AYISI', 'FINANCE']);
      
      if (testResult.length > 0) {
        console.log('✅ Authentication query works!');
        console.log(`   Found user: ${testResult[0].name} in ${testResult[0].department_name}`);
      } else {
        console.log('❌ Authentication query returned no results');
      }
    } catch (authError) {
      console.log('❌ Authentication query failed:', authError.message);
    }

    // Step 7: Test users-by-department query
    console.log('\n7. TESTING USERS-BY-DEPARTMENT QUERY...');
    try {
      const [usersResult] = await connection.execute(`
        SELECT u.id, u.name, d.name as department
        FROM users u
        LEFT JOIN departments d ON u.department_id = d.id
        WHERE u.is_active = TRUE
      `);
      
      console.log('✅ Users-by-department query works!');
      console.log(`   Found ${usersResult.length} active users:`);
      usersResult.forEach(user => {
        console.log(`   - ${user.name} (${user.department})`);
      });
    } catch (usersError) {
      console.log('❌ Users-by-department query failed:', usersError.message);
    }

    console.log('\n🎉 AUTHENTICATION SCHEMA FIXES COMPLETED!');

  } catch (error) {
    console.error('❌ Error fixing schema:', error);
  } finally {
    await connection.end();
  }
}

fixAuthenticationSchema().catch(console.error);
