const mysql = require('mysql2/promise');

// Database configuration (matching server config)
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: 'vms@2025@1989',
  database: 'vms_clean_db'
};

async function cleanAllVouchers() {
  let connection;
  
  try {
    console.log('🧹 Starting complete voucher system cleanup...');
    
    // Connect to database
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Connected to database');

    // Start transaction
    await connection.beginTransaction();
    console.log('🔄 Starting transaction...');

    // 1. Clear all voucher-related data
    console.log('\n📋 Clearing voucher data...');
    
    // Clear batch_vouchers (junction table first due to foreign keys)
    const batchVouchersResult = await connection.execute('DELETE FROM batch_vouchers');
    console.log(`   ✅ Cleared ${batchVouchersResult[0].affectedRows} batch_vouchers records`);
    
    // Clear voucher_batches
    const batchesResult = await connection.execute('DELETE FROM voucher_batches');
    console.log(`   ✅ Cleared ${batchesResult[0].affectedRows} voucher_batches records`);
    
    // Clear vouchers
    const vouchersResult = await connection.execute('DELETE FROM vouchers');
    console.log(`   ✅ Cleared ${vouchersResult[0].affectedRows} vouchers records`);

    // 2. Clear notifications
    console.log('\n🔔 Clearing notifications...');
    const notificationsResult = await connection.execute('DELETE FROM notifications');
    console.log(`   ✅ Cleared ${notificationsResult[0].affectedRows} notifications records`);

    // 3. Reset auto-increment counters
    console.log('\n🔄 Resetting auto-increment counters...');
    await connection.execute('ALTER TABLE vouchers AUTO_INCREMENT = 1');
    await connection.execute('ALTER TABLE voucher_batches AUTO_INCREMENT = 1');
    await connection.execute('ALTER TABLE batch_vouchers AUTO_INCREMENT = 1');
    await connection.execute('ALTER TABLE notifications AUTO_INCREMENT = 1');
    console.log('   ✅ Auto-increment counters reset');

    // 4. Verify cleanup
    console.log('\n🔍 Verifying cleanup...');
    const [voucherCount] = await connection.execute('SELECT COUNT(*) as count FROM vouchers');
    const [batchCount] = await connection.execute('SELECT COUNT(*) as count FROM voucher_batches');
    const [batchVoucherCount] = await connection.execute('SELECT COUNT(*) as count FROM batch_vouchers');
    const [notificationCount] = await connection.execute('SELECT COUNT(*) as count FROM notifications');
    
    console.log(`   📊 Vouchers remaining: ${voucherCount[0].count}`);
    console.log(`   📊 Batches remaining: ${batchCount[0].count}`);
    console.log(`   📊 Batch-vouchers remaining: ${batchVoucherCount[0].count}`);
    console.log(`   📊 Notifications remaining: ${notificationCount[0].count}`);

    // Commit transaction
    await connection.commit();
    console.log('\n✅ Transaction committed successfully');

    console.log('\n🎉 COMPLETE VOUCHER SYSTEM CLEANUP SUCCESSFUL!');
    console.log('📝 Summary:');
    console.log(`   - Vouchers cleared: ${vouchersResult[0].affectedRows}`);
    console.log(`   - Batches cleared: ${batchesResult[0].affectedRows}`);
    console.log(`   - Batch-vouchers cleared: ${batchVouchersResult[0].affectedRows}`);
    console.log(`   - Notifications cleared: ${notificationsResult[0].affectedRows}`);
    console.log('\n🚀 System is now ready for fresh voucher creation!');

  } catch (error) {
    console.error('\n❌ Error during cleanup:', error);
    
    if (connection) {
      try {
        await connection.rollback();
        console.log('🔄 Transaction rolled back');
      } catch (rollbackError) {
        console.error('❌ Rollback failed:', rollbackError);
      }
    }
    
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the cleanup
cleanAllVouchers()
  .then(() => {
    console.log('\n✨ Cleanup completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Cleanup failed:', error.message);
    process.exit(1);
  });
