/**
 * This file defines the standard voucher status constants
 * to ensure consistency across the client application.
 */

// Define all possible voucher statuses
export const VOUCHER_STATUSES = {
  // Department statuses
  PENDING: 'PENDING',
  PENDING_SUBMISSION: 'PENDING SUBMISSION',
  PENDING_RECEIPT: 'PENDING RECEIPT',
  VOUCHER_PROCESSING: 'VOUCHER PROCESSING',

  // Audit statuses
  AUDIT_PROCESSING: 'AUDIT: PROCESSING',

  // Final statuses
  VOUCHER_CERTIFIED: 'VOUCHER CERTIFIED',
  VOUCHER_REJECTED: 'VOUCHER REJECTED',
  VOUCHER_RETURNED: 'VOUCHER RETURNED',

  // Other statuses
  VOUCHER_PENDING_RETURN: 'VOUCHER PENDING RETURN',

  // System statuses
  OFFSET_BY_AUDIT: 'OFFSET_BY_AUDIT',
};

/**
 * Get the display name for a voucher status
 * @param status The voucher status
 * @returns A user-friendly display name
 */
export function getStatusDisplayName(status: string): string {
  switch (status) {
    case VOUCHER_STATUSES.PENDING:
      return 'Pending';
    case VOUCHER_STATUSES.PENDING_SUBMISSION:
      return 'Pending Submission';
    case VOUCHER_STATUSES.PENDING_RECEIPT:
      return 'Pending Receipt';
    case VOUCHER_STATUSES.VOUCHER_PROCESSING:
      return 'Processing';
    case VOUCHER_STATUSES.AUDIT_PROCESSING:
      return 'In Audit';
    case VOUCHER_STATUSES.VOUCHER_CERTIFIED:
      return 'Certified';
    case VOUCHER_STATUSES.VOUCHER_REJECTED:
      return 'Rejected';
    case VOUCHER_STATUSES.VOUCHER_RETURNED:
      return 'Returned';
    case VOUCHER_STATUSES.VOUCHER_PENDING_RETURN:
      return 'Pending Return';
    default:
      return status;
  }
}

/**
 * Get the status color for a voucher status
 * @param status The voucher status
 * @returns A CSS color class
 */
export function getStatusColor(status: string): string {
  switch (status) {
    case VOUCHER_STATUSES.PENDING:
      return 'text-yellow-500';
    case VOUCHER_STATUSES.PENDING_SUBMISSION:
      return 'text-yellow-500';
    case VOUCHER_STATUSES.PENDING_RECEIPT:
      return 'text-blue-500';
    case VOUCHER_STATUSES.VOUCHER_PROCESSING:
      return 'text-blue-500';
    case VOUCHER_STATUSES.AUDIT_PROCESSING:
      return 'text-blue-500';
    case VOUCHER_STATUSES.VOUCHER_CERTIFIED:
      return 'text-green-500';
    case VOUCHER_STATUSES.VOUCHER_REJECTED:
      return 'text-red-500';
    case VOUCHER_STATUSES.VOUCHER_RETURNED:
      return 'text-orange-500';
    case VOUCHER_STATUSES.VOUCHER_PENDING_RETURN:
      return 'text-orange-500';
    default:
      return 'text-gray-500';
  }
}
