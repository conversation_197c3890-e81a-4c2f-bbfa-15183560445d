const mysql = require('mysql2/promise');

async function checkAuditUsers() {
  console.log('🔍 CHECKING AUDIT USERS IN DATABASE');
  console.log('='.repeat(50));
  
  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'vms@2025@1989',
      database: 'vms_production'
    });

    // First check table structure
    const [columns] = await connection.execute(`
      SHOW COLUMNS FROM users
    `);

    console.log('📋 User table columns:');
    columns.forEach(col => {
      console.log(`   - ${col.Field} (${col.Type})`);
    });

    const [users] = await connection.execute(`
      SELECT *
      FROM users
      WHERE department = 'AUDIT'
      ORDER BY name
    `);

    console.log(`📊 Found ${users.length} AUDIT users:`);
    users.forEach((user, index) => {
      console.log(`   ${index + 1}. ${user.name} - ${user.role} - ID: ${user.id}`);
    });

    if (users.length === 0) {
      console.log('\n❌ No AUDIT users found! Creating one...');
      
      const bcrypt = require('bcrypt');
      const { v4: uuidv4 } = require('uuid');
      
      const hashedPassword = await bcrypt.hash('audit123', 10);
      const userId = uuidv4();
      
      await connection.execute(`
        INSERT INTO users (id, name, department, role, password_hash)
        VALUES (?, ?, ?, ?, ?)
      `, [userId, 'HARRISON A. SARPONG', 'AUDIT', 'USER', hashedPassword]);
      
      console.log('✅ Created AUDIT user: HARRISON A. SARPONG / audit123');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the check
checkAuditUsers().catch(console.error);
