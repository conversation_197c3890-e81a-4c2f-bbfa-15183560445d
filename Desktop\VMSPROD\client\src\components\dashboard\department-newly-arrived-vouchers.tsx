import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { ArrowDownCircle, Lock } from 'lucide-react';
import { formatVMSDateTime } from '@/utils/formatUtils';

interface DepartmentNewlyArrivedVouchersProps {
  batchesArray: any[];
  onReceiveVouchers: (batchId: string, isBatchId?: boolean) => void;
}

export function DepartmentNewlyArrivedVouchers({ 
  batchesArray, 
  onReceiveVouchers 
}: DepartmentNewlyArrivedVouchersProps) {
  // Safety check for batchesArray
  if (!batchesArray || batchesArray.length === 0) {
    return null;
  }

  return (
    <Card className="border-amber-500 dark:border-amber-400 shadow-lg">
      <CardHeader className="bg-amber-50 dark:bg-amber-900/30">
        <div className="flex items-center gap-2">
          <ArrowDownCircle className="h-5 w-5 text-amber-500" />
          <CardTitle className="uppercase flex items-center gap-2">
            NEWLY ARRIVED VOUCHERS FROM AUDIT
            <Lock className="h-4 w-4 text-amber-500" />
          </CardTitle>
        </div>
        <div className="text-sm text-amber-700 dark:text-amber-300 font-medium mt-1">
          You must receive these processed vouchers from audit before proceeding with other tasks.
        </div>
      </CardHeader>
      <CardContent className="pt-4">
        <ScrollArea className="h-[300px]">
          <div className="space-y-4">
            {batchesArray.map((batch) => {
              // Skip invalid batches
              if (!batch || !batch.id) return null;

              return (
                <Card key={batch.id} className="hover:shadow-md transition-shadow border-amber-200 dark:border-amber-800">
                  <CardContent className="p-4">
                    <div className="flex justify-between items-center">
                      <div>
                        <h3 className="font-medium uppercase">FROM AUDIT DEPARTMENT</h3>
                        <p className="text-sm text-muted-foreground uppercase">
                          SENT BY: {batch.sentBy || 'UNKNOWN'} ON {batch.sentTime ? formatVMSDateTime(batch.sentTime) : 'UNKNOWN'}
                        </p>
                        <p className="text-sm font-medium mt-1 uppercase">
                          {batch.voucherIds?.length || batch.vouchers?.length || 0} PROCESSED VOUCHER{((batch.voucherIds?.length || batch.vouchers?.length || 0) !== 1) ? 'S' : ''}
                        </p>
                        <div className="text-xs text-green-600 dark:text-green-400 mt-1 uppercase">
                          ✅ VOUCHERS HAVE BEEN PROCESSED BY AUDIT
                        </div>
                      </div>
                      <Button
                        onClick={() => batch.id && onReceiveVouchers(batch.id, true)}
                        className="uppercase bg-amber-600 hover:bg-amber-700"
                      >
                        RECEIVE VOUCHERS
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
