// <PERSON><PERSON>er console script to debug DISPATCHED tab data flow
// Copy and paste this into the browser console while on the Audit Dashboard

console.log('🔍 DEBUGGING DISPATCHED TAB DATA FLOW');
console.log('=====================================');

// Check if we're in the right context
if (typeof window === 'undefined') {
  console.error('❌ This script must be run in the browser console');
} else {
  console.log('✅ Running in browser context');
}

// Try to access the store
let store = null;
try {
  // Try different ways to access the store
  if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
    console.log('🔍 React DevTools detected');
  }
  
  // Check if store is available globally
  if (window.store) {
    store = window.store;
    console.log('✅ Found global store');
  } else {
    console.log('⚠️ No global store found');
  }
} catch (error) {
  console.log('⚠️ Error accessing store:', error.message);
}

// Test API access
console.log('\n📡 TESTING API ACCESS');
console.log('---------------------');

fetch('/api/vouchers', {
  credentials: 'include'
})
.then(response => {
  console.log('📊 API Response Status:', response.status);
  if (response.ok) {
    return response.json();
  } else {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }
})
.then(vouchers => {
  console.log('✅ API Data Retrieved Successfully');
  console.log(`📊 Total vouchers: ${vouchers.length}`);
  
  // Find FINANCE vouchers
  const financeVouchers = vouchers.filter(v => {
    const origDept = v.original_department || v.originalDepartment;
    return origDept === 'FINANCE';
  });
  
  console.log(`📊 FINANCE vouchers: ${financeVouchers.length}`);
  
  // Find dispatched FINANCE vouchers
  const dispatchedFinanceVouchers = financeVouchers.filter(v => {
    return v.dispatched === true || v.dispatched === 1;
  });
  
  console.log(`📊 DISPATCHED FINANCE vouchers: ${dispatchedFinanceVouchers.length}`);
  
  if (dispatchedFinanceVouchers.length > 0) {
    console.log('📋 DISPATCHED FINANCE vouchers:');
    dispatchedFinanceVouchers.forEach((v, index) => {
      const voucherId = v.voucher_id || v.voucherId;
      const auditDispatchedBy = v.audit_dispatched_by || v.auditDispatchedBy;
      console.log(`   ${index + 1}. ${voucherId} - Dispatched by: ${auditDispatchedBy || 'N/A'}`);
      console.log(`      Status: ${v.status}`);
      console.log(`      Department: ${v.department}`);
      console.log(`      Original Department: ${v.original_department || v.originalDepartment}`);
      console.log(`      Dispatched: ${v.dispatched}`);
    });
  } else {
    console.log('❌ No dispatched FINANCE vouchers found in API data');
  }
  
  // Test the exact filtering logic used by the frontend
  console.log('\n🧪 TESTING FRONTEND FILTERING LOGIC');
  console.log('-----------------------------------');
  
  const department = 'FINANCE';
  const filteredVouchers = vouchers.filter(v => {
    const origDept = v.original_department || v.originalDepartment;
    const dispatched = v.dispatched;
    return origDept === department && dispatched === true;
  });
  
  console.log(`🔍 Frontend filter: originalDepartment === '${department}' && dispatched === true`);
  console.log(`📊 Filtered result: ${filteredVouchers.length} vouchers`);
  
  if (filteredVouchers.length > 0) {
    console.log('✅ Frontend filtering should work - vouchers found');
  } else {
    console.log('❌ Frontend filtering issue - no vouchers match filter');
    
    // Debug why filtering failed
    console.log('\n🔍 DEBUGGING FILTER FAILURE');
    console.log('---------------------------');
    
    financeVouchers.forEach((v, index) => {
      const voucherId = v.voucher_id || v.voucherId;
      const origDept = v.original_department || v.originalDepartment;
      const dispatched = v.dispatched;
      
      console.log(`${index + 1}. ${voucherId}:`);
      console.log(`   originalDepartment: ${origDept} (${typeof origDept})`);
      console.log(`   dispatched: ${dispatched} (${typeof dispatched})`);
      console.log(`   origDept === 'FINANCE': ${origDept === 'FINANCE'}`);
      console.log(`   dispatched === true: ${dispatched === true}`);
      console.log(`   dispatched === 1: ${dispatched === 1}`);
    });
  }
})
.catch(error => {
  console.error('❌ API Error:', error.message);
  console.log('💡 This might be due to authentication or server issues');
});

console.log('\n📝 INSTRUCTIONS:');
console.log('1. Copy this entire script');
console.log('2. Open browser console (F12)');
console.log('3. Navigate to Audit Dashboard');
console.log('4. Paste and run this script');
console.log('5. Check the output for data flow issues');
