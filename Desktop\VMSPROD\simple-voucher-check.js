const mysql = require('mysql2/promise');

async function simpleVoucherCheck() {
  console.log('🔍 SIMPLE VOUCHER DISPLAY CHECK...\n');
  
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });

  try {
    // Check vouchers
    console.log('📊 VOUCHER ANALYSIS:');
    const [vouchers] = await connection.execute(`
      SELECT id, voucher_id, claimant, department, original_department, status, sent_to_audit, deleted
      FROM vouchers 
      ORDER BY created_at DESC
    `);
    
    console.log(`Total vouchers: ${vouchers.length}`);
    console.log(`Non-deleted vouchers: ${vouchers.filter(v => !v.deleted).length}`);
    
    // Show recent vouchers
    console.log('\n📋 Recent vouchers:');
    vouchers.slice(0, 5).forEach((v, i) => {
      console.log(`${i+1}. ${v.voucher_id} - ${v.claimant}`);
      console.log(`   Department: ${v.department} | Original: ${v.original_department || 'N/A'}`);
      console.log(`   Status: ${v.status} | Deleted: ${v.deleted}`);
      console.log('   ---');
    });

    // Check FINANCE vouchers specifically
    console.log('\n📊 FINANCE VOUCHERS:');
    const financeVouchers = vouchers.filter(v => 
      !v.deleted && (v.department === 'FINANCE' || v.original_department === 'FINANCE')
    );
    console.log(`FINANCE vouchers (current or original): ${financeVouchers.length}`);
    
    financeVouchers.forEach((v, i) => {
      console.log(`${i+1}. ${v.voucher_id} - ${v.claimant}`);
      console.log(`   Current Dept: ${v.department} | Original: ${v.original_department || 'N/A'}`);
      console.log(`   Status: ${v.status}`);
    });

    // Check AUDIT vouchers
    console.log('\n📊 AUDIT VOUCHERS:');
    const auditVouchers = vouchers.filter(v => 
      !v.deleted && v.department === 'AUDIT'
    );
    console.log(`AUDIT vouchers: ${auditVouchers.length}`);
    
    auditVouchers.slice(0, 3).forEach((v, i) => {
      console.log(`${i+1}. ${v.voucher_id} - ${v.claimant}`);
      console.log(`   Original Dept: ${v.original_department || 'N/A'}`);
      console.log(`   Status: ${v.status}`);
    });

    // Check users table
    console.log('\n📊 USERS:');
    const [users] = await connection.execute('SELECT username, department FROM users WHERE is_active = TRUE');
    users.forEach(user => {
      console.log(`   ${user.username} - ${user.department}`);
    });

    // Check if departments table exists
    console.log('\n📊 TABLE CHECK:');
    try {
      const [deptTable] = await connection.execute("SHOW TABLES LIKE 'departments'");
      if (deptTable.length === 0) {
        console.log('❌ departments table is MISSING - this will cause authentication issues!');
      } else {
        console.log('✅ departments table exists');
      }
    } catch (e) {
      console.log('❌ Error checking departments table');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await connection.end();
  }
}

simpleVoucherCheck().catch(console.error);
