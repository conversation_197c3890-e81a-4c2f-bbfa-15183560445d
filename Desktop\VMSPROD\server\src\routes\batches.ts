import express from 'express';
import { v4 as uuidv4 } from 'uuid';
import { query, getTransaction } from '../database/db.js';
import { authenticate } from '../middleware/auth.js';
import { logger } from '../utils/logger.js';
import { VOUCHER_STATUSES, synchronizeVoucherFlags, isValidStatusTransition } from '../utils/voucherStatusFlow.js';
import { simpleEventBus } from '../events/simpleEventBus.js';
import { TransactionStatus } from '../types.js';

export const batchRouter = express.Router();

// Apply authentication middleware to all routes
batchRouter.use(authenticate);

// Get all batches
batchRouter.get('/', async (req, res) => {
  try {
    const { department } = req.query;

    let batches;
    if (department) {
      // If department is specified, filter by department
      batches = await query('SELECT * FROM voucher_batches WHERE department = ?', [department]) as any[];
    } else if (req.user.department === 'AUDIT' || req.user.department === 'SYSTEM ADMIN') {
      // Audit and Admin see all batches
      batches = await query('SELECT * FROM voucher_batches') as any[];
    } else {
      // Other departments see only their batches
      batches = await query('SELECT * FROM voucher_batches WHERE department = ?', [req.user.department]) as any[];
    }

    // Get vouchers for each batch
    for (const batch of batches) {
      const batchVouchers = await query(
        `SELECT v.* FROM vouchers v
         JOIN batch_vouchers bv ON v.id = bv.voucher_id
         WHERE bv.batch_id = ?`,
        [batch.id]
      ) as any[];

      batch.vouchers = batchVouchers;
      batch.voucherIds = batchVouchers.map((v: any) => v.id);
    }

    res.json(batches);
  } catch (error) {
    logger.error('Get batches error:', error);
    res.status(500).json({ error: 'Failed to get batches' });
  }
});

// Get batch by ID
batchRouter.get('/:id', async (req, res) => {
  const batchId = req.params.id;

  try {
    logger.info(`Fetching batch: ${batchId}`);

    // PRODUCTION FIX: Validate batch ID format
    if (!batchId || batchId.trim() === '') {
      logger.warn('Invalid batch ID provided');
      return res.status(400).json({ error: 'Invalid batch ID provided' });
    }

    // Get batch with timeout protection
    logger.info('Step 1: Fetching batch from database');
    const batches = await query('SELECT * FROM voucher_batches WHERE id = ?', [batchId]) as any[];

    if (batches.length === 0) {
      logger.warn(`Batch not found: ${batchId}`);
      return res.status(404).json({
        error: 'Batch not found',
        message: `Batch ${batchId} does not exist or has been removed`,
        batchId: batchId
      });
    }

    const batch = batches[0];
    logger.info(`Step 2: Batch found - ${batch.department} from ${batch.sent_by}`);

    // Check if user has access to this batch
    if (req.user.department !== 'AUDIT' && req.user.department !== 'SYSTEM ADMIN' && batch.department !== req.user.department) {
      logger.warn(`Access denied for user ${req.user.name} to batch ${batchId}`);
      return res.status(403).json({ error: 'Access denied' });
    }

    // Get vouchers in this batch with timeout protection
    logger.info('Step 3: Fetching vouchers in batch');
    const batchVouchers = await query(
      `SELECT v.* FROM vouchers v
       JOIN batch_vouchers bv ON v.id = bv.voucher_id
       WHERE bv.batch_id = ?`,
      [batchId]
    ) as any[];

    logger.info(`Step 4: Found ${batchVouchers.length} vouchers in batch`);

    batch.vouchers = batchVouchers;
    batch.voucherIds = batchVouchers.map((v: any) => v.id);

    // PRODUCTION FIX: Add additional metadata for better error handling
    batch.voucherCount = batchVouchers.length;
    batch.hasVouchers = batchVouchers.length > 0;

    logger.info(`Batch ${batchId} fetched successfully with ${batchVouchers.length} vouchers`);
    res.json(batch);
  } catch (error) {
    logger.error('Get batch error:', error);
    res.status(500).json({
      error: 'Failed to get batch',
      message: 'An internal server error occurred while fetching the batch',
      batchId: batchId
    });
  }
});

// Create batch
batchRouter.post('/', async (req, res) => {
  const connection = await getTransaction();

  try {
    const { department, voucherIds, fromAudit = false } = req.body;

    // Validate required fields
    if (!department || !voucherIds || !Array.isArray(voucherIds) || voucherIds.length === 0) {
      return res.status(400).json({ error: 'Department and voucher IDs are required' });
    }

    // Check if user has access to create batches for this department
    if (req.user.department !== 'AUDIT' && req.user.department !== 'SYSTEM ADMIN' && department !== req.user.department) {
      return res.status(403).json({ error: 'Access denied' });
    }

    // ARCHITECTURAL FIX: Enhanced voucher validation with detailed error reporting
    for (const voucherId of voucherIds) {
      // Validate voucher ID format (should be UUID)
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(voucherId)) {
        await connection.rollback();
        return res.status(400).json({
          error: `Invalid voucher ID format: ${voucherId}. Expected UUID format.`,
          details: 'Voucher IDs must be valid UUIDs. If you see timestamp-based IDs (e.g., v1234567890), this indicates a system configuration issue.'
        });
      }

      const vouchers = await connection.query(
        'SELECT * FROM vouchers WHERE id = ? AND deleted = FALSE',
        [voucherId]
      ) as any[];

      if (vouchers[0].length === 0) {
        await connection.rollback();
        return res.status(404).json({
          error: `Voucher with ID ${voucherId} not found`,
          details: 'The voucher may have been deleted or the ID is incorrect. Please refresh the page and try again.'
        });
      }

      const voucher = vouchers[0][0];

      if (voucher.department !== department && !fromAudit) {
        await connection.rollback();
        return res.status(400).json({ error: `Voucher with ID ${voucherId} does not belong to department ${department}` });
      }
    }

    // Create batch
    const batchId = uuidv4();
    await connection.query(
      'INSERT INTO voucher_batches (id, department, sent_by, sent_time, received, from_audit) VALUES (?, ?, ?, NOW(), ?, ?)',
      [batchId, department, req.user.name, false, fromAudit]
    );

    // Add vouchers to batch
    for (const voucherId of voucherIds) {
      await connection.query(
        'INSERT INTO batch_vouchers (batch_id, voucher_id) VALUES (?, ?)',
        [batchId, voucherId]
      );

      // Get the appropriate status based on direction
      const newStatus = fromAudit ? VOUCHER_STATUSES.VOUCHER_PROCESSING : VOUCHER_STATUSES.PENDING_RECEIPT;
      const { flags } = synchronizeVoucherFlags({ status: newStatus });

      // CRITICAL FIX: Update voucher status, flags, AND sent_to_audit flag for proper workflow
      if (fromAudit) {
        // Vouchers coming FROM audit TO department
        logger.info(`📤 Batch: Updating voucher ${voucherId} - FROM AUDIT TO ${department} - Status: ${newStatus}`);
        await connection.query(
          'UPDATE vouchers SET batch_id = ?, status = ?, flags = ?, audit_dispatch_time = NOW(), audit_dispatched_by = ? WHERE id = ?',
          [batchId, newStatus, JSON.stringify(flags), req.user.name, voucherId]
        );
      } else {
        // Vouchers going FROM department TO audit - MUST set sent_to_audit = TRUE
        logger.info(`📥 Batch: Updating voucher ${voucherId} - FROM ${department} TO AUDIT - Status: ${newStatus}, sent_to_audit: TRUE`);
        await connection.query(
          'UPDATE vouchers SET batch_id = ?, status = ?, flags = ?, sent_to_audit = TRUE, dispatch_to_audit_by = ?, dispatch_time = NOW() WHERE id = ?',
          [batchId, newStatus, JSON.stringify(flags), req.user.name, voucherId]
        );
      }
    }

    // CRITICAL FIX: Only create notifications for the correct recipient
    // When fromAudit = true, vouchers are going FROM Audit TO Department
    // When fromAudit = false, vouchers are going FROM Department TO Audit
    if (fromAudit) {
      // Vouchers going FROM Audit TO Department - notify the DEPARTMENT
      logger.info(`📤 Creating notification for ${department} department (vouchers from Audit)`);
      const notificationId = uuidv4();
      await connection.query(
        `INSERT INTO notifications (
          id, user_id, message, is_read, timestamp, batch_id, type, from_audit
        ) VALUES (?, ?, ?, ?, NOW(), ?, ?, ?)`,
        [
          notificationId,
          department,  // Notify the DEPARTMENT, not Audit
          `New batch received from Audit`,
          false,
          batchId,
          'NEW_BATCH',
          true  // fromAudit = true
        ]
      );
    } else {
      // Vouchers going FROM Department TO Audit - notify AUDIT
      logger.info(`📤 Creating notification for AUDIT department (vouchers from ${department})`);
      const notificationId = uuidv4();
      await connection.query(
        `INSERT INTO notifications (
          id, user_id, message, is_read, timestamp, batch_id, type, from_audit
        ) VALUES (?, ?, ?, ?, NOW(), ?, ?, ?)`,
        [
          notificationId,
          'AUDIT',  // Notify AUDIT department
          `New batch received from ${department}`,
          false,
          batchId,
          'NEW_BATCH',
          false  // fromAudit = false
        ]
      );
    }

    // Commit transaction
    await connection.commit();

    // Get created batch with vouchers
    const batches = await query('SELECT * FROM voucher_batches WHERE id = ?', [batchId]) as any[];
    const batchVouchers = await query(
      `SELECT v.* FROM vouchers v
       JOIN batch_vouchers bv ON v.id = bv.voucher_id
       WHERE bv.batch_id = ?`,
      [batchId]
    ) as any[];

    const result = batches[0];
    result.vouchers = batchVouchers;
    result.voucherIds = batchVouchers.map((v: any) => v.id);

    // PERMANENT SOLUTION: Use simple event bus to avoid circular dependencies
    // Emit batch creation event
    simpleEventBus.emitBatchCreated(result);
    logger.info(`📡 Emitted batch creation event for ${batchId}`);

    // Emit notification creation event
    const targetDepartment = fromAudit ? department : 'AUDIT';
    const notificationMessage = fromAudit ?
      `New batch received from Audit` :
      `New batch received from ${department}`;

    simpleEventBus.emitNotificationCreated({
      id: `notification-${Date.now()}`,
      user_id: targetDepartment,
      message: notificationMessage,
      is_read: false,
      timestamp: new Date().toISOString(),
      batch_id: batchId,
      type: 'NEW_BATCH',
      from_audit: fromAudit
    });
    logger.info(`📡 Emitted notification event for ${targetDepartment} department`);

    logger.info(`✅ Successfully processed batch ${batchId} with ${batchVouchers.length} vouchers`);

    res.status(201).json(result);
  } catch (error) {
    await connection.rollback();
    logger.error('Create batch error:', error);
    res.status(500).json({ error: 'Failed to create batch' });
  } finally {
    connection.release();
  }
});

// Receive batch
batchRouter.post('/:id/receive', async (req, res) => {
  const connection = await getTransaction();

  try {
    const batchId = req.params.id;
    const { receivedVoucherIds = [], rejectedVoucherIds = [], rejectionComments = {} } = req.body;

    // Get batch
    const batches = await connection.query('SELECT * FROM voucher_batches WHERE id = ?', [batchId]) as any[];

    if (batches[0].length === 0) {
      await connection.rollback();
      return res.status(404).json({ error: 'Batch not found' });
    }

    const receiveBatch = batches[0][0];

    // Check if user has access to receive this batch
    const isFromAudit = receiveBatch.from_audit;
    if (isFromAudit) {
      // If batch is from Audit, only the department can receive it
      if (req.user.department !== receiveBatch.department && req.user.department !== 'SYSTEM ADMIN') {
        await connection.rollback();
        return res.status(403).json({ error: 'Access denied' });
      }
    } else {
      // If batch is from department to Audit, only Audit can receive it
      if (req.user.department !== 'AUDIT' && req.user.department !== 'SYSTEM ADMIN') {
        await connection.rollback();
        return res.status(403).json({ error: 'Access denied' });
      }
    }

    // Mark batch as received
    await connection.query(
      'UPDATE voucher_batches SET received = TRUE WHERE id = ?',
      [batchId]
    );

    // Get all vouchers in this batch
    const batchVouchers = await connection.query(
      `SELECT v.* FROM vouchers v
       JOIN batch_vouchers bv ON v.id = bv.voucher_id
       WHERE bv.batch_id = ?`,
      [batchId]
    ) as any[];

    const allVoucherIds = batchVouchers[0].map((v: any) => v.id);

    // Process received vouchers
    for (const voucherId of receivedVoucherIds) {
      if (!allVoucherIds.includes(voucherId)) {
        continue; // Skip if voucher is not in this batch
      }

      // Get current voucher state
      const [currentVoucher] = await connection.query(
        'SELECT * FROM vouchers WHERE id = ? FOR UPDATE',
        [voucherId]
      ) as any[];

      if (!currentVoucher[0]) {
        continue;
      }

      const voucher = currentVoucher[0];

      // WORKFLOW FIX: Get the appropriate status based on direction and workflow
      let newStatus: TransactionStatus;
      if (isFromAudit) {
        // Batch from Audit to Department - vouchers are being RECEIVED by department
        // WORKFLOW: When department receives vouchers from audit, they become CERTIFIED
        if (voucher.status === 'VOUCHER REJECTED') {
          newStatus = VOUCHER_STATUSES.VOUCHER_REJECTED; // Keep rejected status
        } else if (voucher.pending_return || voucher.is_returned) {
          newStatus = VOUCHER_STATUSES.VOUCHER_PROCESSING; // Returned vouchers go back to processing
        } else {
          // CRITICAL FIX: Normal vouchers become CERTIFIED when department receives them
          newStatus = VOUCHER_STATUSES.VOUCHER_CERTIFIED;
        }
      } else {
        // Batch from Department to Audit - vouchers are being ACCEPTED by audit
        // When audit receives vouchers, they should go to AUDIT: PROCESSING status
        newStatus = VOUCHER_STATUSES.AUDIT_PROCESSING;
      }
      const { flags } = synchronizeVoucherFlags({ status: newStatus });

      // Validate the status transition
      // CRITICAL FIX: Safe JSON parsing for voucher flags
      let currentFlags: any = {};
      if (voucher.flags) {
        try {
          // If flags is already an object, use it directly
          if (typeof voucher.flags === 'object') {
            currentFlags = voucher.flags;
          } else {
            // If flags is a string, try to parse it
            currentFlags = JSON.parse(voucher.flags);
          }
        } catch (error) {
          logger.warn(`Invalid flags JSON for voucher ${voucherId}: ${voucher.flags}. Using empty flags.`);
          currentFlags = {};
        }
      }

      // CRITICAL DEBUG: Log validation parameters
      logger.info(`🔍 VALIDATION DEBUG: Checking transition for voucher ${voucherId}:`, {
        currentStatus: voucher.status,
        newStatus: newStatus,
        userRole: req.user.role,
        userDepartment: req.user.department,
        userName: req.user.name,
        currentFlags: currentFlags
      });

      // DUAL ROLE SYSTEM: Validate the status transition with both role and department
      const { isValid, reason } = isValidStatusTransition(
        voucher.status,
        newStatus,
        req.user.role,
        currentFlags,
        req.user.department  // Pass department for dual role validation
      );

      if (!isValid) {
        logger.warn(`🚫 DUAL ROLE SYSTEM: Invalid status transition attempted: ${voucher.status} -> ${newStatus}: ${reason}`, {
          userRole: req.user.role,
          userDepartment: req.user.department,
          userName: req.user.name,
          voucherId: voucher.id
        });
        continue;
      }

      // Log the status change
      logger.info(`🔄 AUDIT ACCEPTANCE: Changing voucher ${voucherId} status: ${voucher.status} → ${newStatus} (received by ${req.user.name})`);
      logger.info(`🔄 AUDIT ACCEPTANCE: Setting received_by_audit = ${!isFromAudit} for voucher ${voucherId}`);

      // ARCHITECTURAL FIX: Update voucher with proper audit processing fields (using existing schema)
      if (isFromAudit) {
        // Vouchers being received by department from audit - restore to original department
        await connection.query(
          `UPDATE vouchers SET
           status = ?,
           flags = ?,
           department = COALESCE(original_department, department),
           received_by = ?,
           receipt_time = NOW()
           WHERE id = ?`,
          [newStatus, JSON.stringify(flags), req.user.name, voucherId]
        );
      } else {
        // CRITICAL FIX: Vouchers being RECEIVED by audit from department - transfer to AUDIT department
        // PRESERVE original department for proper tab filtering
        await connection.query(
          `UPDATE vouchers SET
           status = ?,
           flags = ?,
           department = 'AUDIT',
           original_department = COALESCE(original_department, department),
           received_by = ?,
           receipt_time = NOW(),
           received_by_audit = TRUE,
           pre_audited_amount = NULL,
           pre_audited_by = NULL,
           certified_by = NULL,
           work_started = FALSE,
           comment = NULL
           WHERE id = ?`,
          [newStatus, JSON.stringify(flags), req.user.name, voucherId]
        );
      }

      logger.info(`✅ AUDIT PROCESSING: Successfully updated voucher ${voucherId} with status ${newStatus}`);

      // ARCHITECTURAL FIX: Create appropriate notifications based on processing type
      if (!isFromAudit) {
        // Vouchers accepted/certified by audit - notify department
        const notificationId = uuidv4();
        await connection.query(
          `INSERT INTO notifications (
            id, user_id, message, is_read, timestamp, voucher_id, type
          ) VALUES (?, ?, ?, ?, NOW(), ?, ?)`,
          [
            notificationId,
            voucher.department,
            `Voucher ${voucher.voucher_id} certified by Audit`,
            false,
            voucherId,
            'VOUCHER_CERTIFIED'
          ]
        );
      } else {
        // Vouchers received by department from audit - notify department
        const notificationId = uuidv4();
        await connection.query(
          `INSERT INTO notifications (
            id, user_id, message, is_read, timestamp, voucher_id, type
          ) VALUES (?, ?, ?, ?, NOW(), ?, ?)`,
          [
            notificationId,
            voucher.department,
            `Voucher ${voucher.voucher_id} received from Audit`,
            false,
            voucherId,
            'VOUCHER_RECEIVED'
          ]
        );
      }
    }

    // Process rejected vouchers
    for (const voucherId of rejectedVoucherIds) {
      if (!allVoucherIds.includes(voucherId)) {
        continue; // Skip if voucher is not in this batch
      }

      const comment = rejectionComments[voucherId] || '';

      // ARCHITECTURAL FIX: Use VOUCHER_REJECTED status so rejected vouchers appear in department's rejected tab
      const newStatus = VOUCHER_STATUSES.VOUCHER_REJECTED;
      const { flags } = synchronizeVoucherFlags({ status: newStatus });

      // Update voucher status and flags with proper rejection tracking (using existing schema)
      await connection.query(
        'UPDATE vouchers SET status = ?, flags = ?, rejected_by = ?, rejection_time = NOW(), comment = ? WHERE id = ?',
        [VOUCHER_STATUSES.VOUCHER_REJECTED, JSON.stringify(flags), req.user.name, comment, voucherId]
      );

      // Create notification for department
      const voucher = batchVouchers[0].find((v: any) => v.id === voucherId);
      if (voucher) {
        const notificationId = uuidv4();
        await connection.query(
          `INSERT INTO notifications (
            id, user_id, message, is_read, timestamp, voucher_id, type, from_audit
          ) VALUES (?, ?, ?, ?, NOW(), ?, ?, ?)`,
          [
            notificationId,
            isFromAudit ? 'AUDIT' : voucher.department,
            `Voucher ${voucher.voucher_id} rejected`,
            false,
            voucherId,
            'VOUCHER_REJECTED',
            !isFromAudit
          ]
        );
      }
    }

    // ARCHITECTURAL FIX: Mark batch as received and create return batch for certified vouchers
    await connection.query(
      'UPDATE voucher_batches SET received = TRUE WHERE id = ?',
      [batchId]
    );

    // ARCHITECTURAL FIX: Create return batches for both certified and rejected vouchers
    const returnBatch = batches[0][0];

    // Create return batch for certified vouchers (when audit accepts vouchers)
    if (!isFromAudit && receivedVoucherIds.length > 0) {
      const returnBatchId = uuidv4();

      await connection.query(
        `INSERT INTO voucher_batches (
          id, department, sent_by, sent_time, received, from_audit
        ) VALUES (?, ?, ?, NOW(), FALSE, TRUE)`,
        [
          returnBatchId,
          returnBatch.department,
          req.user.name
        ]
      );

      // Add vouchers to the return batch
      for (const voucherId of receivedVoucherIds) {
        await connection.query(
          'INSERT INTO batch_vouchers (batch_id, voucher_id) VALUES (?, ?)',
          [returnBatchId, voucherId]
        );
      }

      logger.info(`✅ Created return batch ${returnBatchId} for ${receivedVoucherIds.length} certified vouchers to ${returnBatch.department}`);
    }

    // Create return batch for rejected vouchers (when audit rejects vouchers)
    if (!isFromAudit && rejectedVoucherIds.length > 0) {
      const rejectionBatchId = uuidv4();

      await connection.query(
        `INSERT INTO voucher_batches (
          id, department, sent_by, sent_time, received, from_audit
        ) VALUES (?, ?, ?, NOW(), FALSE, TRUE)`,
        [
          rejectionBatchId,
          returnBatch.department,
          req.user.name
        ]
      );

      // Add vouchers to the rejection batch
      for (const voucherId of rejectedVoucherIds) {
        await connection.query(
          'INSERT INTO batch_vouchers (batch_id, voucher_id) VALUES (?, ?)',
          [rejectionBatchId, voucherId]
        );
      }

      logger.info(`✅ Created rejection batch ${rejectionBatchId} for ${rejectedVoucherIds.length} rejected vouchers to ${returnBatch.department}`);
    }

    // Commit transaction
    await connection.commit();

    // Get updated batch with vouchers
    const updatedBatches = await query('SELECT * FROM voucher_batches WHERE id = ?', [batchId]) as any[];
    const updatedBatchVouchers = await query(
      `SELECT v.* FROM vouchers v
       JOIN batch_vouchers bv ON v.id = bv.voucher_id
       WHERE bv.batch_id = ?`,
      [batchId]
    ) as any[];

    const result = updatedBatches[0];
    result.vouchers = updatedBatchVouchers;
    result.voucherIds = updatedBatchVouchers.map((v: any) => v.id);

    res.status(200).json(result);
  } catch (error) {
    await connection.rollback();
    logger.error('Receive batch error:', error);
    res.status(500).json({ error: 'Failed to receive batch' });
  } finally {
    connection.release();
  }
});