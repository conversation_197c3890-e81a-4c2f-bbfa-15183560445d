const mysql = require('mysql2/promise');

async function testCurrentAPIState() {
  console.log('🧪 TESTING CURRENT API STATE');
  console.log('='.repeat(40));

  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'vms@2025@1989',
      database: 'vms_production'
    });
    
    console.log('✅ Connected to database');

    // Test what the API returns for batches
    console.log('\n1. WHAT API RETURNS FOR BATCHES:');
    console.log('-'.repeat(40));
    
    const [allBatches] = await connection.execute(`
      SELECT 
        id,
        department,
        sent_by,
        sent_time,
        received,
        from_audit
      FROM voucher_batches 
      ORDER BY sent_time DESC
    `);

    console.log(`📦 Total batches in API: ${allBatches.length}`);
    
    // Simulate what AUDIT dashboard should get
    const auditBatches = allBatches.filter(batch => !batch.from_audit);
    console.log(`🏛️  AUDIT should get: ${auditBatches.length} batches`);
    auditBatches.forEach((batch, index) => {
      console.log(`   ${index + 1}. FROM ${batch.department} (${batch.received ? 'RECEIVED' : 'UNRECEIVED'})`);
    });
    
    // Simulate what FINANCE dashboard should get
    const financeBatches = allBatches.filter(batch => batch.department === 'FINANCE');
    console.log(`\n💰 FINANCE should get: ${financeBatches.length} batches`);
    financeBatches.forEach((batch, index) => {
      const direction = batch.from_audit ? 'FROM AUDIT' : 'TO AUDIT';
      console.log(`   ${index + 1}. ${direction} (${batch.received ? 'RECEIVED' : 'UNRECEIVED'})`);
    });

    // Check what should appear in each dashboard
    console.log('\n2. WHAT SHOULD APPEAR IN DASHBOARDS:');
    console.log('-'.repeat(40));
    
    const auditShouldShow = allBatches.filter(batch => !batch.from_audit && !batch.received);
    const financeShouldShow = allBatches.filter(batch => 
      batch.department === 'FINANCE' && batch.from_audit && !batch.received
    );
    
    console.log(`🏛️  AUDIT DASHBOARD should show: ${auditShouldShow.length} unreceived batches`);
    if (auditShouldShow.length === 0) {
      console.log('   ✅ Should be EMPTY - no batches to receive');
    }
    
    console.log(`💰 FINANCE DASHBOARD should show: ${financeShouldShow.length} unreceived batches`);
    if (financeShouldShow.length > 0) {
      console.log('   ✅ Should show "NEWLY ARRIVED VOUCHERS FROM AUDIT" notification');
    }

    // Check for the specific issue
    console.log('\n3. ISSUE DIAGNOSIS:');
    console.log('-'.repeat(40));
    
    if (auditShouldShow.length === 0 && financeShouldShow.length > 0) {
      console.log('🎯 ISSUE CONFIRMED:');
      console.log('   - Audit dashboard should be EMPTY (no batches to receive)');
      console.log('   - Finance dashboard should show 6 batches from audit');
      console.log('   - If Audit dashboard is showing batches, it\'s a frontend caching issue');
      console.log('');
      console.log('🔧 SOLUTION:');
      console.log('   1. User needs to logout completely from Audit');
      console.log('   2. Login as Finance user to see the batches');
      console.log('   3. Clear browser cache if needed');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n✅ Database connection closed');
    }
  }
}

testCurrentAPIState();
