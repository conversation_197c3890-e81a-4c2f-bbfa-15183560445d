import express from 'express';
import { v4 as uuidv4 } from 'uuid';
import { query, getTransaction } from '../database/db.js';
import { authenticate } from '../middleware/auth.js';
import { logger } from '../utils/logger.js';
import { broadcastVoucherUpdate, broadcastDataUpdate, broadcastNotificationUpdate } from '../socket/socketHandlers.js';
import { VOUCHER_STATUSES, synchronizeVoucherFlags, isValidStatusTransition } from '../utils/voucherStatusFlow.js';

export const voucherRouter = express.Router();

// Apply authentication middleware to all routes
voucherRouter.use(authenticate);

// Removed seeding endpoint - fixing root causes instead

// Get all vouchers
voucherRouter.get('/', async (req, res) => {
  try {
    const { department } = req.query;
    const timestamp = new Date().toISOString();

    logger.info(`GET /vouchers request at ${timestamp} by user ${req.user.name} (${req.user.department}), query department: ${department || 'none'}`);

    let vouchers;
    if (department) {
      // If department is specified, get vouchers that belong to OR originally came from that department
      logger.info(`Fetching vouchers for specified department: ${department}`);
      vouchers = await query('SELECT * FROM vouchers WHERE (department = ? OR original_department = ?) AND deleted = FALSE ORDER BY voucher_id', [department, department]) as any[];
    } else if (req.user.department === 'AUDIT' || req.user.department === 'SYSTEM ADMIN') {
      // Audit and Admin see all vouchers
      logger.info(`Fetching all vouchers for ${req.user.department} user`);
      vouchers = await query('SELECT * FROM vouchers WHERE deleted = FALSE ORDER BY department, voucher_id') as any[];
    } else {
      // Other departments see vouchers that belong to OR originally came from their department
      logger.info(`Fetching vouchers for user's department: ${req.user.department}`);
      vouchers = await query('SELECT * FROM vouchers WHERE (department = ? OR original_department = ?) AND deleted = FALSE ORDER BY voucher_id', [req.user.department, req.user.department]) as any[];
    }

    // Log the number of vouchers found
    logger.info(`Found ${vouchers.length} vouchers for request from ${req.user.name} (${req.user.department})`);

    // If no vouchers found, check if there should be any
    if (vouchers.length === 0) {
      const deptToCheck = department || req.user.department;
      if (deptToCheck !== 'AUDIT' && deptToCheck !== 'SYSTEM ADMIN') {
        // Check if there are any vouchers for this department at all
        const voucherCount = await query('SELECT COUNT(*) as count FROM vouchers WHERE department = ?', [deptToCheck]) as any[];
        if (voucherCount[0].count > 0) {
          logger.warn(`No non-deleted vouchers found for department ${deptToCheck}, but ${voucherCount[0].count} total vouchers exist`);
        }
      }
    }

    // Check for gaps in voucher IDs
    if (vouchers.length > 0) {
      // Group vouchers by department
      const vouchersByDept: Record<string, any[]> = {};
      for (const voucher of vouchers) {
        if (!vouchersByDept[voucher.department]) {
          vouchersByDept[voucher.department] = [];
        }
        vouchersByDept[voucher.department].push(voucher);
      }

      // Check each department for gaps
      for (const [dept, deptVouchers] of Object.entries(vouchersByDept)) {
        // Group by month prefix
        const vouchersByPrefix: Record<string, string[]> = {};
        for (const voucher of deptVouchers) {
          if (!voucher.voucher_id) continue;

          const prefix = voucher.voucher_id.substring(0, 3);
          if (!vouchersByPrefix[prefix]) {
            vouchersByPrefix[prefix] = [];
          }
          vouchersByPrefix[prefix].push(voucher.voucher_id);
        }

        // Check each prefix group for gaps
        for (const [prefix, ids] of Object.entries(vouchersByPrefix)) {
          if (ids.length <= 1) continue;

          // Extract numeric parts
          const numericParts = ids.map((id: string) => parseInt(id.substring(3)));
          numericParts.sort((a: number, b: number) => a - b);

          // Check for gaps
          for (let i = 1; i < numericParts.length; i++) {
            if (numericParts[i] - numericParts[i-1] > 1) {
              logger.warn(`Gap detected in ${dept} voucher sequence: ${prefix}${numericParts[i-1].toString().padStart(5, '0')} -> ${prefix}${numericParts[i].toString().padStart(5, '0')}`);
            }
          }
        }
      }
    }

    // Log the first few vouchers for debugging
    if (vouchers.length > 0) {
      const sampleVouchers = vouchers.slice(0, 3).map(v => `${v.voucher_id} (${v.id}): ${v.status}`);
      logger.info(`Sample vouchers: ${sampleVouchers.join(', ')}`);
    }

    // FIXED: Transform database fields to client-compatible format
    const transformedVouchers = vouchers.map(voucher => ({
      ...voucher,
      // Map snake_case to camelCase for client compatibility
      voucherId: voucher.voucher_id,
      originalDepartment: voucher.original_department, // 🔧 CRITICAL FIX: Add missing originalDepartment transformation
      createdBy: voucher.created_by,
      dispatchedBy: voucher.dispatched_by,
      dispatchTime: voucher.dispatch_time,
      sentToAudit: Boolean(voucher.sent_to_audit),
      batchId: voucher.batch_id,
      receivedBy: voucher.received_by,
      receivedByAudit: Boolean(voucher.received_by_audit), // 🔧 CRITICAL FIX: Add missing receivedByAudit transformation
      receiptTime: voucher.receipt_time,
      taxType: voucher.tax_type,
      taxDetails: voucher.tax_details,
      taxAmount: voucher.tax_amount,
      preAuditedAmount: voucher.pre_audited_amount,
      preAuditedBy: voucher.pre_audited_by,
      certifiedBy: voucher.certified_by,
      auditDispatchTime: voucher.audit_dispatch_time,
      auditDispatchedBy: voucher.audit_dispatched_by,
      dispatchToOnDepartment: Boolean(voucher.dispatch_to_on_department),
      postProvisionalCash: Boolean(voucher.post_provisional_cash),
      dispatched: Boolean(voucher.dispatched),
      dispatchToAuditBy: voucher.dispatch_to_audit_by,
      isReturned: Boolean(voucher.is_returned),
      returnComment: voucher.return_comment,
      returnTime: voucher.return_time,
      deleted: Boolean(voucher.deleted),
      deletionTime: voucher.deletion_time,
      rejectionTime: voucher.rejection_time,
      departmentReceiptTime: voucher.department_receipt_time,
      departmentReceivedBy: voucher.department_received_by,
      departmentRejected: Boolean(voucher.department_rejected),
      rejectedBy: voucher.rejected_by,
      pendingReturn: Boolean(voucher.pending_return),
      returnInitiatedTime: voucher.return_initiated_time,
      referenceId: voucher.reference_id,
      idempotencyKey: voucher.idempotency_key,
      workStarted: Boolean(voucher.work_started), // CRITICAL FIX: Map workStarted field
      createdAt: voucher.created_at
    }));

    res.json(transformedVouchers);
  } catch (error) {
    logger.error('Get vouchers error:', error);
    res.status(500).json({ error: 'Failed to get vouchers' });
  }
});

// Get voucher by ID
voucherRouter.get('/:id', async (req, res) => {
  try {
    const voucherId = req.params.id;

    // Get voucher
    const vouchers = await query('SELECT * FROM vouchers WHERE id = ? AND deleted = FALSE', [voucherId]) as any[];

    if (vouchers.length === 0) {
      return res.status(404).json({ error: 'Voucher not found' });
    }

    const voucher = vouchers[0];

    // Check if user has access to this voucher
    if (req.user.department !== 'AUDIT' && req.user.department !== 'SYSTEM ADMIN' && voucher.department !== req.user.department) {
      return res.status(403).json({ error: 'Access denied' });
    }

    // FIXED: Transform database fields to client-compatible format
    const transformedVoucher = {
      ...voucher,
      // Map snake_case to camelCase for client compatibility
      voucherId: voucher.voucher_id,
      originalDepartment: voucher.original_department, // CRITICAL FIX: Include original department
      createdBy: voucher.created_by,
      dispatchedBy: voucher.dispatched_by,
      dispatchTime: voucher.dispatch_time,
      sentToAudit: Boolean(voucher.sent_to_audit),
      batchId: voucher.batch_id,
      receivedBy: voucher.received_by,
      receivedByAudit: Boolean(voucher.received_by_audit), // CRITICAL FIX: Include audit receipt status
      receiptTime: voucher.receipt_time,
      taxType: voucher.tax_type,
      taxDetails: voucher.tax_details,
      taxAmount: voucher.tax_amount,
      preAuditedAmount: voucher.pre_audited_amount,
      preAuditedBy: voucher.pre_audited_by,
      certifiedBy: voucher.certified_by,
      auditDispatchTime: voucher.audit_dispatch_time,
      auditDispatchedBy: voucher.audit_dispatched_by,
      dispatchToOnDepartment: Boolean(voucher.dispatch_to_on_department),
      postProvisionalCash: Boolean(voucher.post_provisional_cash),
      dispatched: Boolean(voucher.dispatched),
      dispatchToAuditBy: voucher.dispatch_to_audit_by,
      isReturned: Boolean(voucher.is_returned),
      returnComment: voucher.return_comment,
      returnTime: voucher.return_time,
      deleted: Boolean(voucher.deleted),
      deletionTime: voucher.deletion_time,
      rejectionTime: voucher.rejection_time,
      departmentReceiptTime: voucher.department_receipt_time,
      departmentReceivedBy: voucher.department_received_by,
      departmentRejected: Boolean(voucher.department_rejected),
      rejectedBy: voucher.rejected_by,
      pendingReturn: Boolean(voucher.pending_return),
      returnInitiatedTime: voucher.return_initiated_time,
      referenceId: voucher.reference_id,
      idempotencyKey: voucher.idempotency_key,
      workStarted: Boolean(voucher.work_started), // CRITICAL FIX: Map workStarted field
      createdAt: voucher.created_at
    };

    res.json(transformedVoucher);
  } catch (error) {
    logger.error('Get voucher error:', error);
    res.status(500).json({ error: 'Failed to get voucher' });
  }
});

// OLD generateVoucherId function REMOVED to prevent conflicts
// Now using department-specific voucher ID generation inline in the POST endpoint

// Create voucher with comprehensive duplicate prevention
voucherRouter.post('/', async (req, res) => {
  try {
    const {
      claimant,
      description,
      amount,
      currency,
      department,
      taxType,
      taxDetails,
      taxAmount,
      comment,
      idempotencyKey
    } = req.body;

    // Validate required fields
    if (!claimant || !description || !amount || !currency || !department) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // CRITICAL FIX: Enhanced duplicate prevention
    // Check for recent duplicate submissions (same user, description, amount within 5 minutes)
    const recentDuplicates = await query(`
      SELECT * FROM vouchers
      WHERE created_by = ?
        AND description = ?
        AND amount = ?
        AND department = ?
        AND created_at >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)
        AND deleted = FALSE
      ORDER BY created_at DESC
      LIMIT 1
    `, [req.user.id, description, amount, department]) as any[];

    if (recentDuplicates.length > 0) {
      logger.warn(`Duplicate voucher creation blocked: ${description} by ${req.user.name}`);
      return res.status(200).json({
        ...recentDuplicates[0],
        message: 'Voucher already exists - returning existing voucher'
      });
    }

    // CORRECT PERMISSION LOGIC: Enhanced permission check with detailed logging
    logger.info('🔐 VMSPROD VOUCHER CREATION - Permission check:', {
      userDepartment: req.user.department,
      requestedDepartment: department,
      userRole: req.user.role,
      userName: req.user.name,
      isAudit: req.user.department === 'AUDIT',
      isSystemAdmin: req.user.department === 'SYSTEM ADMIN',
      departmentMatch: department === req.user.department,
      isUserRole: req.user.role === 'USER',
      isViewerRole: req.user.role === 'VIEWER'
    });

    // CORRECT PERMISSION LOGIC:
    // 1. VIEWER role cannot create vouchers (only view)
    // 2. USER role can create vouchers for their own department
    // 3. SYSTEM ADMIN can create vouchers for any department
    // 4. AUDIT does NOT create vouchers - they receive and process them
    const canCreateVoucher = (
      req.user.role === 'USER' && department === req.user.department ||  // USER role for own department
      req.user.department === 'SYSTEM ADMIN'  // SYSTEM ADMIN for any department
    );

    // Block VIEWER role completely
    if (req.user.role === 'VIEWER') {
      logger.warn('🚫 VMSPROD VOUCHER CREATION - Access denied for VIEWER role:', {
        userDepartment: req.user.department,
        requestedDepartment: department,
        userRole: req.user.role,
        reason: 'VIEWER role cannot create vouchers'
      });
      return res.status(403).json({
        error: 'Access denied. VIEWER role cannot create vouchers.',
        details: {
          userDepartment: req.user.department,
          requestedDepartment: department,
          userRole: req.user.role
        }
      });
    }

    // Block AUDIT department from creating vouchers
    if (req.user.department === 'AUDIT') {
      logger.warn('🚫 VMSPROD VOUCHER CREATION - Access denied for AUDIT department:', {
        userDepartment: req.user.department,
        requestedDepartment: department,
        userRole: req.user.role,
        reason: 'AUDIT department does not create vouchers - they receive and process them'
      });
      return res.status(403).json({
        error: 'Access denied. AUDIT department does not create vouchers.',
        details: {
          userDepartment: req.user.department,
          requestedDepartment: department,
          userRole: req.user.role
        }
      });
    }

    // Check general permission
    if (!canCreateVoucher) {
      logger.warn('🚫 VMSPROD VOUCHER CREATION - Access denied:', {
        userDepartment: req.user.department,
        requestedDepartment: department,
        userRole: req.user.role,
        reason: 'User does not have permission to create vouchers for this department'
      });
      return res.status(403).json({
        error: 'Access denied. You can only create vouchers for your own department.',
        details: {
          userDepartment: req.user.department,
          requestedDepartment: department,
          userRole: req.user.role
        }
      });
    }

    logger.info('✅ VMSPROD VOUCHER CREATION - Permission granted for user:', {
      userName: req.user.name,
      userDepartment: req.user.department,
      userRole: req.user.role,
      targetDepartment: department
    });

    // PRODUCTION: Enhanced idempotency protection
    if (idempotencyKey) {
      const existingVouchers = await query(
        'SELECT * FROM vouchers WHERE created_by = ? AND department = ? AND idempotency_key = ? AND deleted = FALSE',
        [req.user.id, department, idempotencyKey]
      ) as any[];

      if (existingVouchers.length > 0) {
        logger.info(`Duplicate voucher creation attempt blocked for user ${req.user.id} with key ${idempotencyKey}`);
        return res.status(200).json({
          ...existingVouchers[0],
          message: 'Voucher already exists with this idempotency key'
        });
      }
    }

    // CRITICAL FIX: Add submission lock to prevent concurrent submissions
    const submissionLockKey = `${req.user.id}-${department}-${Date.now().toString().slice(0, -3)}`; // Lock per second
    logger.info(`Creating voucher with lock key: ${submissionLockKey}`);

    // CRITICAL FIX: Use a transaction with row locking to ensure atomicity and prevent race conditions
    const connection = await getTransaction();

    try {
      // PERMANENT FIX: Generate department-specific voucher ID within the transaction
      const now = new Date();
      const month = now.toLocaleString('en-US', { month: 'short' }).toUpperCase();

      // Create unique department prefix mapping
      const deptPrefixMap: Record<string, string> = {
        'FINANCE': 'FIN',
        'MINISTRIES': 'MIN',
        'PENSIONS': 'PEN',
        'PENTMEDIA': 'PMD',  // Unique prefix for PENTMEDIA
        'MISSIONS': 'MIS',
        'PENTSOS': 'PSO',    // Unique prefix for PENTSOS
        'AUDIT': 'AUD',
        'SYSTEM ADMIN': 'SYS'
      };

      const deptPrefix = deptPrefixMap[department] || department.substring(0, 3).toUpperCase();

      // CRITICAL FIX: Use a more robust approach to get the next voucher number
      // Count existing vouchers GLOBALLY for this prefix and month to avoid duplicates
      // when vouchers are transferred between departments
      const [countResult] = await connection.query(
        `SELECT COUNT(*) as voucher_count
         FROM vouchers
         WHERE voucher_id LIKE ?
         FOR UPDATE`,
        [`${deptPrefix}${month}%`]
      ) as any[];

      // Calculate the next voucher number based on count (ensures no gaps)
      const nextNumber = (countResult[0].voucher_count || 0) + 1;
      let voucherId = `${deptPrefix}${month}${nextNumber.toString().padStart(4, '0')}`;

      // SAFETY CHECK: Verify this voucher ID doesn't already exist
      const [existingCheck] = await connection.query(
        'SELECT COUNT(*) as exists_count FROM vouchers WHERE voucher_id = ?',
        [voucherId]
      ) as any[];

      if (existingCheck[0].exists_count > 0) {
        // If somehow the ID exists, use MAX approach as fallback
        // CRITICAL FIX: Check GLOBALLY across all departments
        const [maxResult] = await connection.query(
          `SELECT MAX(CAST(SUBSTRING(voucher_id, 7) AS UNSIGNED)) as max_count
           FROM vouchers
           WHERE voucher_id LIKE ?`,
          [`${deptPrefix}${month}%`]
        ) as any[];

        const fallbackNumber = (maxResult[0].max_count || 0) + 1;
        const fallbackVoucherId = `${deptPrefix}${month}${fallbackNumber.toString().padStart(4, '0')}`;

        logger.warn(`Voucher ID collision detected. Using fallback: ${voucherId} → ${fallbackVoucherId}`);
        voucherId = fallbackVoucherId;
      }

      // Log the voucher ID generation
      logger.info(`Generated department-specific voucher ID: ${voucherId} for department: ${department} (prefix: ${deptPrefix})`);

      // Create voucher
      const id = uuidv4();
      const date = new Date().toLocaleString('en-US', {
        day: '2-digit',
        month: 'long',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      }).toUpperCase();

      // PERMANENT SOLUTION: Insert voucher with correct PENDING status for immediate display
      await connection.query(
        `INSERT INTO vouchers (
          id, voucher_id, date, claimant, description, amount, currency, department,
          original_department, dispatched_by, dispatch_time, status, sent_to_audit, created_by,
          tax_type, tax_details, tax_amount, comment, idempotency_key, deleted
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          id, voucherId, date, claimant, description, amount, currency, department,
          department, // Set original_department same as department initially
          '', '', VOUCHER_STATUSES.PENDING, false, req.user.name,
          taxType || null, taxDetails || null, taxAmount || null, comment || null, idempotencyKey || null, false
        ]
      );

      // Commit the transaction
      await connection.commit();

      // Get created voucher
      const vouchers = await query('SELECT * FROM vouchers WHERE id = ?', [id]) as any[];
      const createdVoucher = vouchers[0];

      // Log the created voucher for debugging
      logger.info(`Created voucher with ID: ${id}, Voucher ID: ${voucherId}`);
      console.log('Created voucher:', createdVoucher);

      // Ensure the voucher_id is properly set in the response
      if (!createdVoucher.voucher_id) {
        createdVoucher.voucher_id = voucherId;
      }

      // CRITICAL FIX: Format voucher for client and broadcast
      const clientFormattedVoucher = {
        ...createdVoucher,
        // Ensure client-compatible field names
        voucherId: createdVoucher.voucher_id,
        createdBy: createdVoucher.created_by,
        dispatchedBy: createdVoucher.dispatched_by || '',
        sentToAudit: Boolean(createdVoucher.sent_to_audit),
        isReturned: Boolean(createdVoucher.is_returned),
        pendingReturn: Boolean(createdVoucher.pending_return),
        dispatched: Boolean(createdVoucher.dispatched),
        lastUpdated: new Date().toISOString()
      };

      broadcastVoucherUpdate('created', clientFormattedVoucher);

      res.status(201).json(createdVoucher);
    } catch (error) {
      // Rollback the transaction on error
      await connection.rollback();
      throw error;
    } finally {
      // Release the connection
      connection.release();
    }
  } catch (error) {
    logger.error('Create voucher error:', error);
    res.status(500).json({ error: 'Failed to create voucher' });
  }
});

// Update voucher
voucherRouter.put('/:id', async (req, res) => {
  try {
    const voucherId = req.params.id;

    // Get voucher
    const vouchers = await query('SELECT * FROM vouchers WHERE id = ? AND deleted = FALSE', [voucherId]) as any[];

    if (vouchers.length === 0) {
      return res.status(404).json({ error: 'Voucher not found' });
    }

    const voucher = vouchers[0];

    // Check if user has access to update this voucher
    if (req.user.department !== 'AUDIT' && req.user.department !== 'SYSTEM ADMIN' && voucher.department !== req.user.department) {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Define field mapping from frontend camelCase to database snake_case
    const fieldMapping: Record<string, string> = {
      // Basic fields
      'voucherId': 'voucher_id',
      'voucher_id': 'voucher_id',
      'claimant': 'claimant',
      'description': 'description',
      'amount': 'amount',
      'currency': 'currency',
      'department': 'original_department',
      'status': 'status',
      'comment': 'comment',
      'date': 'date',

      // Audit fields
      'preAuditedAmount': 'pre_audited_amount',
      'pre_audited_amount': 'pre_audited_amount',
      'preAuditedBy': 'pre_audited_by',
      'pre_audited_by': 'pre_audited_by',
      'certifiedBy': 'certified_by',
      'certified_by': 'certified_by',
      'taxType': 'tax_type',
      'tax_type': 'tax_type',
      'taxDetails': 'tax_details',
      'tax_details': 'tax_details',
      'taxAmount': 'tax_amount',
      'tax_amount': 'tax_amount',
      'certified': 'certified',
      'receivedByAudit': 'received_by_audit',
      'received_by_audit': 'received_by_audit',
      'certificationDate': 'certification_date',
      'certification_date': 'certification_date',

      // Dispatch fields
      'dispatchedBy': 'dispatched_by',
      'dispatched_by': 'dispatched_by',
      'dispatchTime': 'dispatch_time',
      'dispatch_time': 'dispatch_time',
      'dispatched': 'dispatched',
      'dispatchToAuditBy': 'dispatch_to_audit_by',
      'dispatch_to_audit_by': 'dispatch_to_audit_by',
      'auditDispatchTime': 'audit_dispatch_time',
      'audit_dispatch_time': 'audit_dispatch_time',
      'auditDispatchedBy': 'audit_dispatched_by',
      'audit_dispatched_by': 'audit_dispatched_by',

      // Return fields
      'isReturned': 'is_returned',
      'is_returned': 'is_returned',
      'returnComment': 'return_comment',
      'return_comment': 'return_comment',
      'returnTime': 'return_time',
      'return_time': 'return_time',
      'pendingReturn': 'pending_return',
      'pending_return': 'pending_return',
      'returnInitiatedTime': 'return_initiated_time',
      'return_initiated_time': 'return_initiated_time',

      // Receipt fields
      'receivedBy': 'received_by',
      'received_by': 'received_by',
      'receiptTime': 'receipt_time',
      'receipt_time': 'receipt_time',
      'departmentReceiptTime': 'department_receipt_time',
      'department_receipt_time': 'department_receipt_time',
      'departmentReceivedBy': 'department_received_by',
      'department_received_by': 'department_received_by',

      // Rejection fields
      'rejectedBy': 'rejected_by',
      'rejected_by': 'rejected_by',
      'rejectionTime': 'rejection_time',
      'rejection_time': 'rejection_time',
      'departmentRejected': 'department_rejected',
      'department_rejected': 'department_rejected',
      'rejectionReason': 'rejection_reason',
      'rejection_reason': 'rejection_reason',

      // Lock fields
      'isLocked': 'is_locked',
      'is_locked': 'is_locked',
      'lockedBy': 'locked_by',
      'locked_by': 'locked_by',
      'lockTime': 'lock_time',
      'lock_time': 'lock_time',
      'lastUpdatedBy': 'last_updated_by',
      'last_updated_by': 'last_updated_by',

      // Other fields
      'sentToAudit': 'sent_to_audit',
      'sent_to_audit': 'sent_to_audit',
      'batchId': 'batch_id',
      'batch_id': 'batch_id',
      'createdBy': 'created_by',
      'created_by': 'created_by',
      'deleted': 'deleted',
      'deletionTime': 'deletion_time',
      'deletion_time': 'deletion_time',
      'postProvisionalCash': 'post_provisional_cash',
      'post_provisional_cash': 'post_provisional_cash',
      'dispatchToOnDepartment': 'dispatch_to_on_department',
      'dispatch_to_on_department': 'dispatch_to_on_department',
      'referenceId': 'reference_id',
      'reference_id': 'reference_id',
      'version': 'version',
      'flags': 'flags',
      'idempotencyKey': 'idempotency_key',
      'idempotency_key': 'idempotency_key',
      'workStarted': 'work_started',
      'work_started': 'work_started',
      // Note: final_status, batch_processed, and batch_processed_time fields removed
      // as they don't exist in the database schema
    };

    // Build update query
    let updateQuery = 'UPDATE vouchers SET ';
    const updateParams = [];
    const updates = [];

    // DEBUGGING: Log all incoming fields for troubleshooting
    logger.info(`🔍 VOUCHER UPDATE DEBUG: Incoming fields for voucher ${voucherId}:`, Object.keys(req.body));
    logger.info(`🔍 VOUCHER UPDATE DEBUG: Full request body:`, req.body);

    // Process each field in the request body
    for (const [key, value] of Object.entries(req.body)) {
      // Skip fields that shouldn't be updated directly
      if (key === 'id' || key === 'lastUpdated') {
        logger.info(`🔍 VOUCHER UPDATE DEBUG: Skipping field: ${key}`);
        continue;
      }

      // Get the correct database column name
      const columnName = fieldMapping[key];

      if (columnName) {
        updates.push(`${columnName} = ?`);
        updateParams.push(value);
        logger.info(`🔍 VOUCHER UPDATE DEBUG: Mapped field: ${key} → ${columnName} = ${value}`);
      } else {
        // Log unknown fields but don't fail
        logger.warn(`🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: ${key} = ${value}`);
      }
    }

    // If no updates, return early with detailed error
    if (updates.length === 0) {
      logger.error(`🔍 VOUCHER UPDATE DEBUG: No valid updates for voucher ${voucherId}. Received fields: ${Object.keys(req.body).join(', ')}`);
      return res.status(400).json({
        error: 'No valid updates provided',
        receivedFields: Object.keys(req.body),
        validFields: Object.keys(fieldMapping)
      });
    }

    updateQuery += updates.join(', ') + ' WHERE id = ?';
    updateParams.push(voucherId);

    // Log the update query for debugging
    logger.info(`Updating voucher ${voucherId} with query: ${updateQuery}`);
    logger.info(`Update parameters:`, updateParams);

    // Execute update
    await query(updateQuery, updateParams);

    // Get updated voucher
    const updatedVouchers = await query('SELECT * FROM vouchers WHERE id = ?', [voucherId]) as any[];
    const updatedVoucher = updatedVouchers[0];

    // Transform voucher for client compatibility before broadcasting
    const transformedVoucher = {
      ...updatedVoucher,
      voucherId: updatedVoucher.voucher_id,
      originalDepartment: updatedVoucher.original_department,
      createdBy: updatedVoucher.created_by,
      dispatchedBy: updatedVoucher.dispatched_by,
      dispatchTime: updatedVoucher.dispatch_time,
      sentToAudit: Boolean(updatedVoucher.sent_to_audit),
      batchId: updatedVoucher.batch_id,
      receivedBy: updatedVoucher.received_by,
      receivedByAudit: Boolean(updatedVoucher.received_by_audit),
      receiptTime: updatedVoucher.receipt_time,
      taxType: updatedVoucher.tax_type,
      taxDetails: updatedVoucher.tax_details,
      taxAmount: updatedVoucher.tax_amount,
      preAuditedAmount: updatedVoucher.pre_audited_amount,
      preAuditedBy: updatedVoucher.pre_audited_by,
      certifiedBy: updatedVoucher.certified_by,
      auditDispatchTime: updatedVoucher.audit_dispatch_time,
      auditDispatchedBy: updatedVoucher.audit_dispatched_by,
      dispatchToOnDepartment: Boolean(updatedVoucher.dispatch_to_on_department),
      postProvisionalCash: Boolean(updatedVoucher.post_provisional_cash),
      dispatched: Boolean(updatedVoucher.dispatched),
      dispatchToAuditBy: updatedVoucher.dispatch_to_audit_by,
      workStarted: Boolean(updatedVoucher.work_started),
      isReturned: Boolean(updatedVoucher.is_returned),
      returnComment: updatedVoucher.return_comment,
      returnTime: updatedVoucher.return_time,
      deleted: Boolean(updatedVoucher.deleted),
      deletionTime: updatedVoucher.deletion_time,
      rejectionTime: updatedVoucher.rejection_time,
      departmentReceiptTime: updatedVoucher.department_receipt_time,
      departmentReceivedBy: updatedVoucher.department_received_by,
      departmentRejected: Boolean(updatedVoucher.department_rejected),
      rejectedBy: updatedVoucher.rejected_by,
      pendingReturn: Boolean(updatedVoucher.pending_return),
      returnInitiatedTime: updatedVoucher.return_initiated_time,
      referenceId: updatedVoucher.reference_id,
      idempotencyKey: updatedVoucher.idempotency_key,
      createdAt: updatedVoucher.created_at
    };

    // PRODUCTION FIX: Use only one broadcast method to prevent duplicates
    broadcastVoucherUpdate('updated', transformedVoucher);

    res.json(updatedVoucher);
  } catch (error) {
    logger.error('Update voucher error:', error);
    res.status(500).json({ error: 'Failed to update voucher' });
  }
});

// Delete voucher (soft delete)
voucherRouter.delete('/:id', async (req, res) => {
  try {
    const voucherId = req.params.id;

    // Get voucher
    const vouchers = await query('SELECT * FROM vouchers WHERE id = ? AND deleted = FALSE', [voucherId]) as any[];

    if (vouchers.length === 0) {
      return res.status(404).json({ error: 'Voucher not found' });
    }

    const voucher = vouchers[0];

    // Check if user has access to delete this voucher
    if (req.user.department !== 'AUDIT' && req.user.department !== 'SYSTEM ADMIN' && voucher.department !== req.user.department) {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Soft delete
    await query(
      'UPDATE vouchers SET deleted = TRUE, deletion_time = ? WHERE id = ?',
      [new Date().toISOString(), voucherId]
    );

    // PRODUCTION FIX: Use only one broadcast method to prevent duplicates
    broadcastVoucherUpdate('deleted', { id: voucherId, voucher_id: voucher.voucher_id });

    res.json({ message: 'Voucher deleted successfully' });
  } catch (error) {
    logger.error('Delete voucher error:', error);
    res.status(500).json({ error: 'Failed to delete voucher' });
  }
});

// Send voucher to audit
voucherRouter.post('/:id/send-to-audit', async (req, res) => {
  try {
    const voucherId = req.params.id;
    const connection = await getTransaction();

    try {
      // Get voucher with row lock
      const [vouchers] = await connection.query(
        'SELECT * FROM vouchers WHERE id = ? AND deleted = FALSE FOR UPDATE',
        [voucherId]
      ) as any[];

      if (vouchers.length === 0) {
        await connection.rollback();
        return res.status(404).json({ error: 'Voucher not found' });
      }

      const voucher = vouchers[0];

      // Check if user has access to send this voucher to audit
      if (req.user.department !== 'SYSTEM ADMIN' && voucher.department !== req.user.department) {
        await connection.rollback();
        return res.status(403).json({ error: 'Access denied' });
      }

      // Get the flags that should be set for the new status
      const { flags } = synchronizeVoucherFlags({ status: VOUCHER_STATUSES.PENDING_RECEIPT });

      // FIXED: Validate the status transition with the flags that WILL BE SET during transition
      const currentFlags = voucher.flags ? JSON.parse(voucher.flags) : {};
      const flagsAfterTransition = { ...currentFlags, sentToAudit: true }; // Include the flag that will be set

      const { isValid, reason } = isValidStatusTransition(
        voucher.status,
        VOUCHER_STATUSES.PENDING_RECEIPT,
        req.user.role,
        flagsAfterTransition // Use flags that will exist after transition
      );

      if (!isValid) {
        await connection.rollback();
        logger.warn(`Invalid status transition attempted: ${voucher.status} -> ${VOUCHER_STATUSES.PENDING_RECEIPT}: ${reason}`);
        return res.status(400).json({
          error: `Invalid status transition: ${reason}`
        });
      }

      // CRITICAL FIX: Update voucher with proper status, synchronized flags, sent_to_audit flag, AND reference_id for offset logic
      await connection.query(
        'UPDATE vouchers SET status = ?, flags = ?, dispatch_to_audit_by = ?, dispatch_time = NOW(), sent_to_audit = TRUE, reference_id = ? WHERE id = ?',
        [VOUCHER_STATUSES.PENDING_RECEIPT, JSON.stringify(flags), req.user.name, voucher.voucher_id, voucherId]
      );

      // Log the status change
      logger.info(`Changed voucher ${voucherId} status to ${VOUCHER_STATUSES.PENDING_RECEIPT} (sent to audit by ${req.user.name})`);

      // Create notification for audit department
      const notificationId = uuidv4();

      // Get all users with AUDIT department
      const auditUsers = await connection.query(
        'SELECT id FROM users WHERE department = "AUDIT" AND is_active = 1'
      ) as any[];

      // Create notifications for audit users
      if (auditUsers[0].length === 0) {
        await connection.query(
          `INSERT INTO notifications (
            id, user_id, message, is_read, timestamp, voucher_id, type
          ) VALUES (?, ?, ?, ?, NOW(), ?, ?)`,
          [
            notificationId,
            'AUDIT',
            `New voucher ${voucher.voucher_id} received from ${voucher.department}`,
            false,
            voucherId,
            'NEW_VOUCHER'
          ]
        );
      } else {
        for (const auditUser of auditUsers[0]) {
          await connection.query(
            `INSERT INTO notifications (
              id, user_id, message, is_read, timestamp, voucher_id, type
            ) VALUES (?, ?, ?, ?, NOW(), ?, ?)`,
            [
              uuidv4(),
              auditUser.id,
              `New voucher ${voucher.voucher_id} received from ${voucher.department}`,
              false,
              voucherId,
              'NEW_VOUCHER'
            ]
          );
        }
      }

      // Get updated voucher
      const [updatedVouchers] = await connection.query(
        'SELECT * FROM vouchers WHERE id = ?',
        [voucherId]
      ) as any[];
      const updatedVoucher = updatedVouchers[0];

      // Transform voucher for client compatibility before broadcasting
      const transformedVoucher = {
        ...updatedVoucher,
        voucherId: updatedVoucher.voucher_id,
        originalDepartment: updatedVoucher.original_department,
        createdBy: updatedVoucher.created_by,
        dispatchedBy: updatedVoucher.dispatched_by,
        dispatchTime: updatedVoucher.dispatch_time,
        sentToAudit: Boolean(updatedVoucher.sent_to_audit),
        batchId: updatedVoucher.batch_id,
        receivedBy: updatedVoucher.received_by,
        receivedByAudit: Boolean(updatedVoucher.received_by_audit),
        receiptTime: updatedVoucher.receipt_time,
        taxType: updatedVoucher.tax_type,
        taxDetails: updatedVoucher.tax_details,
        taxAmount: updatedVoucher.tax_amount,
        preAuditedAmount: updatedVoucher.pre_audited_amount,
        preAuditedBy: updatedVoucher.pre_audited_by,
        certifiedBy: updatedVoucher.certified_by,
        auditDispatchTime: updatedVoucher.audit_dispatch_time,
        auditDispatchedBy: updatedVoucher.audit_dispatched_by,
        dispatchToOnDepartment: Boolean(updatedVoucher.dispatch_to_on_department),
        postProvisionalCash: Boolean(updatedVoucher.post_provisional_cash),
        dispatched: Boolean(updatedVoucher.dispatched),
        dispatchToAuditBy: updatedVoucher.dispatch_to_audit_by,
        workStarted: Boolean(updatedVoucher.work_started),
        isReturned: Boolean(updatedVoucher.is_returned),
        returnComment: updatedVoucher.return_comment,
        returnTime: updatedVoucher.return_time,
        deleted: Boolean(updatedVoucher.deleted),
        deletionTime: updatedVoucher.deletion_time,
        rejectionTime: updatedVoucher.rejection_time,
        departmentReceiptTime: updatedVoucher.department_receipt_time,
        departmentReceivedBy: updatedVoucher.department_received_by,
        departmentRejected: Boolean(updatedVoucher.department_rejected),
        rejectedBy: updatedVoucher.rejected_by,
        pendingReturn: Boolean(updatedVoucher.pending_return),
        returnInitiatedTime: updatedVoucher.return_initiated_time,
        referenceId: updatedVoucher.reference_id,
        idempotencyKey: updatedVoucher.idempotency_key,
        createdAt: updatedVoucher.created_at
      };

      // PRODUCTION FIX: Use only one broadcast method to prevent duplicates
      broadcastVoucherUpdate('updated', transformedVoucher);

      // Broadcast notification
      const notification = {
        id: notificationId,
        user_id: 'AUDIT',
        message: `New voucher ${voucher.voucher_id} received from ${voucher.department}`,
        is_read: false,
        timestamp: new Date().toISOString(),
        voucher_id: voucherId,
        type: 'NEW_VOUCHER'
      };
      // PRODUCTION FIX: Use only one broadcast method to prevent duplicates
      broadcastNotificationUpdate('created', notification);

      await connection.commit();
      res.json(updatedVoucher);
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  } catch (error) {
    logger.error('Send voucher to audit error:', error);
    res.status(500).json({ error: 'Failed to send voucher to audit' });
  }
});

// Return voucher from audit
voucherRouter.post('/:id/return', async (req, res) => {
  try {
    const voucherId = req.params.id;
    const { returnComment } = req.body;

    // Get voucher
    const vouchers = await query('SELECT * FROM vouchers WHERE id = ? AND deleted = FALSE', [voucherId]) as any[];

    if (vouchers.length === 0) {
      return res.status(404).json({ error: 'Voucher not found' });
    }

    const voucher = vouchers[0];

    // Check if user has access to return this voucher
    if (req.user.department !== 'AUDIT' && req.user.department !== 'SYSTEM ADMIN') {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Get the flags that should be set for the new status
    const { flags } = synchronizeVoucherFlags({ status: VOUCHER_STATUSES.VOUCHER_RETURNED });

    // Validate the status transition
    if (!isValidStatusTransition(voucher.status, VOUCHER_STATUSES.VOUCHER_RETURNED, req.user.role, voucher)) {
      logger.warn(`Invalid status transition attempted: ${voucher.status} -> ${VOUCHER_STATUSES.VOUCHER_RETURNED}`);
      return res.status(400).json({
        error: `Invalid status transition from ${voucher.status} to ${VOUCHER_STATUSES.VOUCHER_RETURNED}`
      });
    }

    // Update voucher with proper status and synchronized flags
    const currentTime = new Date().toISOString();
    await query(
      'UPDATE vouchers SET status = ?, flags = ? WHERE id = ?',
      [VOUCHER_STATUSES.VOUCHER_RETURNED, JSON.stringify(flags), voucherId]
    );

    // Log the status change
    logger.info(`Changed voucher ${voucherId} status to ${VOUCHER_STATUSES.VOUCHER_RETURNED} (returned by ${req.user.name})`);
    console.log(`Changed voucher ${voucherId} status to ${VOUCHER_STATUSES.VOUCHER_RETURNED} (returned by ${req.user.name})`);

    // Create notification for department
    await query(
      `INSERT INTO notifications (
        id, user_id, message, is_read, timestamp, voucher_id, type, from_audit
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        uuidv4(),
        voucher.department,
        `Voucher ${voucher.voucher_id} returned from Audit`,
        false,
        currentTime,
        voucherId,
        'VOUCHER_RETURNED',
        true
      ]
    );

    // Log the return
    logger.info(`Voucher ${voucherId} (${voucher.voucher_id}) returned by ${req.user.name}`);
    console.log(`Voucher ${voucherId} (${voucher.voucher_id}) returned by ${req.user.name} with comment: ${returnComment || 'No comment provided'}`);


    // Get updated voucher
    const updatedVouchers = await query('SELECT * FROM vouchers WHERE id = ?', [voucherId]) as any[];
    const updatedVoucher = updatedVouchers[0];

    // PRODUCTION FIX: Broadcast voucher update to prevent frontend spinning
    broadcastVoucherUpdate('updated', updatedVoucher);

    res.json(updatedVoucher);
  } catch (error) {
    logger.error('Return voucher error:', error);
    res.status(500).json({ error: 'Failed to return voucher' });
  }
});

// Certify voucher
voucherRouter.post('/:id/certify', async (req, res) => {
  try {
    const voucherId = req.params.id;

    // Get voucher
    const vouchers = await query('SELECT * FROM vouchers WHERE id = ? AND deleted = FALSE', [voucherId]) as any[];

    if (vouchers.length === 0) {
      return res.status(404).json({ error: 'Voucher not found' });
    }

    const voucher = vouchers[0];

    // Check if user has access to certify this voucher
    if (req.user.department !== 'AUDIT' && req.user.department !== 'SYSTEM ADMIN') {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Get the flags that should be set for the new status
    const { flags } = synchronizeVoucherFlags({ status: VOUCHER_STATUSES.VOUCHER_CERTIFIED });

    // Validate the status transition
    if (!isValidStatusTransition(voucher.status, VOUCHER_STATUSES.VOUCHER_CERTIFIED, req.user.role, voucher)) {
      logger.warn(`Invalid status transition attempted: ${voucher.status} -> ${VOUCHER_STATUSES.VOUCHER_CERTIFIED}`);
      return res.status(400).json({
        error: `Invalid status transition from ${voucher.status} to ${VOUCHER_STATUSES.VOUCHER_CERTIFIED}`
      });
    }

    // Update voucher with proper status and synchronized flags
    // PRODUCTION FIX: Update database columns to match flags for proper filtering
    // OFFSET LOGIC FIX: Set reference_id to enable offset logic for removing original voucher from Processing tab
    const currentTime = new Date().toISOString();
    await query(
      `UPDATE vouchers SET
        status = ?,
        flags = ?,
        sent_to_audit = TRUE,
        dispatched = TRUE,
        certified_by = ?,
        audit_dispatch_time = ?,
        reference_id = ?
      WHERE id = ?`,
      [
        VOUCHER_STATUSES.VOUCHER_CERTIFIED,
        JSON.stringify(flags),
        req.user.name,
        currentTime,
        voucher.voucher_id, // CRITICAL FIX: Set reference_id to original voucher_id for offset logic
        voucherId
      ]
    );

    // Log the status change
    logger.info(`Changed voucher ${voucherId} status to ${VOUCHER_STATUSES.VOUCHER_CERTIFIED} (certified by ${req.user.name})`);
    console.log(`Changed voucher ${voucherId} status to ${VOUCHER_STATUSES.VOUCHER_CERTIFIED} (certified by ${req.user.name})`);

    // Create notification for the department
    await query(
      `INSERT INTO notifications (
        id, user_id, message, is_read, timestamp, voucher_id, type, from_audit
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        uuidv4(),
        voucher.department,
        `Voucher ${voucher.voucher_id} certified by Audit`,
        false,
        currentTime,
        voucherId,
        'VOUCHER_CERTIFIED',
        true
      ]
    );

    // Log the certification
    logger.info(`Voucher ${voucherId} (${voucher.voucher_id}) certified by ${req.user.name}`);
    console.log(`Voucher ${voucherId} (${voucher.voucher_id}) certified by ${req.user.name}`);

    // Get updated voucher
    const updatedVouchers = await query('SELECT * FROM vouchers WHERE id = ?', [voucherId]) as any[];
    const updatedVoucher = updatedVouchers[0];

    // PRODUCTION FIX: Broadcast voucher update to prevent frontend spinning
    broadcastVoucherUpdate('updated', updatedVoucher);

    res.json(updatedVoucher);
  } catch (error) {
    logger.error('Certify voucher error:', error);
    res.status(500).json({ error: 'Failed to certify voucher' });
  }
});

// Reject voucher
voucherRouter.post('/:id/reject', async (req, res) => {
  try {
    const voucherId = req.params.id;
    const { comment } = req.body;

    // Get voucher
    const vouchers = await query('SELECT * FROM vouchers WHERE id = ? AND deleted = FALSE', [voucherId]) as any[];

    if (vouchers.length === 0) {
      return res.status(404).json({ error: 'Voucher not found' });
    }

    const voucher = vouchers[0];

    // Check if user has access to reject this voucher
    if (req.user.department !== 'AUDIT' && req.user.department !== 'SYSTEM ADMIN') {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Get the flags that should be set for the new status
    const { flags } = synchronizeVoucherFlags({ status: VOUCHER_STATUSES.VOUCHER_REJECTED });

    // Validate the status transition
    if (!isValidStatusTransition(voucher.status, VOUCHER_STATUSES.VOUCHER_REJECTED, req.user.role, voucher)) {
      logger.warn(`Invalid status transition attempted: ${voucher.status} -> ${VOUCHER_STATUSES.VOUCHER_REJECTED}`);
      return res.status(400).json({
        error: `Invalid status transition from ${voucher.status} to ${VOUCHER_STATUSES.VOUCHER_REJECTED}`
      });
    }

    // Update voucher with proper status and synchronized flags
    const currentTime = new Date().toISOString();
    await query(
      'UPDATE vouchers SET status = ?, flags = ? WHERE id = ?',
      [VOUCHER_STATUSES.VOUCHER_REJECTED, JSON.stringify(flags), voucherId]
    );

    // Log the status change
    logger.info(`Changed voucher ${voucherId} status to ${VOUCHER_STATUSES.VOUCHER_REJECTED} (rejected by ${req.user.name})`);
    console.log(`Changed voucher ${voucherId} status to ${VOUCHER_STATUSES.VOUCHER_REJECTED} (rejected by ${req.user.name})`);

    // Create notification for department
    const notificationId = uuidv4();
    await query(
      `INSERT INTO notifications (
        id, user_id, message, is_read, timestamp, voucher_id, type, from_audit
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        notificationId,
        voucher.department,
        `Voucher ${voucher.voucher_id} rejected by Audit`,
        false,
        currentTime,
        voucherId,
        'VOUCHER_REJECTED',
        true
      ]
    );

    // Log the rejection
    logger.info(`Voucher ${voucherId} (${voucher.voucher_id}) rejected by ${req.user.name}`);
    console.log(`Voucher ${voucherId} (${voucher.voucher_id}) rejected by ${req.user.name} with comment: ${comment || 'No comment provided'}`);


    // Get updated voucher
    const updatedVouchers = await query('SELECT * FROM vouchers WHERE id = ?', [voucherId]) as any[];
    const updatedVoucher = updatedVouchers[0];

    // PRODUCTION FIX: Broadcast voucher update to prevent frontend spinning
    broadcastVoucherUpdate('updated', updatedVoucher);

    res.json(updatedVoucher);
  } catch (error) {
    logger.error('Reject voucher error:', error);
    res.status(500).json({ error: 'Failed to reject voucher' });
  }
});

// Get blacklisted voucher IDs
voucherRouter.get('/blacklist/ids', async (req, res) => {
  try {
    const blacklistedIds = await query('SELECT * FROM blacklisted_voucher_ids') as any[];
    res.json(blacklistedIds);
  } catch (error) {
    logger.error('Get blacklisted voucher IDs error:', error);
    res.status(500).json({ error: 'Failed to get blacklisted voucher IDs' });
  }
});

// Add voucher ID to blacklist
voucherRouter.post('/blacklist/ids', async (req, res) => {
  try {
    const { voucherId } = req.body;

    if (!voucherId) {
      return res.status(400).json({ error: 'Voucher ID is required' });
    }

    // Check if already blacklisted
    const existingIds = await query('SELECT * FROM blacklisted_voucher_ids WHERE voucher_id = ?', [voucherId]) as any[];

    if (existingIds.length > 0) {
      return res.status(409).json({ error: 'Voucher ID already blacklisted' });
    }

    // Add to blacklist
    const id = uuidv4();
    await query(
      'INSERT INTO blacklisted_voucher_ids (id, voucher_id) VALUES (?, ?)',
      [id, voucherId]
    );

    res.status(201).json({
      id,
      voucherId
    });
  } catch (error) {
    logger.error('Add to blacklist error:', error);
    res.status(500).json({ error: 'Failed to add voucher ID to blacklist' });
  }
});
