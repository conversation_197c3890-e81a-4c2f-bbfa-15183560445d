const mysql = require('mysql2/promise');

async function checkDispatchedVoucher() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root', 
    password: 'vms@2025@1989',
    database: 'vms_production'
  });
  
  console.log('🔍 Checking dispatched voucher state...');
  
  const [vouchers] = await connection.execute('SELECT voucher_id, department, original_department, status, dispatched, audit_dispatched_by FROM vouchers WHERE id = ?', ['d34ca623-d30c-4fd9-97e3-90ca0193bf84']);
  const voucher = vouchers[0];
  
  console.log('📄 Current voucher state:');
  console.log('   Voucher ID:', voucher.voucher_id);
  console.log('   Department:', voucher.department);
  console.log('   Original Department:', voucher.original_department);
  console.log('   Status:', voucher.status);
  console.log('   Dispatched:', voucher.dispatched);
  console.log('   Audit Dispatched By:', voucher.audit_dispatched_by);
  
  console.log('\n🧪 DISPATCHED tab filtering analysis:');
  console.log('   Current filter (originalDepartment === FINANCE && dispatched === true):');
  console.log('     originalDepartment === FINANCE:', voucher.original_department === 'FINANCE');
  console.log('     dispatched === true:', voucher.dispatched === 1);
  console.log('     Would appear in FINANCE DISPATCHED tab:', voucher.original_department === 'FINANCE' && voucher.dispatched === 1);
  
  console.log('\n   Correct filter for AUDIT DISPATCHED tab should be:');
  console.log('     audit_dispatched_by is set:', voucher.audit_dispatched_by ? 'YES' : 'NO');
  console.log('     dispatched === true:', voucher.dispatched === 1);
  
  await connection.end();
}

checkDispatchedVoucher().catch(console.error);
