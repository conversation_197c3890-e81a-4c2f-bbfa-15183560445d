import { ClearanceRemark, Currency, Department, Notification, ProvisionalCashRecord, TaxType, TransactionStatus, User, Voucher } from "./types";

// Mock data for development
export const departments: Department[] = [
  "FINANCE",
  "MINISTRIES",
  "PENSIONS",
  "PENTMEDIA",
  "MISSIONS",
  "PENTSOS",
  "AUDIT",
  "ADMINISTRATOR",
  "SYSTEM ADMIN"
];

export const taxTypes: TaxType[] = [
  "GOODS 3%",
  "SERVICE 7.5%",
  "WORKS 5%",
  "RENT 8%",
  "PCC 12.5%",
  "RISK 5%",
  "VEH.MAINT 10%",
  "OTHER"
];

export const currencies: Currency[] = [
  "GHS",
  "USD",
  "GBP",
  "EUR"
];

// Helper function to ensure all users have the required fields
const createCompleteUser = (user: Partial<User>): User => {
  // Default password is department name + 123 (e.g., FINANCE123)
  const defaultPassword = `${user.department}123`;

  return {
    id: user.id || `user-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
    name: user.name || '',
    department: user.department || '',
    role: user.role || 'USER',
    email: `${user.name?.toLowerCase().replace(/\s+/g, '.')}@${user.department?.toLowerCase()}.com`,
    password: user.password || defaultPassword,
    dateCreated: new Date().toISOString(),
    isActive: true,
    ...user
  } as User;
};

// Mock users - removed for production, users come from database
export const users: User[] = [];

// COMPREHENSIVE SOLUTION: Create test vouchers for all departments to test workflow
// These will be inserted into the database for testing purposes
export const testVouchersForAllDepartments = [
  // MINISTRIES test vouchers
  {
    voucherId: "MINJUN0001",
    date: new Date().toISOString(),
    claimant: "SAMUEL MAWUKO",
    description: "Transportation Allowance",
    amount: 3000,
    currency: "GHS",
    department: "MINISTRIES",
    status: "PENDING",
    sentToAudit: false,
    deleted: false
  },
  {
    voucherId: "MINJUN0002",
    date: new Date().toISOString(),
    claimant: "MARY ASANTE",
    description: "Office Supplies",
    amount: 1500,
    currency: "GHS",
    department: "MINISTRIES",
    status: "PENDING",
    sentToAudit: false,
    deleted: false
  },
  // PENSIONS test vouchers
  {
    voucherId: "PENJUN0001",
    date: new Date().toISOString(),
    claimant: "JOHN MENSAH",
    description: "Pension Payment",
    amount: 12000,
    currency: "GHS",
    department: "PENSIONS",
    status: "PENDING",
    sentToAudit: false,
    deleted: false
  },
  // PENTMEDIA test vouchers
  {
    voucherId: "PMJUN0001",
    date: new Date().toISOString(),
    claimant: "GRACE OWUSU",
    description: "Media Equipment",
    amount: 5000,
    currency: "GHS",
    department: "PENTMEDIA",
    status: "PENDING",
    sentToAudit: false,
    deleted: false
  },
  // MISSIONS test vouchers
  {
    voucherId: "MISJUN0001",
    date: new Date().toISOString(),
    claimant: "PETER ADJEI",
    description: "Mission Trip Expenses",
    amount: 8000,
    currency: "GHS",
    department: "MISSIONS",
    status: "PENDING",
    sentToAudit: false,
    deleted: false
  },
  // PENTSOS test vouchers
  {
    voucherId: "PSJUN0001",
    date: new Date().toISOString(),
    claimant: "ESTHER BOATENG",
    description: "Social Services",
    amount: 4000,
    currency: "GHS",
    department: "PENTSOS",
    status: "PENDING",
    sentToAudit: false,
    deleted: false
  }
];

// Keep original empty for production
export const initialVouchers: Voucher[] = [];

// Initial provisional cash records - PRODUCTION: Keep empty for clean system
export const initialProvisionalCashRecords: ProvisionalCashRecord[] = [];

// Initial notifications
export const initialNotifications: Notification[] = [
  {
    id: "n1",
    userId: "7",
    message: "New voucher received from Finance Department",
    isRead: false,
    timestamp: "28-APRIL-2025 10:30 AM",
    voucherId: "v1",
    type: "NEW_VOUCHER"
  },
  {
    id: "n2",
    userId: "1",
    message: "Voucher APR00003 rejected by Audit",
    isRead: false,
    timestamp: "28-APRIL-2025 12:45 PM",
    voucherId: "v3",
    type: "VOUCHER_REJECTED"
  }
];

// ARCHITECTURAL FIX: Removed generateVoucherId function
// All voucher ID generation now happens server-side for consistency

// Calculate clearance remark
export type ClearanceResult = {
  remark: ClearanceRemark;
  difference: number;
}

export const calculateClearanceRemark = (mainAmount: number, amountRetired: number): ClearanceResult => {
  const difference = Math.abs(mainAmount - amountRetired);

  if (mainAmount === amountRetired) {
    return { remark: "CLEARED", difference: 0 };
  } else if (amountRetired > mainAmount) {
    return { remark: "DUE STAFF", difference };
  } else {
    return { remark: "REFUNDED TO CHEST", difference };
  }
};
