const mysql = require('mysql2/promise');

async function checkTableStructure() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root', 
    password: 'vms@2025@1989',
    database: 'vms_production'
  });
  
  console.log('📋 VOUCHERS TABLE STRUCTURE:');
  console.log('============================');
  
  const [columns] = await connection.execute('DESCRIBE vouchers');
  columns.forEach(col => {
    console.log(`${col.Field} - ${col.Type} - ${col.Null} - ${col.Key} - ${col.Default}`);
  });
  
  console.log('\n📊 SAMPLE VOUCHER DATA:');
  console.log('=======================');
  
  const [sampleVouchers] = await connection.execute('SELECT * FROM vouchers LIMIT 2');
  console.log('Sample vouchers:', JSON.stringify(sampleVouchers, null, 2));
  
  await connection.end();
}

checkTableStructure().catch(console.error);
