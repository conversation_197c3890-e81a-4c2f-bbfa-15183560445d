import { StateCreator } from 'zustand';
import { TransactionStatus, Voucher, Department } from '../../types';
import { formatCurrentDate } from '../utils';

export interface AppState {
  currentUser: { name: string } | null;
  vouchers: Voucher[];
  voucherBatches: Array<{ id: string }>;
  users: Array<{ id: string; department: Department }>;
  pendingDispatches?: Set<string>; // Track pending dispatch operations to prevent duplicates
  createVoucherBatch: (department: Department, voucherIds: string[], dispatchedBy: string) => void;
  updateVoucher: (id: string, data: Partial<Voucher>) => void;
  addNotification: (data: { userId: string; message: string; voucherId: string; type: string; batchId: string; fromAudit: boolean }) => void;
}

export interface AuditSlice {
  sendVouchersToAudit: (department: Department, voucherIds: string[], dispatchedBy: string) => Promise<void>;
  sendVouchersFromAuditToDepartment: (department: Department, voucherIds: string[]) => Promise<void>;
}

export const createAuditSlice: StateCreator<AppState, [], [], AuditSlice> = (set, get) => ({
  // Initialize pendingDispatches as an empty Set
  pendingDispatches: new Set<string>(),

  sendVouchersToAudit: async (department: Department, voucherIds: string[], dispatchedBy: string) => {
    if (voucherIds.length === 0) return;

    // Check if any vouchers are already sent to audit
    const alreadySentVouchers = get().vouchers.filter(
      (v: Voucher) => voucherIds.includes(v.id) && v.sentToAudit
    );

    if (alreadySentVouchers.length > 0) {
      throw new Error("Some vouchers have already been sent to Audit");
    }

    try {
      // FIXED: Use session-based authentication for LAN deployment
      const currentUser = get().currentUser;
      if (!currentUser) {
        throw new Error('Authentication required. Please log in again.');
      }

      console.log('🔄 Creating batch via API for vouchers:', voucherIds);

      const response = await fetch('/api/batches', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
          // REMOVED: Authorization header - using session cookies instead
        },
        credentials: 'include', // FIXED: Include session cookies
        body: JSON.stringify({
          department,
          voucherIds,
          fromAudit: false
        })
      });

      if (response.ok) {
        const batch = await response.json();
        console.log('✅ Batch created successfully:', batch.id);

        // Sync local state with database
        const newBatch = {
          id: batch.id,
          department,
          voucherIds: batch.voucherIds || voucherIds,
          sentBy: batch.sent_by || dispatchedBy,
          sentTime: batch.sent_time,
          received: false,
          fromAudit: false
        };

        // CRITICAL FIX: Refresh batches from API instead of manual state update
        await get().fetchBatches();

        // CRITICAL FIX: Only update essential fields to prevent infinite loops
        voucherIds.forEach(id => {
          set((state) => ({
            vouchers: state.vouchers.map(v =>
              v.id === id ? {
                ...v,
                status: "PENDING RECEIPT",
                sentToAudit: true,
                batchId: batch.id,
                dispatchToAuditBy: dispatchedBy,
                dispatchTime: batch.sent_time
              } : v
            )
          }));
        });

        console.log(`✅ Successfully sent ${voucherIds.length} vouchers to audit in batch ${batch.id}`);

        // PRODUCTION-READY: Force complete data refresh after batch operations
        console.log('🔄 FORCING COMPLETE DATA REFRESH after batch send to audit...');
        try {
          const { fetchVouchers, fetchBatches, currentUser } = get();
          if (currentUser?.department) {
            // Add a small delay to ensure database has been updated
            await new Promise(resolve => setTimeout(resolve, 500));

            // Force refresh with timestamp to bypass cache
            await fetchVouchers(); // CRITICAL FIX: Fetch ALL vouchers
            await fetchBatches();

            console.log('✅ COMPLETE DATA REFRESH completed successfully after batch operation');
          }
        } catch (refreshError) {
          console.error('❌ Error during complete data refresh:', refreshError);
          // Continue execution - don't fail the entire operation
        }

        return;
      } else {
        const errorText = await response.text();
        throw new Error(`Batch creation failed: ${response.status} ${response.statusText} - ${errorText}`);
      }

    } catch (error) {
      console.error('❌ Failed to create batch:', error);
      throw error; // Let the UI handle the error properly - no fallbacks
    }
  },

  sendVouchersFromAuditToDepartment: async (department: Department, voucherIds: string[]) => {
    if (voucherIds.length === 0) return;

    const currentUser = get().currentUser;
    if (!currentUser) return;

    // CRITICAL FIX: Filter out vouchers that were already processed via batch receiving
    const vouchers = get().vouchers;
    const eligibleVoucherIds = voucherIds.filter(id => {
      const voucher = vouchers.find(v => v.id === id);
      return voucher && !voucher.batchProcessed; // Skip vouchers already processed via batch
    });

    if (eligibleVoucherIds.length === 0) {
      console.log('🚫 AUDIT DISPATCH: All vouchers were already processed via batch receiving, skipping dispatch');
      return;
    }

    if (eligibleVoucherIds.length < voucherIds.length) {
      console.log(`⚠️ AUDIT DISPATCH: Filtered out ${voucherIds.length - eligibleVoucherIds.length} vouchers already processed via batch`);
    }

    // CRITICAL FIX: Prevent duplicate batch creation by checking if vouchers are already being dispatched
    const voucherKey = `${department}-${eligibleVoucherIds.sort().join(',')}`;
    const state = get();

    // Initialize pendingDispatches if it doesn't exist
    if (!state.pendingDispatches) {
      set((state) => ({
        ...state,
        pendingDispatches: new Set()
      }));
    }

    // Check if there's already a pending dispatch for these exact vouchers
    const currentState = get();
    // FIXED: Ensure pendingDispatches is always a Set
    const pendingDispatches = currentState.pendingDispatches || new Set<string>();

    if (pendingDispatches.has(voucherKey)) {
      console.log('⚠️ DUPLICATE DISPATCH PREVENTED: Already dispatching these vouchers to', department);
      return;
    }

    // Mark these vouchers as being dispatched
    set((state) => ({
      ...state,
      pendingDispatches: new Set([...(state.pendingDispatches || new Set()), voucherKey])
    }));

    console.log(`🚀 AUDIT DISPATCH: Sending ${eligibleVoucherIds.length} vouchers from Audit to ${department} department`);

    try {
      // FIXED: Use session-based authentication for LAN deployment
      const currentUser = get().currentUser;
      if (!currentUser) {
        throw new Error('Authentication required. Please log in again.');
      }

      console.log('🔄 AUDIT DISPATCH: Creating batch via API for vouchers from Audit to Department:', eligibleVoucherIds);
      console.log(`🎯 AUDIT DISPATCH: Target department: ${department}, FromAudit: true`);
      console.log(`👤 AUDIT DISPATCH: Current user: ${currentUser.name} (${currentUser.department})`);

      const response = await fetch('/api/batches', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
          // REMOVED: Authorization header - using session cookies instead
        },
        credentials: 'include', // FIXED: Include session cookies
        body: JSON.stringify({
          department,
          voucherIds: eligibleVoucherIds,
          fromAudit: true  // CRITICAL: This indicates batch is FROM Audit TO Department
        })
      });

      console.log(`📡 AUDIT DISPATCH: API response status: ${response.status} ${response.statusText}`);

      // CRITICAL FIX: Handle duplicate batch prevention
      if (response.status === 409) {
        const errorData = await response.json();
        if (errorData.error === 'DUPLICATE_BATCH_PREVENTED') {
          console.warn('🚫 DUPLICATE BATCH PREVENTED:', errorData.message);
          throw new Error(`Duplicate batch prevented: ${errorData.message}`);
        }
      }

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`❌ AUDIT DISPATCH: Batch creation failed: ${response.status} ${response.statusText}`);
        console.error(`❌ AUDIT DISPATCH: Error details: ${errorText}`);
        throw new Error(`Batch creation failed: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const createdBatch = await response.json();
      console.log('✅ AUDIT DISPATCH: Successfully created batch via API:', createdBatch);

      // CRITICAL FIX: Refresh batches from API instead of adding to local state
      // This ensures proper filtering and prevents stale data issues
      await get().fetchBatches();

      // PRODUCTION FIX: Update local voucher states to match database
      // The API call above already updated the database, now sync local state
      eligibleVoucherIds.forEach((id: string) => {
        const voucher = get().vouchers.find((v: Voucher) => v.id === id);
        if (voucher) {
          // Check if this is a returned voucher
          const isVoucherReturned = voucher.pendingReturn || voucher.isReturned || false;
          // Check if this is a rejected voucher
          const isRejectedVoucher = voucher.isRejectedVoucher || voucher.status === "REJECTED: PENDING DISPATCH";

          console.log(`🔄 AUDIT DISPATCH: Updating local state for voucher ${voucher.id}, isReturned: ${isVoucherReturned}, isRejectedVoucher: ${isRejectedVoucher}`);

          let updateData: Partial<Voucher>;

          // NEW FLOW: Handle rejected vouchers specially
          if (isRejectedVoucher) {
            console.log(`🔄 AUDIT DISPATCH: Processing REJECTED voucher ${voucher.id} - will go directly to REJECTED tab, not DISPATCHED tab`);

            updateData = {
              status: "VOUCHER REJECTED" as TransactionStatus, // Change status to VOUCHER REJECTED when dispatched
              rejectedDispatchedBy: currentUser.name,
              rejectedDispatchTime: formatCurrentDate(),
              dispatched: false, // Don't mark as dispatched to avoid appearing in DISPATCHED tab
              isRejectedVoucher: true,
              batchId: createdBatch.id,
              comment: voucher.comment // Preserve rejection comment
            };
          } else {
            // For normal vouchers, use existing logic
            updateData = {
              status: "VOUCHER PROCESSING" as TransactionStatus,  // FIXED: Correct status for department processing
              auditDispatchedBy: voucher.auditDispatchedBy || currentUser.name,
              auditDispatchTime: formatCurrentDate(),
              isReturned: isVoucherReturned,
              pendingReturn: false, // Clear pendingReturn flag
              dispatched: true,
              batchId: createdBatch.id  // Use actual batch ID from API response
            };
          }

          // For returned vouchers, ensure we preserve the comments
          if (isVoucherReturned) {
            const existingReturnComment = voucher.returnComment || voucher.comment;
            const existingComment = voucher.comment || voucher.returnComment;

            // Ensure we have a valid comment
            const finalComment = String(existingReturnComment || existingComment || "NO COMMENT PROVIDED").trim();

            console.log(`🔄 AUDIT DISPATCH: Preserving return comment for voucher ${voucher.id}: "${finalComment}"`);

            updateData.returnComment = finalComment;
            updateData.comment = finalComment;
          }

          // Update the voucher in the store
          console.log(`🔄 AUDIT DISPATCH: Final update data for voucher ${voucher.id}:`, {
            status: updateData.status,
            isReturned: updateData.isReturned,
            pendingReturn: updateData.pendingReturn,
            dispatched: updateData.dispatched,
            batchId: updateData.batchId
          });

          get().updateVoucher(id, updateData);

          // Verify the update was applied
          setTimeout(() => {
            const updatedVoucher = get().vouchers.find(v => v.id === id);
            if (updatedVoucher) {
              console.log(`✅ AUDIT DISPATCH: Voucher ${voucher.id} updated successfully:`, {
                status: updatedVoucher.status,
                isReturned: updatedVoucher.isReturned,
                pendingReturn: updatedVoucher.pendingReturn,
                dispatched: updatedVoucher.dispatched,
                batchId: updatedVoucher.batchId
              });
            }
          }, 100);
        }
      });

      console.log(`✅ AUDIT DISPATCH: Successfully sent ${eligibleVoucherIds.length} vouchers to ${department}, created batch ${createdBatch.id}`);

    } catch (error) {
      console.error('❌ AUDIT DISPATCH: Failed to send vouchers to department:', error);

      // Show user-friendly error message
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

      // You might want to show a toast notification here
      // toast.error(`Failed to send vouchers to ${department}: ${errorMessage}`);

      throw error; // Re-throw so calling code can handle it
    } finally {
      // CRITICAL FIX: Always clear the pending dispatch to allow future dispatches
      set((state) => {
        const newPendingDispatches = new Set(state.pendingDispatches || new Set());
        newPendingDispatches.delete(voucherKey);
        return { ...state, pendingDispatches: newPendingDispatches };
      });
    }
  },
});
