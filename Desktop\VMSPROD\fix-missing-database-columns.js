/**
 * CRITICAL DATABASE SCHEMA FIX
 * 
 * This script fixes the missing database columns that are preventing
 * proper voucher batch creation during audit-to-finance dispatch.
 * 
 * Root Cause: Missing columns cause batch creation to fail, so vouchers
 * go directly to certified tab without proper batch notifications.
 */

import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: './server/.env' });

const connection = await mysql.createConnection({
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'vms@2025@1989',
  database: process.env.DB_NAME || 'vms_production'
});

console.log('🔧 CRITICAL DATABASE SCHEMA FIX FOR BATCH CREATION');
console.log('==================================================');
console.log('Fixing missing columns that prevent voucher batch creation...\n');

try {
  // Step 1: Fix voucher_batches table - Add voucher_ids column
  console.log('1. Fixing voucher_batches table...');
  
  const [batchColumns] = await connection.execute(`
    SELECT COLUMN_NAME 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'voucher_batches'
  `, [process.env.DB_NAME || 'vms_production']);
  
  const hasVoucherIds = batchColumns.some(col => col.COLUMN_NAME === 'voucher_ids');
  
  if (!hasVoucherIds) {
    console.log('   ❌ Missing voucher_ids column - ADDING...');
    await connection.execute(`
      ALTER TABLE voucher_batches 
      ADD COLUMN voucher_ids JSON DEFAULT NULL
    `);
    console.log('   ✅ Added voucher_ids column to voucher_batches');
  } else {
    console.log('   ✅ voucher_ids column already exists');
  }

  // Step 2: Fix vouchers table - Add missing columns
  console.log('\n2. Fixing vouchers table...');
  
  const [voucherColumns] = await connection.execute(`
    SELECT COLUMN_NAME 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'vouchers'
  `, [process.env.DB_NAME || 'vms_production']);
  
  const existingColumns = voucherColumns.map(col => col.COLUMN_NAME);
  
  // Check and add received_by_audit column
  if (!existingColumns.includes('received_by_audit')) {
    console.log('   ❌ Missing received_by_audit column - ADDING...');
    await connection.execute(`
      ALTER TABLE vouchers 
      ADD COLUMN received_by_audit BOOLEAN DEFAULT FALSE
    `);
    console.log('   ✅ Added received_by_audit column');
  } else {
    console.log('   ✅ received_by_audit column already exists');
  }

  // Check and add work_started column
  if (!existingColumns.includes('work_started')) {
    console.log('   ❌ Missing work_started column - ADDING...');
    await connection.execute(`
      ALTER TABLE vouchers 
      ADD COLUMN work_started BOOLEAN DEFAULT FALSE
    `);
    console.log('   ✅ Added work_started column');
  } else {
    console.log('   ✅ work_started column already exists');
  }

  // Check and add flags column
  if (!existingColumns.includes('flags')) {
    console.log('   ❌ Missing flags column - ADDING...');
    await connection.execute(`
      ALTER TABLE vouchers 
      ADD COLUMN flags JSON DEFAULT NULL
    `);
    console.log('   ✅ Added flags column');
  } else {
    console.log('   ✅ flags column already exists');
  }

  // Check and add created_at column (for duplicate detection)
  if (!existingColumns.includes('created_at')) {
    console.log('   ❌ Missing created_at column - ADDING...');
    await connection.execute(`
      ALTER TABLE vouchers 
      ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    `);
    console.log('   ✅ Added created_at column');
  } else {
    console.log('   ✅ created_at column already exists');
  }

  // Step 3: Fix active_sessions table - Add role column
  console.log('\n3. Fixing active_sessions table...');
  
  const [sessionColumns] = await connection.execute(`
    SELECT COLUMN_NAME 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'active_sessions'
  `, [process.env.DB_NAME || 'vms_production']);
  
  const hasRole = sessionColumns.some(col => col.COLUMN_NAME === 'role');
  
  if (!hasRole) {
    console.log('   ❌ Missing role column - ADDING...');
    await connection.execute(`
      ALTER TABLE active_sessions 
      ADD COLUMN role VARCHAR(50) DEFAULT 'USER'
    `);
    console.log('   ✅ Added role column to active_sessions');
  } else {
    console.log('   ✅ role column already exists');
  }

  // Step 4: Update existing data for consistency
  console.log('\n4. Updating existing data for consistency...');
  
  // Set received_by_audit = TRUE for vouchers in AUDIT department
  const [auditUpdate] = await connection.execute(`
    UPDATE vouchers 
    SET received_by_audit = TRUE 
    WHERE department = 'AUDIT' AND received_by_audit = FALSE
  `);
  console.log(`   ✅ Updated ${auditUpdate.affectedRows} vouchers with received_by_audit = TRUE`);

  // Set work_started = TRUE for vouchers that have been processed
  const [workUpdate] = await connection.execute(`
    UPDATE vouchers 
    SET work_started = TRUE 
    WHERE (pre_audited_by IS NOT NULL OR certified_by IS NOT NULL) AND work_started = FALSE
  `);
  console.log(`   ✅ Updated ${workUpdate.affectedRows} vouchers with work_started = TRUE`);

  // Set created_at for existing vouchers (use dispatch_time or current time)
  const [createdAtUpdate] = await connection.execute(`
    UPDATE vouchers 
    SET created_at = COALESCE(dispatch_time, NOW()) 
    WHERE created_at IS NULL
  `);
  console.log(`   ✅ Updated ${createdAtUpdate.affectedRows} vouchers with created_at timestamps`);

  // Step 5: Test batch creation functionality
  console.log('\n5. Testing batch creation functionality...');
  
  try {
    // Test inserting a sample batch to verify the schema works
    const testBatchId = 'test-batch-' + Date.now();
    await connection.execute(`
      INSERT INTO voucher_batches (
        id, department, voucher_ids, sent_by, sent_time, received, from_audit
      ) VALUES (?, 'FINANCE', '["test-voucher-id"]', 'SYSTEM TEST', NOW(), FALSE, TRUE)
    `, [testBatchId]);
    
    console.log('   ✅ Batch creation test successful');
    
    // Clean up test batch
    await connection.execute('DELETE FROM voucher_batches WHERE id = ?', [testBatchId]);
    console.log('   ✅ Test batch cleaned up');
    
  } catch (testError) {
    console.log('   ❌ Batch creation test failed:', testError.message);
  }

  // Step 6: Verify all fixes
  console.log('\n6. Verifying all schema fixes...');
  
  const [finalBatchColumns] = await connection.execute(`
    SELECT COLUMN_NAME 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'voucher_batches'
  `, [process.env.DB_NAME || 'vms_production']);
  
  const [finalVoucherColumns] = await connection.execute(`
    SELECT COLUMN_NAME 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'vouchers'
  `, [process.env.DB_NAME || 'vms_production']);
  
  const [finalSessionColumns] = await connection.execute(`
    SELECT COLUMN_NAME 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'active_sessions'
  `, [process.env.DB_NAME || 'vms_production']);
  
  const batchColumnsOk = finalBatchColumns.some(col => col.COLUMN_NAME === 'voucher_ids');
  const voucherColumnsOk = ['received_by_audit', 'work_started', 'flags', 'created_at'].every(col =>
    finalVoucherColumns.some(c => c.COLUMN_NAME === col)
  );
  const sessionColumnsOk = finalSessionColumns.some(col => col.COLUMN_NAME === 'role');
  
  console.log('\n📊 VERIFICATION RESULTS:');
  console.log(`   voucher_batches.voucher_ids: ${batchColumnsOk ? '✅ EXISTS' : '❌ MISSING'}`);
  console.log(`   vouchers.received_by_audit: ${finalVoucherColumns.some(c => c.COLUMN_NAME === 'received_by_audit') ? '✅ EXISTS' : '❌ MISSING'}`);
  console.log(`   vouchers.work_started: ${finalVoucherColumns.some(c => c.COLUMN_NAME === 'work_started') ? '✅ EXISTS' : '❌ MISSING'}`);
  console.log(`   vouchers.flags: ${finalVoucherColumns.some(c => c.COLUMN_NAME === 'flags') ? '✅ EXISTS' : '❌ MISSING'}`);
  console.log(`   vouchers.created_at: ${finalVoucherColumns.some(c => c.COLUMN_NAME === 'created_at') ? '✅ EXISTS' : '❌ MISSING'}`);
  console.log(`   active_sessions.role: ${sessionColumnsOk ? '✅ EXISTS' : '❌ MISSING'}`);

  if (batchColumnsOk && voucherColumnsOk && sessionColumnsOk) {
    console.log('\n🎉 DATABASE SCHEMA FIX COMPLETE!');
    console.log('✅ All missing columns have been added');
    console.log('✅ Existing data has been updated for consistency');
    console.log('✅ Batch creation functionality should now work');
    console.log('✅ Audit-to-Finance dispatch will create proper batch notifications');
    console.log('✅ Finance department will receive voucher batches correctly');
  } else {
    console.log('\n❌ SCHEMA FIX INCOMPLETE - Some columns are still missing');
  }

} catch (error) {
  console.error('\n❌ DATABASE SCHEMA FIX FAILED:', error);
  console.error('Stack:', error.stack);
} finally {
  await connection.end();
  console.log('\n🔌 Database connection closed');
}
