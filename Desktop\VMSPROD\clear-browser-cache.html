<!DOCTYPE html>
<html>
<head>
    <title>Clear VMS Browser Cache</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        .container {
            text-align: center;
            padding: 20px;
            border: 1px solid #333;
            border-radius: 8px;
        }
        button {
            background: #dc2626;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        button:hover {
            background: #b91c1c;
        }
        .success {
            color: #10b981;
            margin: 20px 0;
        }
        .info {
            color: #3b82f6;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 Clear VMS Browser Cache</h1>
        <p>This will clear all cached data from the VMS application including:</p>
        <ul style="text-align: left; display: inline-block;">
            <li>Voucher data</li>
            <li>Provisional cash records</li>
            <li>Batch notifications</li>
            <li>User session data</li>
            <li>All localStorage data</li>
        </ul>
        
        <div class="info">
            <strong>Current Storage Usage:</strong>
            <div id="storageInfo">Calculating...</div>
        </div>
        
        <button onclick="clearAllCache()">Clear All Cache</button>
        <button onclick="clearSpecificData()">Clear VMS Data Only</button>
        
        <div id="result"></div>
        
        <p style="margin-top: 30px; font-size: 14px; color: #666;">
            After clearing cache, refresh the VMS application to load fresh data from the server.
        </p>
    </div>

    <script>
        function updateStorageInfo() {
            try {
                let totalSize = 0;
                let itemCount = 0;
                
                for (let key in localStorage) {
                    if (localStorage.hasOwnProperty(key)) {
                        totalSize += localStorage[key].length;
                        itemCount++;
                    }
                }
                
                const sizeKB = (totalSize / 1024).toFixed(2);
                document.getElementById('storageInfo').innerHTML = 
                    `${itemCount} items, ${sizeKB} KB total`;
            } catch (error) {
                document.getElementById('storageInfo').innerHTML = 'Unable to calculate';
            }
        }
        
        function clearAllCache() {
            try {
                // Clear localStorage
                localStorage.clear();
                
                // Clear sessionStorage
                sessionStorage.clear();
                
                // Clear IndexedDB (if any)
                if ('indexedDB' in window) {
                    indexedDB.databases().then(databases => {
                        databases.forEach(db => {
                            indexedDB.deleteDatabase(db.name);
                        });
                    });
                }
                
                document.getElementById('result').innerHTML = 
                    '<div class="success">✅ All browser cache cleared successfully!</div>';
                
                updateStorageInfo();
                
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
                
            } catch (error) {
                document.getElementById('result').innerHTML = 
                    '<div style="color: #dc2626;">❌ Error clearing cache: ' + error.message + '</div>';
            }
        }
        
        function clearSpecificData() {
            try {
                // Clear VMS-specific localStorage keys
                const vmsKeys = [];
                for (let key in localStorage) {
                    if (key.includes('app-storage') || 
                        key.includes('vms') || 
                        key.includes('voucher') || 
                        key.includes('provisional') ||
                        key.includes('auth')) {
                        vmsKeys.push(key);
                    }
                }
                
                vmsKeys.forEach(key => {
                    localStorage.removeItem(key);
                });
                
                document.getElementById('result').innerHTML = 
                    `<div class="success">✅ Cleared ${vmsKeys.length} VMS-specific cache items!</div>`;
                
                updateStorageInfo();
                
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
                
            } catch (error) {
                document.getElementById('result').innerHTML = 
                    '<div style="color: #dc2626;">❌ Error clearing VMS cache: ' + error.message + '</div>';
            }
        }
        
        // Update storage info on page load
        updateStorageInfo();
        
        // Auto-refresh storage info every 5 seconds
        setInterval(updateStorageInfo, 5000);
    </script>
</body>
</html>
