const mysql = require('mysql2/promise');

async function testBatchCreationLogic() {
  console.log('🧪 Testing Batch Creation Logic');
  console.log('='.repeat(50));

  let connection;
  
  try {
    // Connect to database
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'vms@2025@1989',
      database: 'vms_production'
    });
    
    console.log('✅ Connected to database');

    // Step 1: Check current batches
    console.log('\n1. CURRENT BATCHES IN SYSTEM:');
    console.log('-'.repeat(40));
    
    const [batches] = await connection.execute(`
      SELECT id, department, sent_by, from_audit, received, sent_time
      FROM voucher_batches 
      ORDER BY sent_time DESC 
      LIMIT 10
    `);

    if (batches.length === 0) {
      console.log('📦 No batches found in system');
    } else {
      console.log(`📦 Found ${batches.length} recent batches:`);
      batches.forEach((batch, index) => {
        console.log(`   ${index + 1}. ID: ${batch.id.substring(0, 8)}...`);
        console.log(`      Department: ${batch.department}`);
        console.log(`      Sent By: ${batch.sent_by}`);
        console.log(`      From Audit: ${batch.from_audit ? 'YES' : 'NO'}`);
        console.log(`      Received: ${batch.received ? 'YES' : 'NO'}`);
        console.log(`      Sent Time: ${batch.sent_time}`);
        console.log('      ---');
      });
    }

    // Step 2: Analyze batch filtering logic
    console.log('\n2. BATCH FILTERING ANALYSIS:');
    console.log('-'.repeat(40));

    // Batches that should appear in AUDIT dashboard (from departments TO audit)
    const [auditBatches] = await connection.execute(`
      SELECT COUNT(*) as count
      FROM voucher_batches 
      WHERE from_audit = FALSE AND received = FALSE
    `);

    // Batches that should appear in DEPARTMENT dashboards (from audit TO departments)
    const [departmentBatches] = await connection.execute(`
      SELECT department, COUNT(*) as count
      FROM voucher_batches 
      WHERE from_audit = TRUE AND received = FALSE
      GROUP BY department
    `);

    console.log(`📊 Batches for AUDIT dashboard: ${auditBatches[0].count}`);
    console.log('   (These are batches FROM departments TO audit)');
    
    if (departmentBatches.length > 0) {
      console.log('\n📊 Batches for DEPARTMENT dashboards:');
      departmentBatches.forEach(dept => {
        console.log(`   ${dept.department}: ${dept.count} batches`);
        console.log('   (These are batches FROM audit TO this department)');
      });
    } else {
      console.log('\n📊 No batches for department dashboards');
    }

    // Step 3: Check for potential issues
    console.log('\n3. POTENTIAL ISSUES CHECK:');
    console.log('-'.repeat(40));

    // Check for batches with incorrect from_audit flag
    const [incorrectBatches] = await connection.execute(`
      SELECT id, department, sent_by, from_audit
      FROM voucher_batches 
      WHERE (sent_by LIKE '%AUDIT%' AND from_audit = FALSE)
         OR (sent_by NOT LIKE '%AUDIT%' AND from_audit = TRUE)
    `);

    if (incorrectBatches.length > 0) {
      console.log('🚨 FOUND BATCHES WITH INCORRECT from_audit FLAG:');
      incorrectBatches.forEach(batch => {
        console.log(`   ID: ${batch.id.substring(0, 8)}... | Dept: ${batch.department} | Sent By: ${batch.sent_by} | From Audit: ${batch.from_audit}`);
      });
    } else {
      console.log('✅ All batches have correct from_audit flags');
    }

    console.log('\n4. EXPECTED BEHAVIOR:');
    console.log('-'.repeat(40));
    console.log('✅ When AUDIT dispatches vouchers to FINANCE:');
    console.log('   - Batch created with department = "FINANCE"');
    console.log('   - Batch created with from_audit = TRUE');
    console.log('   - Batch appears in FINANCE dashboard (not AUDIT)');
    console.log('   - Batch shows as "NEWLY ARRIVED VOUCHERS" in FINANCE');
    
    console.log('\n✅ When FINANCE sends vouchers to AUDIT:');
    console.log('   - Batch created with department = "FINANCE"');
    console.log('   - Batch created with from_audit = FALSE');
    console.log('   - Batch appears in AUDIT dashboard');
    console.log('   - Batch shows as "NEWLY ARRIVED VOUCHERS" in AUDIT');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n✅ Database connection closed');
    }
  }
}

testBatchCreationLogic();
