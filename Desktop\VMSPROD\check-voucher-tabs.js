const mysql = require('mysql2/promise');

async function checkVoucherTabs() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root', 
    password: 'vms@2025@1989',
    database: 'vms_production'
  });
  
  console.log('🔍 Checking current voucher status...');
  
  const [vouchers] = await connection.execute('SELECT voucher_id, department, status, sent_to_audit FROM vouchers WHERE id = ?', ['d34ca623-d30c-4fd9-97e3-90ca0193bf84']);
  const voucher = vouchers[0];
  
  console.log('📄 Current voucher state:');
  console.log('   Voucher ID:', voucher.voucher_id);
  console.log('   Department:', voucher.department);
  console.log('   Status:', voucher.status);
  console.log('   Sent to Audit:', voucher.sent_to_audit);
  
  console.log('\n🧪 Tab filtering analysis:');
  console.log('   Should appear in PENDING tab:', 
    voucher.department === 'FINANCE' && 
    voucher.sent_to_audit === 0 && 
    (voucher.status === 'PENDING' || voucher.status === 'PENDING SUBMISSION') ? '✅ YES' : '❌ NO'
  );
  
  console.log('   Should appear in PROCESSING tab:', 
    voucher.sent_to_audit === 1 ? '✅ YES' : '❌ NO'
  );
  
  console.log('   Should appear in CERTIFIED tab:', 
    voucher.status === 'VOUCHER CERTIFIED' ? '✅ YES' : '❌ NO'
  );
  
  await connection.end();
}

checkVoucherTabs().catch(console.error);
