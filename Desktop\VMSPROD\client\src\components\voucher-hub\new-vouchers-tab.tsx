import React, { useState, useRef, useEffect } from 'react';

// Custom Fragment component that filters out external data attributes
const SafeFragment = ({ children, ...props }: { children: React.ReactNode; [key: string]: any }) => {
  // Filter out any data-* props that might be injected externally
  const { key, ...filteredProps } = props;
  const safeProps = Object.keys(filteredProps).reduce((acc, propKey) => {
    if (!propKey.startsWith('data-')) {
      acc[propKey] = filteredProps[propKey];
    }
    return acc;
  }, {} as any);

  return <React.Fragment key={key} {...safeProps}>{children}</React.Fragment>;
};


import { formatNumberWithCommas, formatVMSDateTime, formatVMSDate } from '@/utils/formatUtils';
import { Pencil, Check, X } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Card,
  CardContent
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Voucher, TaxType } from '@/lib/types';
import { SortableColumnHeader } from './sortable-column-header';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
  PopoverClose,
} from "@/components/ui/popover";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useAppStore } from '@/lib/store';
import { toast } from 'sonner';

interface NewVouchersTabProps {
  filteredVouchers: Voucher[];
  sortColumn: string | null;
  sortDirection: 'asc' | 'desc';
  handleSort: (column: string) => void;
  voucherEdits: Record<string, any>;
  handleVoucherEdit: (voucherId: string, field: string, value: any) => void;
  handleSaveVoucherEdits: (voucherId: string) => void;
  auditUsers: string[];
  setActiveTab?: (tab: string) => void;
  isEditable?: boolean;
}

export function NewVouchersTab({
  filteredVouchers,
  sortColumn,
  sortDirection,
  handleSort,
  voucherEdits,
  handleVoucherEdit,
  handleSaveVoucherEdits,
  auditUsers,
  setActiveTab,
}: NewVouchersTabProps) {
  const [editingVoucher, setEditingVoucher] = useState<string | null>(null);
  const [returnCommentMap, setReturnCommentMap] = useState<Record<string, string>>({});
  const [showReturnCommentMap, setShowReturnCommentMap] = useState<Record<string, boolean>>({});

  // Store hooks
  const updateVoucher = useAppStore((state) => state.updateVoucher);

  // Refs for synchronized scrolling
  const headerRef = useRef<HTMLDivElement>(null);
  const bodyRef = useRef<HTMLDivElement>(null);

  // Set up synchronized scrolling between header and body
  useEffect(() => {
    const headerElement = headerRef.current;
    const bodyElement = bodyRef.current;

    if (!headerElement || !bodyElement) return;

    const handleHeaderScroll = () => {
      if (bodyElement) {
        bodyElement.scrollLeft = headerElement.scrollLeft;
      }
    };

    const handleBodyScroll = () => {
      if (headerElement) {
        headerElement.scrollLeft = bodyElement.scrollLeft;
      }
    };

    headerElement.addEventListener('scroll', handleHeaderScroll);
    bodyElement.addEventListener('scroll', handleBodyScroll);

    return () => {
      headerElement.removeEventListener('scroll', handleHeaderScroll);
      bodyElement.removeEventListener('scroll', handleBodyScroll);
    };
  }, []);
  const [openTaxPopover, setOpenTaxPopover] = useState<string | null>(null);
  const [openOptionsPopover, setOpenOptionsPopover] = useState<string | null>(null);

  const handleStartEditing = (voucherId: string) => {
    setEditingVoucher(voucherId);

    if (!voucherEdits[voucherId]) {
      const voucher = filteredVouchers.find(v => v.id === voucherId);
      if (voucher) {
        handleVoucherEdit(voucherId, 'preAuditedAmount', voucher.amount);
      }
    }
  };

  const handleCancelEditing = (voucherId: string) => {
    setEditingVoucher(null);
    setOpenTaxPopover(null);
    setOpenOptionsPopover(null);

    if (voucherEdits[voucherId]) {
      handleVoucherEdit(voucherId, null, null);
    }
  };

  const handleSave = (voucherId: string) => {
    handleSaveVoucherEdits(voucherId);
    setEditingVoucher(null);
    setOpenTaxPopover(null);
    setOpenOptionsPopover(null);
  };

  const handleReturnVoucherToggle = (voucherId: string, isChecked: boolean) => {
    if (isChecked) {
      setShowReturnCommentMap(prev => ({...prev, [voucherId]: true}));
    } else {
      handleVoucherEdit(voucherId, 'isReturned', false);
      setShowReturnCommentMap(prev => ({ ...prev, [voucherId]: false }));
    }
  };

  const handleReturnCommentChange = (voucherId: string, comment: string) => {
    setReturnCommentMap(prev => ({ ...prev, [voucherId]: comment }));
  };



  const handleConfirmReturn = (voucherId: string) => {
    const comment = returnCommentMap[voucherId] || 'NO COMMENT PROVIDED';

    console.log(`Confirming return for voucher ${voucherId} with comment: "${comment}"`);

    handleVoucherEdit(voucherId, 'isReturned', true);
    handleVoucherEdit(voucherId, 'returnComment', comment);
    handleVoucherEdit(voucherId, 'comment', comment);

    handleSaveVoucherEdits(voucherId);

    setShowReturnCommentMap(prev => ({ ...prev, [voucherId]: false }));
    setReturnCommentMap(prev => {
      const newMap = { ...prev };
      delete newMap[voucherId];
      return newMap;
    });
  };

  const handleCancelReturn = (voucherId: string) => {
    setShowReturnCommentMap(prev => ({ ...prev, [voucherId]: false }));
    setReturnCommentMap(prev => {
      const newMap = { ...prev };
      delete newMap[voucherId];
      return newMap;
    });
  };

  const getTaxOptions = (): TaxType[] => [
    "NONE",
    "GOODS 3%",
    "SERVICE 7.5%",
    "WORKS 5%",
    "RENT 8%",
    "PCC 12.5%",
    "RISK 5%",
    "VEH.MAINT 10%",
    "OTHER"
  ];

  const getReturnOptions = () => [
    { value: "NO", label: "NO" },
    { value: "YES", label: "YES" },
  ];

  const getProvisionalCashOptions = () => [
    { value: "NO", label: "NO" },
    { value: "YES", label: "YES" },
  ];

  return (
    <div className="space-y-2">
      <div className="flex flex-col rounded-md border">
        {/* Fixed header */}
        <div className="sticky top-0 z-10 bg-background overflow-hidden">
          <div ref={headerRef} className="overflow-x-auto scrollbar-visible" style={{ scrollbarWidth: 'thin' }}>
            <table className="w-full table-fixed" style={{ tableLayout: 'fixed', minWidth: '1500px' }}>
            <thead>
              <tr className="bg-background">
                <SortableColumnHeader
                  title="VOUCHER ID"
                  sortKey="voucherId"
                  currentSortColumn={sortColumn}
                  currentSortDirection={sortDirection}
                  onSort={handleSort}
                  className="whitespace-nowrap w-[8%] px-4 text-center"
                />
                <SortableColumnHeader
                  title="DATE RECEIVED"
                  sortKey="date"
                  currentSortColumn={sortColumn}
                  currentSortDirection={sortDirection}
                  onSort={handleSort}
                  className="whitespace-nowrap w-[12%] px-4 text-center"
                />
                <SortableColumnHeader
                  title="CLAIMANT"
                  sortKey="claimant"
                  currentSortColumn={sortColumn}
                  currentSortDirection={sortDirection}
                  onSort={handleSort}
                  className="whitespace-nowrap w-[12%] px-4 text-center"
                />
                <SortableColumnHeader
                  title="DESCRIPTION"
                  sortKey="description"
                  currentSortColumn={sortColumn}
                  currentSortDirection={sortDirection}
                  onSort={handleSort}
                  className="whitespace-nowrap w-[15%] px-4 text-center"
                />
                <SortableColumnHeader
                  title="AMOUNT"
                  sortKey="amount"
                  currentSortColumn={sortColumn}
                  currentSortDirection={sortDirection}
                  onSort={handleSort}
                  className="whitespace-nowrap w-[8%] px-4 text-center"
                />
                <TableHead className="uppercase whitespace-nowrap sticky top-0 bg-background z-10 w-[12%] px-4 text-center">CERTIFIED AMT</TableHead>
                <TableHead className="uppercase whitespace-nowrap sticky top-0 bg-background z-10 w-[10%] px-4 text-center">TAX</TableHead>
                <TableHead className="uppercase whitespace-nowrap sticky top-0 bg-background z-10 w-[12%] px-4 text-center">PRE-AUDITED BY</TableHead>
                <TableHead className="uppercase whitespace-nowrap sticky top-0 bg-background z-10 w-[12%] px-4 text-center">CERTIFIED BY</TableHead>
                <TableHead className="uppercase whitespace-nowrap sticky top-0 bg-background z-10 w-[10%] px-4 text-center">OTHER ACTIONS</TableHead>
                <TableHead className="uppercase whitespace-nowrap sticky top-0 bg-background z-10 w-[7%] px-4 text-center">SAVE</TableHead>
              </tr>
            </thead>
            </table>
          </div>
        </div>

        {/* Scrollable body */}
        <div className="overflow-auto h-[60vh] scrollbar-visible" style={{ scrollbarWidth: 'thin', overflowY: 'scroll' }}>
          <div ref={bodyRef} className="overflow-x-auto scrollbar-visible" style={{ scrollbarWidth: 'thin' }}>
            <table className="w-full table-fixed" style={{ tableLayout: 'fixed', minWidth: '1500px' }}>
              <tbody>
              {filteredVouchers.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={11} className="h-24 text-center uppercase">
                    NO NEW VOUCHERS FOUND.
                  </TableCell>
                </TableRow>
              ) : (
                filteredVouchers.map((voucher) => {
                  const isEditing = editingVoucher === voucher.id;
                  const edits = voucherEdits[voucher.id] || {};
                  const showReturnComment = showReturnCommentMap[voucher.id] || false;

                  return (
                    <SafeFragment key={voucher.id}>
                      <TableRow
                        className="hover:bg-muted/50 cursor-pointer"
                        data-voucher-id={voucher.id}
                        onClick={() => {
                          if (!isEditing) {
                            setEditingVoucher(voucher.id);
                            setVoucherEdits(prev => ({
                              ...prev,
                              [voucher.id]: {
                                claimant: voucher.claimant,
                                description: voucher.description,
                                amount: voucher.amount,
                                date: voucher.date
                              }
                            }));
                          }
                        }}
                      >
                        <TableCell className="font-medium uppercase w-[8%] px-4 text-center">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <span className="block truncate">{voucher.voucherId}</span>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{voucher.voucherId}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </TableCell>
                        <TableCell className="uppercase whitespace-nowrap overflow-hidden text-ellipsis w-[12%] px-4 text-center">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <span className="block truncate">
                                  {voucher.receiptTime ? formatVMSDateTime(voucher.receiptTime) : 'Not Received'}
                                </span>
                              </TooltipTrigger>
                              <TooltipContent>
                                <div className="text-center">
                                  <p className="font-semibold">Date Received by Audit</p>
                                  <p>{voucher.receiptTime ? formatVMSDateTime(voucher.receiptTime) : 'Not yet received'}</p>
                                  {voucher.receivedBy && (
                                    <>
                                      <p className="font-semibold mt-2">Received By</p>
                                      <p>{voucher.receivedBy}</p>
                                    </>
                                  )}
                                </div>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </TableCell>
                        <TableCell className="uppercase whitespace-nowrap overflow-hidden text-ellipsis w-[12%] px-4 text-center">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <span className="block truncate">{voucher.claimant}</span>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{voucher.claimant}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </TableCell>
                        <TableCell className="uppercase overflow-hidden text-ellipsis w-[15%] px-4 text-center">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <span className="block truncate">{voucher.description}</span>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{voucher.description}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </TableCell>
                        <TableCell className="uppercase whitespace-nowrap text-xs w-[8%] px-4 text-center">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <span className="block truncate">{typeof voucher.amount === 'number' ? voucher.amount.toFixed(2) : parseFloat(voucher.amount || '0').toFixed(2)} {voucher.currency}</span>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{typeof voucher.amount === 'number' ? voucher.amount.toFixed(2) : parseFloat(voucher.amount || '0').toFixed(2)} {voucher.currency}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </TableCell>
                        <TableCell className="uppercase text-xs w-[12%] px-4 text-center">
                          {isEditing ? (
                            <Input
                              type="number"
                              value={edits.preAuditedAmount || ''}
                              onChange={(e) => {
                                const value = e.target.value;
                                handleVoucherEdit(voucher.id, 'preAuditedAmount', value ? parseFloat(value) : undefined);
                              }}
                              placeholder="e.g., 200.012"
                              min="0"
                              step="0.001"
                              className="w-full uppercase"
                            />
                          ) : (
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <span className="block truncate">
                                    {voucher.preAuditedAmount ? `${formatNumberWithCommas(voucher.preAuditedAmount)} ${voucher.currency}` : '-'}
                                  </span>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>{voucher.preAuditedAmount ? `${formatNumberWithCommas(voucher.preAuditedAmount)} ${voucher.currency}` : '-'}</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          )}
                        </TableCell>
                        <TableCell className="uppercase w-[10%] px-4 text-center">
                          {isEditing ? (
                            <Popover
                              open={openTaxPopover === voucher.id}
                              onOpenChange={(open) => {
                                setOpenTaxPopover(open ? voucher.id : null);
                              }}
                            >
                              <PopoverTrigger asChild>
                                <Button
                                  variant="outline"
                                  className="w-[100px] uppercase bg-transparent border-white/10"
                                >
                                  {edits.taxType && edits.taxType !== 'NONE' ? edits.taxType : 'TAX'}
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent className="w-[400px] p-0 bg-background border-white/10" align="start">
                                <div className="p-4 space-y-4">
                                  <div className="flex justify-between items-center">
                                    <h4 className="font-medium text-lg uppercase">TAX DETAILS</h4>
                                    <PopoverClose asChild>
                                      <Button
                                        variant="ghost"
                                        className="text-xs"
                                        onClick={() => {
                                          setOpenTaxPopover(null);
                                        }}
                                      >
                                        OK
                                      </Button>
                                    </PopoverClose>
                                  </div>

                                  <div className="grid grid-cols-2 gap-4">
                                    <div className="space-y-2">
                                      <label className="text-sm uppercase">Tax Type</label>
                                      <Select
                                        value={edits.taxType || 'NONE'}
                                        onValueChange={(value) => handleVoucherEdit(voucher.id, 'taxType', value === 'NONE' ? '' : value)}
                                      >
                                        <SelectTrigger className="w-full bg-transparent border-white/10 uppercase">
                                          <SelectValue placeholder="SELECT TYPE" />
                                        </SelectTrigger>
                                        <SelectContent>
                                          <SelectItem value="" className="uppercase">SELECT TAX TYPE</SelectItem>
                                          {getTaxOptions().map((tax) => (
                                            <SelectItem key={tax} value={tax}>
                                              {tax}
                                            </SelectItem>
                                          ))}
                                        </SelectContent>
                                      </Select>
                                    </div>

                                    <div className="space-y-2">
                                      <label className="text-sm uppercase">Tax Amount</label>
                                      <Input
                                        type="number"
                                        value={edits.taxAmount || ''}
                                        onChange={(e) => {
                                          const value = e.target.value;
                                          handleVoucherEdit(voucher.id, 'taxAmount', value ? parseFloat(value) : undefined);
                                        }}
                                        className="w-full bg-transparent border-white/10"
                                        placeholder="e.g., 15.125"
                                        min="0"
                                        step="0.001"
                                        disabled={!edits.taxType || edits.taxType === 'NONE'}
                                      />
                                    </div>
                                  </div>
                                </div>
                              </PopoverContent>
                            </Popover>
                          ) : (
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <span className="block truncate">
                                    {voucher.taxType ?
                                      (voucher.taxAmount ?
                                        `${voucher.taxType}: ${formatNumberWithCommas(voucher.taxAmount)} ${voucher.currency}` :
                                        voucher.taxType) :
                                      '-'}
                                  </span>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>
                                    {voucher.taxType ?
                                      (voucher.taxAmount ?
                                        `${voucher.taxType}: ${formatNumberWithCommas(voucher.taxAmount)} ${voucher.currency}` :
                                        voucher.taxType) :
                                      '-'}
                                  </p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          )}
                        </TableCell>
                        <TableCell className="uppercase w-[12%] px-4 text-center">
                          {isEditing ? (
                            <Select
                              value={edits.preAuditedBy || ''}
                              onValueChange={(value) => handleVoucherEdit(voucher.id, 'preAuditedBy', value)}
                            >
                              <SelectTrigger className="uppercase w-full bg-transparent border-white/10">
                                <SelectValue placeholder={edits.preAuditedBy || 'SELECT'} />
                              </SelectTrigger>
                              <SelectContent className="uppercase">
                                <SelectItem value="" className="uppercase">SELECT PERSON</SelectItem>
                                {auditUsers.map((user) => (
                                  <SelectItem key={user} value={user}>
                                    {user}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          ) : (
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <span className="block truncate">
                                    {voucher.preAuditedBy || '-'}
                                  </span>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>{voucher.preAuditedBy || '-'}</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          )}
                        </TableCell>

                        <TableCell className="uppercase w-[12%] px-4 text-center">
                          {isEditing ? (
                            <Select
                              value={edits.certifiedBy || ''}
                              onValueChange={(value) => handleVoucherEdit(voucher.id, 'certifiedBy', value)}
                            >
                              <SelectTrigger className="w-full uppercase bg-transparent border-white/10">
                                <SelectValue placeholder={edits.certifiedBy || 'SELECT'} />
                              </SelectTrigger>
                              <SelectContent className="uppercase">
                                <SelectItem value="" className="uppercase">SELECT PERSON</SelectItem>
                                {auditUsers.map((user) => (
                                  <SelectItem key={user} value={user}>
                                    {user}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          ) : (
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <span className="block truncate">
                                    {voucher.certifiedBy || '-'}
                                  </span>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>{voucher.certifiedBy || '-'}</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          )}
                        </TableCell>
                        <TableCell className="uppercase w-[10%] px-4 text-center">
                          {isEditing ? (
                            <Popover
                              open={openOptionsPopover === voucher.id}
                              onOpenChange={(open) => {
                                setOpenOptionsPopover(open ? voucher.id : null);
                              }}
                            >
                              <PopoverTrigger asChild>
                                <Button
                                  variant="outline"
                                  className="uppercase bg-transparent border-white/10"
                                >
                                  OPTIONS
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent className="w-[400px] p-0 bg-background border-white/10" align="start">
                                <div className="p-4 space-y-4">
                                  <div className="flex justify-between items-center">
                                    <h4 className="font-medium text-lg uppercase">OTHER OPTIONS</h4>
                                    <PopoverClose asChild>
                                      <Button
                                        variant="ghost"
                                        className="text-xs"
                                        onClick={() => {
                                          setOpenOptionsPopover(null);
                                        }}
                                      >
                                        OK
                                      </Button>
                                    </PopoverClose>
                                  </div>

                                  <div className="space-y-4">
                                    <div className="space-y-2">
                                      <label className="text-sm uppercase">Post Provisional Cash</label>
                                      <Select
                                        value={edits.postProvisionalCash ? "YES" : "NO"}
                                        onValueChange={(value) => handleVoucherEdit(voucher.id, 'postProvisionalCash', value === "YES")}
                                      >
                                        <SelectTrigger className="w-full bg-transparent border-white/10">
                                          <SelectValue placeholder="Select" />
                                        </SelectTrigger>
                                        <SelectContent>
                                          <SelectItem value="" className="uppercase">SELECT OPTION</SelectItem>
                                          {getProvisionalCashOptions().map(option => (
                                            <SelectItem key={option.value} value={option.value}>
                                              {option.label}
                                            </SelectItem>
                                          ))}
                                        </SelectContent>
                                      </Select>
                                    </div>

                                    <div className="space-y-2">
                                      <label className="text-sm uppercase">Return Voucher</label>
                                      <Select
                                        value={edits.isReturned ? "YES" : "NO"}
                                        onValueChange={(value) => {
                                          handleVoucherEdit(voucher.id, 'isReturned', value === "YES");
                                          if (value === "YES") {
                                            setShowReturnCommentMap(prev => ({ ...prev, [voucher.id]: true }));
                                          } else {
                                            setShowReturnCommentMap(prev => ({ ...prev, [voucher.id]: false }));
                                          }
                                        }}
                                      >
                                        <SelectTrigger className="w-full bg-transparent border-white/10">
                                          <SelectValue placeholder="Select" />
                                        </SelectTrigger>
                                        <SelectContent>
                                          <SelectItem value="" className="uppercase">SELECT OPTION</SelectItem>
                                          {getReturnOptions().map(option => (
                                            <SelectItem key={option.value} value={option.value}>
                                              {option.label}
                                            </SelectItem>
                                          ))}
                                        </SelectContent>
                                      </Select>
                                    </div>
                                  </div>
                                </div>
                              </PopoverContent>
                            </Popover>
                          ) : (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleStartEditing(voucher.id);
                              }}
                              className="bg-blue-600 hover:bg-blue-700 text-white"
                            >
                              <Pencil className="h-4 w-4" />
                              <span className="ml-2">EDIT</span>
                            </Button>
                          )}
                        </TableCell>
                        <TableCell className="uppercase w-[7%] px-4 text-center">
                          {isEditing && (
                            <div className="flex space-x-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleCancelEditing(voucher.id)}
                                className="text-red-500 hover:text-red-700 hover:bg-red-50"
                              >
                                <X className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="default"
                                size="sm"
                                onClick={() => handleSave(voucher.id)}
                                className="bg-green-600 hover:bg-green-700"
                              >
                                SAVE
                              </Button>
                            </div>
                          )}
                        </TableCell>
                      </TableRow>

                      {showReturnComment && (
                        <TableRow>
                          <TableCell colSpan={11} className="bg-muted/30">
                            <Card>
                              <CardContent className="pt-4">
                                <div className="space-y-4">
                                  <div>
                                    <Label htmlFor={`return-comment-${voucher.id}`} className="text-sm font-medium uppercase">
                                      Return Comment
                                    </Label>
                                    <Textarea
                                      id={`return-comment-${voucher.id}`}
                                      placeholder="Enter reason for returning this voucher"
                                      value={returnCommentMap[voucher.id] || ''}
                                      onChange={(e) => handleReturnCommentChange(voucher.id, e.target.value)}
                                      className="mt-1"
                                      rows={3}
                                    />
                                  </div>
                                  <div className="flex justify-end space-x-2">
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => handleCancelReturn(voucher.id)}
                                    >
                                      CANCEL
                                    </Button>
                                    <Button
                                      size="sm"
                                      onClick={() => handleConfirmReturn(voucher.id)}
                                      className="bg-red-500 hover:bg-red-600"
                                      disabled={!returnCommentMap[voucher.id]}
                                    >
                                      CONFIRM RETURN
                                    </Button>
                                  </div>
                                </div>
                              </CardContent>
                            </Card>
                          </TableCell>
                        </TableRow>
                      )}
                    </SafeFragment>
                  );
                })
              )}
              </tbody>
            </table>
          </div>
        </div>
      </div>


    </div>
  );
}
