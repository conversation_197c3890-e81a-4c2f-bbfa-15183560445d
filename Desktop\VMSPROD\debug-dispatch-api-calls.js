/**
 * DEBUG SCRIPT: Monitor API calls during voucher dispatch
 * 
 * This script will help identify the exact API calls being made when
 * dispatching vouchers from AUDIT to FINANCE and why they're failing.
 */

import axios from 'axios';
import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: './server/.env' });

const API_BASE = 'http://localhost:8080/api';
const connection = await mysql.createConnection({
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'vms@2025@1989',
  database: process.env.DB_NAME || 'vms_production'
});

console.log('🔍 DEBUGGING VOUCHER DISPATCH API CALLS');
console.log('========================================');

let sessionCookie = '';

try {
  // Step 1: Login as AUDIT user
  console.log('\n1. Logging in as AUDIT user...');
  const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
    department: 'AUDIT',
    username: 'SAMUEL ASIEDU',
    password: '123'
  }, {
    withCredentials: true
  });

  // Extract session cookie
  const cookies = loginResponse.headers['set-cookie'];
  if (cookies) {
    sessionCookie = cookies.find(cookie => cookie.startsWith('connect.sid='));
    console.log('✅ Login successful, session cookie obtained');
  }

  // Step 2: Get vouchers in AUDIT department
  console.log('\n2. Fetching vouchers in AUDIT department...');
  const vouchersResponse = await axios.get(`${API_BASE}/vouchers?department=AUDIT`, {
    withCredentials: true,
    headers: { 'Cookie': sessionCookie }
  });

  const auditVouchers = vouchersResponse.data.filter(v => 
    v.status === 'AUDIT: PROCESSING' && !v.deleted
  );

  console.log(`Found ${auditVouchers.length} vouchers in AUDIT processing`);

  if (auditVouchers.length === 0) {
    console.log('❌ No vouchers available for dispatch testing');
    process.exit(1);
  }

  const testVoucher = auditVouchers[0];
  console.log(`\n📋 Test voucher: ${testVoucher.voucherId} (${testVoucher.id})`);
  console.log(`   Status: ${testVoucher.status}`);
  console.log(`   Department: ${testVoucher.department}`);

  // Step 3: Test voucher update (this is what's failing)
  console.log('\n3. Testing voucher update (simulating dispatch)...');
  
  const updateData = {
    auditDispatchedBy: 'SAMUEL ASIEDU',
    dispatched: true,
    status: 'VOUCHER CERTIFIED',
    auditDispatchTime: new Date().toISOString()
  };

  console.log('📤 Sending update data:', updateData);

  try {
    const updateResponse = await axios.put(`${API_BASE}/vouchers/${testVoucher.id}`, updateData, {
      withCredentials: true,
      headers: { 
        'Cookie': sessionCookie,
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ Voucher update successful:', updateResponse.status);
    console.log('📋 Updated voucher data:', {
      id: updateResponse.data.id,
      status: updateResponse.data.status,
      auditDispatchedBy: updateResponse.data.auditDispatchedBy,
      dispatched: updateResponse.data.dispatched
    });

  } catch (updateError) {
    console.log('❌ VOUCHER UPDATE FAILED!');
    console.log('Status:', updateError.response?.status);
    console.log('Error message:', updateError.response?.data);
    console.log('Full error:', updateError.message);

    // Check server logs for more details
    console.log('\n🔍 Checking server logs for error details...');
    
    // Let's also check what fields are being rejected
    if (updateError.response?.status === 400) {
      console.log('\n🔍 Testing individual fields to identify the problematic one...');
      
      const fieldsToTest = [
        { auditDispatchedBy: 'SAMUEL ASIEDU' },
        { dispatched: true },
        { status: 'VOUCHER CERTIFIED' },
        { auditDispatchTime: new Date().toISOString() }
      ];

      for (const fieldTest of fieldsToTest) {
        try {
          console.log(`   Testing field:`, fieldTest);
          await axios.put(`${API_BASE}/vouchers/${testVoucher.id}`, fieldTest, {
            withCredentials: true,
            headers: { 
              'Cookie': sessionCookie,
              'Content-Type': 'application/json'
            }
          });
          console.log(`   ✅ Field accepted:`, Object.keys(fieldTest)[0]);
        } catch (fieldError) {
          console.log(`   ❌ Field rejected:`, Object.keys(fieldTest)[0]);
          console.log(`      Error:`, fieldError.response?.data?.error || fieldError.message);
        }
      }
    }
  }

  // Step 4: Test batch creation (this should work if voucher update works)
  console.log('\n4. Testing batch creation...');
  
  try {
    const batchData = {
      department: 'FINANCE',
      voucherIds: [testVoucher.id],
      fromAudit: true
    };

    console.log('📤 Creating batch with data:', batchData);

    const batchResponse = await axios.post(`${API_BASE}/batches`, batchData, {
      withCredentials: true,
      headers: { 
        'Cookie': sessionCookie,
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ Batch creation successful:', batchResponse.status);
    console.log('📦 Created batch:', {
      id: batchResponse.data.id,
      department: batchResponse.data.department,
      fromAudit: batchResponse.data.from_audit,
      voucherCount: batchResponse.data.vouchers?.length || 0
    });

  } catch (batchError) {
    console.log('❌ BATCH CREATION FAILED!');
    console.log('Status:', batchError.response?.status);
    console.log('Error message:', batchError.response?.data);
    console.log('Full error:', batchError.message);
  }

  // Step 5: Check database state
  console.log('\n5. Checking database state...');
  
  const [voucherCheck] = await connection.execute(
    'SELECT id, voucher_id, status, audit_dispatched_by, dispatched FROM vouchers WHERE id = ?',
    [testVoucher.id]
  );

  console.log('📊 Current voucher state in database:', voucherCheck[0]);

  const [batchCheck] = await connection.execute(
    'SELECT * FROM voucher_batches WHERE department = ? ORDER BY sent_time DESC LIMIT 1',
    ['FINANCE']
  );

  if (batchCheck.length > 0) {
    console.log('📦 Latest FINANCE batch:', {
      id: batchCheck[0].id,
      department: batchCheck[0].department,
      from_audit: batchCheck[0].from_audit,
      sent_time: batchCheck[0].sent_time
    });
  } else {
    console.log('📦 No FINANCE batches found');
  }

} catch (error) {
  console.error('❌ Script execution failed:', error);
} finally {
  await connection.end();
  console.log('\n🔌 Database connection closed');
}
