/**
 * PRODUCTION-<PERSON><PERSON><PERSON> PORT MANAGEMENT
 * Handles automatic port discovery and conflict resolution
 */
export interface PortConfig {
    preferredPort: number;
    fallbackPorts: number[];
    maxRetries: number;
}
export interface ServerInfo {
    port: number;
    host: string;
    url: string;
}
/**
 * Check if a port is available
 */
export declare function checkPortAvailable(port: number, host?: string): Promise<boolean>;
/**
 * Find an available port from a list of preferred ports
 */
export declare function findAvailablePort(config: PortConfig): Promise<number>;
/**
 * Get server network information
 */
export declare function getServerInfo(port: number): ServerInfo;
/**
 * Broadcast server information for client discovery
 */
export declare function broadcastServerInfo(serverInfo: ServerInfo): void;
/**
 * Save server info to a shared network location for client discovery
 */
export declare function saveServerInfoToNetwork(serverInfo: ServerInfo): void;
/**
 * Monitor port and restart if needed
 */
export declare function monitorPort(port: number, restartCallback: () => void): void;
