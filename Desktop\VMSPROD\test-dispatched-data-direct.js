const mysql = require('mysql2/promise');

async function testDispatchedDataDirect() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root', 
    password: 'vms@2025@1989',
    database: 'vms_production'
  });
  
  console.log('🔍 Testing DISPATCHED tab data directly from database...');
  
  // Get all FINANCE vouchers that should appear in DISPATCHED tab
  const [vouchers] = await connection.execute(`
    SELECT voucher_id, department, original_department, status, dispatched, audit_dispatched_by, sent_to_audit
    FROM vouchers 
    WHERE original_department = 'FINANCE' 
    AND dispatched = TRUE
    ORDER BY voucher_id
  `);
  
  console.log(`📊 FINANCE vouchers with dispatched = TRUE: ${vouchers.length}`);
  
  if (vouchers.length > 0) {
    console.log('📋 Vouchers that should appear in DISPATCHED tab:');
    vouchers.forEach((voucher, index) => {
      console.log(`   ${index + 1}. ${voucher.voucher_id}`);
      console.log(`      Department: ${voucher.department}`);
      console.log(`      Original Department: ${voucher.original_department}`);
      console.log(`      Status: ${voucher.status}`);
      console.log(`      Dispatched: ${voucher.dispatched}`);
      console.log(`      Audit Dispatched By: ${voucher.audit_dispatched_by || 'N/A'}`);
      console.log(`      Sent to Audit: ${voucher.sent_to_audit}`);
      console.log('');
    });
  } else {
    console.log('❌ No FINANCE vouchers found with dispatched = TRUE');
    
    // Check if there are any FINANCE vouchers at all
    const [allFinanceVouchers] = await connection.execute(`
      SELECT voucher_id, department, original_department, status, dispatched, audit_dispatched_by
      FROM vouchers 
      WHERE original_department = 'FINANCE' 
      ORDER BY voucher_id
    `);
    
    console.log(`📊 Total FINANCE vouchers (any status): ${allFinanceVouchers.length}`);
    
    if (allFinanceVouchers.length > 0) {
      console.log('📋 All FINANCE vouchers:');
      allFinanceVouchers.forEach((voucher, index) => {
        console.log(`   ${index + 1}. ${voucher.voucher_id} - Dispatched: ${voucher.dispatched} - Status: ${voucher.status}`);
      });
    }
  }
  
  await connection.end();
}

testDispatchedDataDirect().catch(console.error);
