{"version": 3, "sources": ["../../@radix-ui/react-scroll-area/src/ScrollArea.tsx", "../../@radix-ui/react-scroll-area/src/useStateMachine.ts"], "sourcesContent": ["/// <reference types=\"resize-observer-browser\" />\n\nimport * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { Presence } from '@radix-ui/react-presence';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { clamp } from '@radix-ui/number';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useStateMachine } from './useStateMachine';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Direction = 'ltr' | 'rtl';\ntype Sizes = {\n  content: number;\n  viewport: number;\n  scrollbar: {\n    size: number;\n    paddingStart: number;\n    paddingEnd: number;\n  };\n};\n\n/* -------------------------------------------------------------------------------------------------\n * ScrollArea\n * -----------------------------------------------------------------------------------------------*/\n\nconst SCROLL_AREA_NAME = 'ScrollArea';\n\ntype ScopedProps<P> = P & { __scopeScrollArea?: Scope };\nconst [createScrollAreaContext, createScrollAreaScope] = createContextScope(SCROLL_AREA_NAME);\n\ntype ScrollAreaContextValue = {\n  type: 'auto' | 'always' | 'scroll' | 'hover';\n  dir: Direction;\n  scrollHideDelay: number;\n  scrollArea: ScrollAreaElement | null;\n  viewport: ScrollAreaViewportElement | null;\n  onViewportChange(viewport: ScrollAreaViewportElement | null): void;\n  content: HTMLDivElement | null;\n  onContentChange(content: HTMLDivElement): void;\n  scrollbarX: ScrollAreaScrollbarElement | null;\n  onScrollbarXChange(scrollbar: ScrollAreaScrollbarElement | null): void;\n  scrollbarXEnabled: boolean;\n  onScrollbarXEnabledChange(rendered: boolean): void;\n  scrollbarY: ScrollAreaScrollbarElement | null;\n  onScrollbarYChange(scrollbar: ScrollAreaScrollbarElement | null): void;\n  scrollbarYEnabled: boolean;\n  onScrollbarYEnabledChange(rendered: boolean): void;\n  onCornerWidthChange(width: number): void;\n  onCornerHeightChange(height: number): void;\n};\n\nconst [ScrollAreaProvider, useScrollAreaContext] =\n  createScrollAreaContext<ScrollAreaContextValue>(SCROLL_AREA_NAME);\n\ntype ScrollAreaElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface ScrollAreaProps extends PrimitiveDivProps {\n  type?: ScrollAreaContextValue['type'];\n  dir?: ScrollAreaContextValue['dir'];\n  scrollHideDelay?: number;\n}\n\nconst ScrollArea = React.forwardRef<ScrollAreaElement, ScrollAreaProps>(\n  (props: ScopedProps<ScrollAreaProps>, forwardedRef) => {\n    const {\n      __scopeScrollArea,\n      type = 'hover',\n      dir,\n      scrollHideDelay = 600,\n      ...scrollAreaProps\n    } = props;\n    const [scrollArea, setScrollArea] = React.useState<ScrollAreaElement | null>(null);\n    const [viewport, setViewport] = React.useState<ScrollAreaViewportElement | null>(null);\n    const [content, setContent] = React.useState<HTMLDivElement | null>(null);\n    const [scrollbarX, setScrollbarX] = React.useState<ScrollAreaScrollbarElement | null>(null);\n    const [scrollbarY, setScrollbarY] = React.useState<ScrollAreaScrollbarElement | null>(null);\n    const [cornerWidth, setCornerWidth] = React.useState(0);\n    const [cornerHeight, setCornerHeight] = React.useState(0);\n    const [scrollbarXEnabled, setScrollbarXEnabled] = React.useState(false);\n    const [scrollbarYEnabled, setScrollbarYEnabled] = React.useState(false);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setScrollArea(node));\n    const direction = useDirection(dir);\n\n    return (\n      <ScrollAreaProvider\n        scope={__scopeScrollArea}\n        type={type}\n        dir={direction}\n        scrollHideDelay={scrollHideDelay}\n        scrollArea={scrollArea}\n        viewport={viewport}\n        onViewportChange={setViewport}\n        content={content}\n        onContentChange={setContent}\n        scrollbarX={scrollbarX}\n        onScrollbarXChange={setScrollbarX}\n        scrollbarXEnabled={scrollbarXEnabled}\n        onScrollbarXEnabledChange={setScrollbarXEnabled}\n        scrollbarY={scrollbarY}\n        onScrollbarYChange={setScrollbarY}\n        scrollbarYEnabled={scrollbarYEnabled}\n        onScrollbarYEnabledChange={setScrollbarYEnabled}\n        onCornerWidthChange={setCornerWidth}\n        onCornerHeightChange={setCornerHeight}\n      >\n        <Primitive.div\n          dir={direction}\n          {...scrollAreaProps}\n          ref={composedRefs}\n          style={{\n            position: 'relative',\n            // Pass corner sizes as CSS vars to reduce re-renders of context consumers\n            ['--radix-scroll-area-corner-width' as any]: cornerWidth + 'px',\n            ['--radix-scroll-area-corner-height' as any]: cornerHeight + 'px',\n            ...props.style,\n          }}\n        />\n      </ScrollAreaProvider>\n    );\n  }\n);\n\nScrollArea.displayName = SCROLL_AREA_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ScrollAreaViewport\n * -----------------------------------------------------------------------------------------------*/\n\nconst VIEWPORT_NAME = 'ScrollAreaViewport';\n\ntype ScrollAreaViewportElement = React.ElementRef<typeof Primitive.div>;\ninterface ScrollAreaViewportProps extends PrimitiveDivProps {\n  nonce?: string;\n}\n\nconst ScrollAreaViewport = React.forwardRef<ScrollAreaViewportElement, ScrollAreaViewportProps>(\n  (props: ScopedProps<ScrollAreaViewportProps>, forwardedRef) => {\n    const { __scopeScrollArea, children, asChild, nonce, ...viewportProps } = props;\n    const context = useScrollAreaContext(VIEWPORT_NAME, __scopeScrollArea);\n    const ref = React.useRef<ScrollAreaViewportElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref, context.onViewportChange);\n    return (\n      <>\n        <style\n          dangerouslySetInnerHTML={{\n            __html: `\n[data-radix-scroll-area-viewport] {\n  scrollbar-width: none;\n  -ms-overflow-style: none;\n  -webkit-overflow-scrolling: touch;\n}\n[data-radix-scroll-area-viewport]::-webkit-scrollbar {\n  display: none;\n}\n:where([data-radix-scroll-area-viewport]) {\n  display: flex;\n  flex-direction: column;\n  align-items: stretch;\n}\n:where([data-radix-scroll-area-content]) {\n  flex-grow: 1;\n}\n`,\n          }}\n          nonce={nonce}\n        />\n        <Primitive.div\n          data-radix-scroll-area-viewport=\"\"\n          {...viewportProps}\n          asChild={asChild}\n          ref={composedRefs}\n          style={{\n            /**\n             * We don't support `visible` because the intention is to have at least one scrollbar\n             * if this component is used and `visible` will behave like `auto` in that case\n             * https://developer.mozilla.org/en-US/docs/Web/CSS/overflow#description\n             *\n             * We don't handle `auto` because the intention is for the native implementation\n             * to be hidden if using this component. We just want to ensure the node is scrollable\n             * so could have used either `scroll` or `auto` here. We picked `scroll` to prevent\n             * the browser from having to work out whether to render native scrollbars or not,\n             * we tell it to with the intention of hiding them in CSS.\n             */\n            overflowX: context.scrollbarXEnabled ? 'scroll' : 'hidden',\n            overflowY: context.scrollbarYEnabled ? 'scroll' : 'hidden',\n            ...props.style,\n          }}\n        >\n          {getSubtree({ asChild, children }, (children) => (\n            <div\n              data-radix-scroll-area-content=\"\"\n              ref={context.onContentChange}\n              /**\n               * When horizontal scrollbar is visible: this element should be at least\n               * as wide as its children for size calculations to work correctly.\n               *\n               * When horizontal scrollbar is NOT visible: this element's width should\n               * be constrained by the parent container to enable `text-overflow: ellipsis`\n               */\n              style={{ minWidth: context.scrollbarXEnabled ? 'fit-content' : undefined }}\n            >\n              {children}\n            </div>\n          ))}\n        </Primitive.div>\n      </>\n    );\n  }\n);\n\nScrollAreaViewport.displayName = VIEWPORT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ScrollAreaScrollbar\n * -----------------------------------------------------------------------------------------------*/\n\nconst SCROLLBAR_NAME = 'ScrollAreaScrollbar';\n\ntype ScrollAreaScrollbarElement = ScrollAreaScrollbarVisibleElement;\ninterface ScrollAreaScrollbarProps extends ScrollAreaScrollbarVisibleProps {\n  forceMount?: true;\n}\n\nconst ScrollAreaScrollbar = React.forwardRef<ScrollAreaScrollbarElement, ScrollAreaScrollbarProps>(\n  (props: ScopedProps<ScrollAreaScrollbarProps>, forwardedRef) => {\n    const { forceMount, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const { onScrollbarXEnabledChange, onScrollbarYEnabledChange } = context;\n    const isHorizontal = props.orientation === 'horizontal';\n\n    React.useEffect(() => {\n      isHorizontal ? onScrollbarXEnabledChange(true) : onScrollbarYEnabledChange(true);\n      return () => {\n        isHorizontal ? onScrollbarXEnabledChange(false) : onScrollbarYEnabledChange(false);\n      };\n    }, [isHorizontal, onScrollbarXEnabledChange, onScrollbarYEnabledChange]);\n\n    return context.type === 'hover' ? (\n      <ScrollAreaScrollbarHover {...scrollbarProps} ref={forwardedRef} forceMount={forceMount} />\n    ) : context.type === 'scroll' ? (\n      <ScrollAreaScrollbarScroll {...scrollbarProps} ref={forwardedRef} forceMount={forceMount} />\n    ) : context.type === 'auto' ? (\n      <ScrollAreaScrollbarAuto {...scrollbarProps} ref={forwardedRef} forceMount={forceMount} />\n    ) : context.type === 'always' ? (\n      <ScrollAreaScrollbarVisible {...scrollbarProps} ref={forwardedRef} />\n    ) : null;\n  }\n);\n\nScrollAreaScrollbar.displayName = SCROLLBAR_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype ScrollAreaScrollbarHoverElement = ScrollAreaScrollbarAutoElement;\ninterface ScrollAreaScrollbarHoverProps extends ScrollAreaScrollbarAutoProps {\n  forceMount?: true;\n}\n\nconst ScrollAreaScrollbarHover = React.forwardRef<\n  ScrollAreaScrollbarHoverElement,\n  ScrollAreaScrollbarHoverProps\n>((props: ScopedProps<ScrollAreaScrollbarHoverProps>, forwardedRef) => {\n  const { forceMount, ...scrollbarProps } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const [visible, setVisible] = React.useState(false);\n\n  React.useEffect(() => {\n    const scrollArea = context.scrollArea;\n    let hideTimer = 0;\n    if (scrollArea) {\n      const handlePointerEnter = () => {\n        window.clearTimeout(hideTimer);\n        setVisible(true);\n      };\n      const handlePointerLeave = () => {\n        hideTimer = window.setTimeout(() => setVisible(false), context.scrollHideDelay);\n      };\n      scrollArea.addEventListener('pointerenter', handlePointerEnter);\n      scrollArea.addEventListener('pointerleave', handlePointerLeave);\n      return () => {\n        window.clearTimeout(hideTimer);\n        scrollArea.removeEventListener('pointerenter', handlePointerEnter);\n        scrollArea.removeEventListener('pointerleave', handlePointerLeave);\n      };\n    }\n  }, [context.scrollArea, context.scrollHideDelay]);\n\n  return (\n    <Presence present={forceMount || visible}>\n      <ScrollAreaScrollbarAuto\n        data-state={visible ? 'visible' : 'hidden'}\n        {...scrollbarProps}\n        ref={forwardedRef}\n      />\n    </Presence>\n  );\n});\n\ntype ScrollAreaScrollbarScrollElement = ScrollAreaScrollbarVisibleElement;\ninterface ScrollAreaScrollbarScrollProps extends ScrollAreaScrollbarVisibleProps {\n  forceMount?: true;\n}\n\nconst ScrollAreaScrollbarScroll = React.forwardRef<\n  ScrollAreaScrollbarScrollElement,\n  ScrollAreaScrollbarScrollProps\n>((props: ScopedProps<ScrollAreaScrollbarScrollProps>, forwardedRef) => {\n  const { forceMount, ...scrollbarProps } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const isHorizontal = props.orientation === 'horizontal';\n  const debounceScrollEnd = useDebounceCallback(() => send('SCROLL_END'), 100);\n  const [state, send] = useStateMachine('hidden', {\n    hidden: {\n      SCROLL: 'scrolling',\n    },\n    scrolling: {\n      SCROLL_END: 'idle',\n      POINTER_ENTER: 'interacting',\n    },\n    interacting: {\n      SCROLL: 'interacting',\n      POINTER_LEAVE: 'idle',\n    },\n    idle: {\n      HIDE: 'hidden',\n      SCROLL: 'scrolling',\n      POINTER_ENTER: 'interacting',\n    },\n  });\n\n  React.useEffect(() => {\n    if (state === 'idle') {\n      const hideTimer = window.setTimeout(() => send('HIDE'), context.scrollHideDelay);\n      return () => window.clearTimeout(hideTimer);\n    }\n  }, [state, context.scrollHideDelay, send]);\n\n  React.useEffect(() => {\n    const viewport = context.viewport;\n    const scrollDirection = isHorizontal ? 'scrollLeft' : 'scrollTop';\n\n    if (viewport) {\n      let prevScrollPos = viewport[scrollDirection];\n      const handleScroll = () => {\n        const scrollPos = viewport[scrollDirection];\n        const hasScrollInDirectionChanged = prevScrollPos !== scrollPos;\n        if (hasScrollInDirectionChanged) {\n          send('SCROLL');\n          debounceScrollEnd();\n        }\n        prevScrollPos = scrollPos;\n      };\n      viewport.addEventListener('scroll', handleScroll);\n      return () => viewport.removeEventListener('scroll', handleScroll);\n    }\n  }, [context.viewport, isHorizontal, send, debounceScrollEnd]);\n\n  return (\n    <Presence present={forceMount || state !== 'hidden'}>\n      <ScrollAreaScrollbarVisible\n        data-state={state === 'hidden' ? 'hidden' : 'visible'}\n        {...scrollbarProps}\n        ref={forwardedRef}\n        onPointerEnter={composeEventHandlers(props.onPointerEnter, () => send('POINTER_ENTER'))}\n        onPointerLeave={composeEventHandlers(props.onPointerLeave, () => send('POINTER_LEAVE'))}\n      />\n    </Presence>\n  );\n});\n\ntype ScrollAreaScrollbarAutoElement = ScrollAreaScrollbarVisibleElement;\ninterface ScrollAreaScrollbarAutoProps extends ScrollAreaScrollbarVisibleProps {\n  forceMount?: true;\n}\n\nconst ScrollAreaScrollbarAuto = React.forwardRef<\n  ScrollAreaScrollbarAutoElement,\n  ScrollAreaScrollbarAutoProps\n>((props: ScopedProps<ScrollAreaScrollbarAutoProps>, forwardedRef) => {\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const { forceMount, ...scrollbarProps } = props;\n  const [visible, setVisible] = React.useState(false);\n  const isHorizontal = props.orientation === 'horizontal';\n  const handleResize = useDebounceCallback(() => {\n    if (context.viewport) {\n      const isOverflowX = context.viewport.offsetWidth < context.viewport.scrollWidth;\n      const isOverflowY = context.viewport.offsetHeight < context.viewport.scrollHeight;\n      setVisible(isHorizontal ? isOverflowX : isOverflowY);\n    }\n  }, 10);\n\n  useResizeObserver(context.viewport, handleResize);\n  useResizeObserver(context.content, handleResize);\n\n  return (\n    <Presence present={forceMount || visible}>\n      <ScrollAreaScrollbarVisible\n        data-state={visible ? 'visible' : 'hidden'}\n        {...scrollbarProps}\n        ref={forwardedRef}\n      />\n    </Presence>\n  );\n});\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype ScrollAreaScrollbarVisibleElement = ScrollAreaScrollbarAxisElement;\ninterface ScrollAreaScrollbarVisibleProps\n  extends Omit<ScrollAreaScrollbarAxisProps, keyof ScrollAreaScrollbarAxisPrivateProps> {\n  orientation?: 'horizontal' | 'vertical';\n}\n\nconst ScrollAreaScrollbarVisible = React.forwardRef<\n  ScrollAreaScrollbarVisibleElement,\n  ScrollAreaScrollbarVisibleProps\n>((props: ScopedProps<ScrollAreaScrollbarVisibleProps>, forwardedRef) => {\n  const { orientation = 'vertical', ...scrollbarProps } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const thumbRef = React.useRef<ScrollAreaThumbElement | null>(null);\n  const pointerOffsetRef = React.useRef(0);\n  const [sizes, setSizes] = React.useState<Sizes>({\n    content: 0,\n    viewport: 0,\n    scrollbar: { size: 0, paddingStart: 0, paddingEnd: 0 },\n  });\n  const thumbRatio = getThumbRatio(sizes.viewport, sizes.content);\n\n  type UncommonProps = 'onThumbPositionChange' | 'onDragScroll' | 'onWheelScroll';\n  const commonProps: Omit<ScrollAreaScrollbarAxisPrivateProps, UncommonProps> = {\n    ...scrollbarProps,\n    sizes,\n    onSizesChange: setSizes,\n    hasThumb: Boolean(thumbRatio > 0 && thumbRatio < 1),\n    onThumbChange: (thumb) => (thumbRef.current = thumb),\n    onThumbPointerUp: () => (pointerOffsetRef.current = 0),\n    onThumbPointerDown: (pointerPos) => (pointerOffsetRef.current = pointerPos),\n  };\n\n  function getScrollPosition(pointerPos: number, dir?: Direction) {\n    return getScrollPositionFromPointer(pointerPos, pointerOffsetRef.current, sizes, dir);\n  }\n\n  if (orientation === 'horizontal') {\n    return (\n      <ScrollAreaScrollbarX\n        {...commonProps}\n        ref={forwardedRef}\n        onThumbPositionChange={() => {\n          if (context.viewport && thumbRef.current) {\n            const scrollPos = context.viewport.scrollLeft;\n            const offset = getThumbOffsetFromScroll(scrollPos, sizes, context.dir);\n            thumbRef.current.style.transform = `translate3d(${offset}px, 0, 0)`;\n          }\n        }}\n        onWheelScroll={(scrollPos) => {\n          if (context.viewport) context.viewport.scrollLeft = scrollPos;\n        }}\n        onDragScroll={(pointerPos) => {\n          if (context.viewport) {\n            context.viewport.scrollLeft = getScrollPosition(pointerPos, context.dir);\n          }\n        }}\n      />\n    );\n  }\n\n  if (orientation === 'vertical') {\n    return (\n      <ScrollAreaScrollbarY\n        {...commonProps}\n        ref={forwardedRef}\n        onThumbPositionChange={() => {\n          if (context.viewport && thumbRef.current) {\n            const scrollPos = context.viewport.scrollTop;\n            const offset = getThumbOffsetFromScroll(scrollPos, sizes);\n            thumbRef.current.style.transform = `translate3d(0, ${offset}px, 0)`;\n          }\n        }}\n        onWheelScroll={(scrollPos) => {\n          if (context.viewport) context.viewport.scrollTop = scrollPos;\n        }}\n        onDragScroll={(pointerPos) => {\n          if (context.viewport) context.viewport.scrollTop = getScrollPosition(pointerPos);\n        }}\n      />\n    );\n  }\n\n  return null;\n});\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype ScrollAreaScrollbarAxisPrivateProps = {\n  hasThumb: boolean;\n  sizes: Sizes;\n  onSizesChange(sizes: Sizes): void;\n  onThumbChange(thumb: ScrollAreaThumbElement | null): void;\n  onThumbPointerDown(pointerPos: number): void;\n  onThumbPointerUp(): void;\n  onThumbPositionChange(): void;\n  onWheelScroll(scrollPos: number): void;\n  onDragScroll(pointerPos: number): void;\n};\n\ntype ScrollAreaScrollbarAxisElement = ScrollAreaScrollbarImplElement;\ninterface ScrollAreaScrollbarAxisProps\n  extends Omit<ScrollAreaScrollbarImplProps, keyof ScrollAreaScrollbarImplPrivateProps>,\n    ScrollAreaScrollbarAxisPrivateProps {}\n\nconst ScrollAreaScrollbarX = React.forwardRef<\n  ScrollAreaScrollbarAxisElement,\n  ScrollAreaScrollbarAxisProps\n>((props: ScopedProps<ScrollAreaScrollbarAxisProps>, forwardedRef) => {\n  const { sizes, onSizesChange, ...scrollbarProps } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const [computedStyle, setComputedStyle] = React.useState<CSSStyleDeclaration>();\n  const ref = React.useRef<ScrollAreaScrollbarAxisElement>(null);\n  const composeRefs = useComposedRefs(forwardedRef, ref, context.onScrollbarXChange);\n\n  React.useEffect(() => {\n    if (ref.current) setComputedStyle(getComputedStyle(ref.current));\n  }, [ref]);\n\n  return (\n    <ScrollAreaScrollbarImpl\n      data-orientation=\"horizontal\"\n      {...scrollbarProps}\n      ref={composeRefs}\n      sizes={sizes}\n      style={{\n        bottom: 0,\n        left: context.dir === 'rtl' ? 'var(--radix-scroll-area-corner-width)' : 0,\n        right: context.dir === 'ltr' ? 'var(--radix-scroll-area-corner-width)' : 0,\n        ['--radix-scroll-area-thumb-width' as any]: getThumbSize(sizes) + 'px',\n        ...props.style,\n      }}\n      onThumbPointerDown={(pointerPos) => props.onThumbPointerDown(pointerPos.x)}\n      onDragScroll={(pointerPos) => props.onDragScroll(pointerPos.x)}\n      onWheelScroll={(event, maxScrollPos) => {\n        if (context.viewport) {\n          const scrollPos = context.viewport.scrollLeft + event.deltaX;\n          props.onWheelScroll(scrollPos);\n          // prevent window scroll when wheeling on scrollbar\n          if (isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos)) {\n            event.preventDefault();\n          }\n        }\n      }}\n      onResize={() => {\n        if (ref.current && context.viewport && computedStyle) {\n          onSizesChange({\n            content: context.viewport.scrollWidth,\n            viewport: context.viewport.offsetWidth,\n            scrollbar: {\n              size: ref.current.clientWidth,\n              paddingStart: toInt(computedStyle.paddingLeft),\n              paddingEnd: toInt(computedStyle.paddingRight),\n            },\n          });\n        }\n      }}\n    />\n  );\n});\n\nconst ScrollAreaScrollbarY = React.forwardRef<\n  ScrollAreaScrollbarAxisElement,\n  ScrollAreaScrollbarAxisProps\n>((props: ScopedProps<ScrollAreaScrollbarAxisProps>, forwardedRef) => {\n  const { sizes, onSizesChange, ...scrollbarProps } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const [computedStyle, setComputedStyle] = React.useState<CSSStyleDeclaration>();\n  const ref = React.useRef<ScrollAreaScrollbarAxisElement>(null);\n  const composeRefs = useComposedRefs(forwardedRef, ref, context.onScrollbarYChange);\n\n  React.useEffect(() => {\n    if (ref.current) setComputedStyle(getComputedStyle(ref.current));\n  }, [ref]);\n\n  return (\n    <ScrollAreaScrollbarImpl\n      data-orientation=\"vertical\"\n      {...scrollbarProps}\n      ref={composeRefs}\n      sizes={sizes}\n      style={{\n        top: 0,\n        right: context.dir === 'ltr' ? 0 : undefined,\n        left: context.dir === 'rtl' ? 0 : undefined,\n        bottom: 'var(--radix-scroll-area-corner-height)',\n        ['--radix-scroll-area-thumb-height' as any]: getThumbSize(sizes) + 'px',\n        ...props.style,\n      }}\n      onThumbPointerDown={(pointerPos) => props.onThumbPointerDown(pointerPos.y)}\n      onDragScroll={(pointerPos) => props.onDragScroll(pointerPos.y)}\n      onWheelScroll={(event, maxScrollPos) => {\n        if (context.viewport) {\n          const scrollPos = context.viewport.scrollTop + event.deltaY;\n          props.onWheelScroll(scrollPos);\n          // prevent window scroll when wheeling on scrollbar\n          if (isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos)) {\n            event.preventDefault();\n          }\n        }\n      }}\n      onResize={() => {\n        if (ref.current && context.viewport && computedStyle) {\n          onSizesChange({\n            content: context.viewport.scrollHeight,\n            viewport: context.viewport.offsetHeight,\n            scrollbar: {\n              size: ref.current.clientHeight,\n              paddingStart: toInt(computedStyle.paddingTop),\n              paddingEnd: toInt(computedStyle.paddingBottom),\n            },\n          });\n        }\n      }}\n    />\n  );\n});\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype ScrollbarContext = {\n  hasThumb: boolean;\n  scrollbar: ScrollAreaScrollbarElement | null;\n  onThumbChange(thumb: ScrollAreaThumbElement | null): void;\n  onThumbPointerUp(): void;\n  onThumbPointerDown(pointerPos: { x: number; y: number }): void;\n  onThumbPositionChange(): void;\n};\n\nconst [ScrollbarProvider, useScrollbarContext] =\n  createScrollAreaContext<ScrollbarContext>(SCROLLBAR_NAME);\n\ntype ScrollAreaScrollbarImplElement = React.ElementRef<typeof Primitive.div>;\ntype ScrollAreaScrollbarImplPrivateProps = {\n  sizes: Sizes;\n  hasThumb: boolean;\n  onThumbChange: ScrollbarContext['onThumbChange'];\n  onThumbPointerUp: ScrollbarContext['onThumbPointerUp'];\n  onThumbPointerDown: ScrollbarContext['onThumbPointerDown'];\n  onThumbPositionChange: ScrollbarContext['onThumbPositionChange'];\n  onWheelScroll(event: WheelEvent, maxScrollPos: number): void;\n  onDragScroll(pointerPos: { x: number; y: number }): void;\n  onResize(): void;\n};\ninterface ScrollAreaScrollbarImplProps\n  extends Omit<PrimitiveDivProps, keyof ScrollAreaScrollbarImplPrivateProps>,\n    ScrollAreaScrollbarImplPrivateProps {}\n\nconst ScrollAreaScrollbarImpl = React.forwardRef<\n  ScrollAreaScrollbarImplElement,\n  ScrollAreaScrollbarImplProps\n>((props: ScopedProps<ScrollAreaScrollbarImplProps>, forwardedRef) => {\n  const {\n    __scopeScrollArea,\n    sizes,\n    hasThumb,\n    onThumbChange,\n    onThumbPointerUp,\n    onThumbPointerDown,\n    onThumbPositionChange,\n    onDragScroll,\n    onWheelScroll,\n    onResize,\n    ...scrollbarProps\n  } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, __scopeScrollArea);\n  const [scrollbar, setScrollbar] = React.useState<ScrollAreaScrollbarElement | null>(null);\n  const composeRefs = useComposedRefs(forwardedRef, (node) => setScrollbar(node));\n  const rectRef = React.useRef<DOMRect | null>(null);\n  const prevWebkitUserSelectRef = React.useRef<string>('');\n  const viewport = context.viewport;\n  const maxScrollPos = sizes.content - sizes.viewport;\n  const handleWheelScroll = useCallbackRef(onWheelScroll);\n  const handleThumbPositionChange = useCallbackRef(onThumbPositionChange);\n  const handleResize = useDebounceCallback(onResize, 10);\n\n  function handleDragScroll(event: React.PointerEvent<HTMLElement>) {\n    if (rectRef.current) {\n      const x = event.clientX - rectRef.current.left;\n      const y = event.clientY - rectRef.current.top;\n      onDragScroll({ x, y });\n    }\n  }\n\n  /**\n   * We bind wheel event imperatively so we can switch off passive\n   * mode for document wheel event to allow it to be prevented\n   */\n  React.useEffect(() => {\n    const handleWheel = (event: WheelEvent) => {\n      const element = event.target as HTMLElement;\n      const isScrollbarWheel = scrollbar?.contains(element);\n      if (isScrollbarWheel) handleWheelScroll(event, maxScrollPos);\n    };\n    document.addEventListener('wheel', handleWheel, { passive: false });\n    return () => document.removeEventListener('wheel', handleWheel, { passive: false } as any);\n  }, [viewport, scrollbar, maxScrollPos, handleWheelScroll]);\n\n  /**\n   * Update thumb position on sizes change\n   */\n  React.useEffect(handleThumbPositionChange, [sizes, handleThumbPositionChange]);\n\n  useResizeObserver(scrollbar, handleResize);\n  useResizeObserver(context.content, handleResize);\n\n  return (\n    <ScrollbarProvider\n      scope={__scopeScrollArea}\n      scrollbar={scrollbar}\n      hasThumb={hasThumb}\n      onThumbChange={useCallbackRef(onThumbChange)}\n      onThumbPointerUp={useCallbackRef(onThumbPointerUp)}\n      onThumbPositionChange={handleThumbPositionChange}\n      onThumbPointerDown={useCallbackRef(onThumbPointerDown)}\n    >\n      <Primitive.div\n        {...scrollbarProps}\n        ref={composeRefs}\n        style={{ position: 'absolute', ...scrollbarProps.style }}\n        onPointerDown={composeEventHandlers(props.onPointerDown, (event) => {\n          const mainPointer = 0;\n          if (event.button === mainPointer) {\n            const element = event.target as HTMLElement;\n            element.setPointerCapture(event.pointerId);\n            rectRef.current = scrollbar!.getBoundingClientRect();\n            // pointer capture doesn't prevent text selection in Safari\n            // so we remove text selection manually when scrolling\n            prevWebkitUserSelectRef.current = document.body.style.webkitUserSelect;\n            document.body.style.webkitUserSelect = 'none';\n            if (context.viewport) context.viewport.style.scrollBehavior = 'auto';\n            handleDragScroll(event);\n          }\n        })}\n        onPointerMove={composeEventHandlers(props.onPointerMove, handleDragScroll)}\n        onPointerUp={composeEventHandlers(props.onPointerUp, (event) => {\n          const element = event.target as HTMLElement;\n          if (element.hasPointerCapture(event.pointerId)) {\n            element.releasePointerCapture(event.pointerId);\n          }\n          document.body.style.webkitUserSelect = prevWebkitUserSelectRef.current;\n          if (context.viewport) context.viewport.style.scrollBehavior = '';\n          rectRef.current = null;\n        })}\n      />\n    </ScrollbarProvider>\n  );\n});\n\n/* -------------------------------------------------------------------------------------------------\n * ScrollAreaThumb\n * -----------------------------------------------------------------------------------------------*/\n\nconst THUMB_NAME = 'ScrollAreaThumb';\n\ntype ScrollAreaThumbElement = ScrollAreaThumbImplElement;\ninterface ScrollAreaThumbProps extends ScrollAreaThumbImplProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst ScrollAreaThumb = React.forwardRef<ScrollAreaThumbElement, ScrollAreaThumbProps>(\n  (props: ScopedProps<ScrollAreaThumbProps>, forwardedRef) => {\n    const { forceMount, ...thumbProps } = props;\n    const scrollbarContext = useScrollbarContext(THUMB_NAME, props.__scopeScrollArea);\n    return (\n      <Presence present={forceMount || scrollbarContext.hasThumb}>\n        <ScrollAreaThumbImpl ref={forwardedRef} {...thumbProps} />\n      </Presence>\n    );\n  }\n);\n\ntype ScrollAreaThumbImplElement = React.ElementRef<typeof Primitive.div>;\ninterface ScrollAreaThumbImplProps extends PrimitiveDivProps {}\n\nconst ScrollAreaThumbImpl = React.forwardRef<ScrollAreaThumbImplElement, ScrollAreaThumbImplProps>(\n  (props: ScopedProps<ScrollAreaThumbImplProps>, forwardedRef) => {\n    const { __scopeScrollArea, style, ...thumbProps } = props;\n    const scrollAreaContext = useScrollAreaContext(THUMB_NAME, __scopeScrollArea);\n    const scrollbarContext = useScrollbarContext(THUMB_NAME, __scopeScrollArea);\n    const { onThumbPositionChange } = scrollbarContext;\n    const composedRef = useComposedRefs(forwardedRef, (node) =>\n      scrollbarContext.onThumbChange(node)\n    );\n    const removeUnlinkedScrollListenerRef = React.useRef<() => void>();\n    const debounceScrollEnd = useDebounceCallback(() => {\n      if (removeUnlinkedScrollListenerRef.current) {\n        removeUnlinkedScrollListenerRef.current();\n        removeUnlinkedScrollListenerRef.current = undefined;\n      }\n    }, 100);\n\n    React.useEffect(() => {\n      const viewport = scrollAreaContext.viewport;\n      if (viewport) {\n        /**\n         * We only bind to native scroll event so we know when scroll starts and ends.\n         * When scroll starts we start a requestAnimationFrame loop that checks for\n         * changes to scroll position. That rAF loop triggers our thumb position change\n         * when relevant to avoid scroll-linked effects. We cancel the loop when scroll ends.\n         * https://developer.mozilla.org/en-US/docs/Mozilla/Performance/Scroll-linked_effects\n         */\n        const handleScroll = () => {\n          debounceScrollEnd();\n          if (!removeUnlinkedScrollListenerRef.current) {\n            const listener = addUnlinkedScrollListener(viewport, onThumbPositionChange);\n            removeUnlinkedScrollListenerRef.current = listener;\n            onThumbPositionChange();\n          }\n        };\n        onThumbPositionChange();\n        viewport.addEventListener('scroll', handleScroll);\n        return () => viewport.removeEventListener('scroll', handleScroll);\n      }\n    }, [scrollAreaContext.viewport, debounceScrollEnd, onThumbPositionChange]);\n\n    return (\n      <Primitive.div\n        data-state={scrollbarContext.hasThumb ? 'visible' : 'hidden'}\n        {...thumbProps}\n        ref={composedRef}\n        style={{\n          width: 'var(--radix-scroll-area-thumb-width)',\n          height: 'var(--radix-scroll-area-thumb-height)',\n          ...style,\n        }}\n        onPointerDownCapture={composeEventHandlers(props.onPointerDownCapture, (event) => {\n          const thumb = event.target as HTMLElement;\n          const thumbRect = thumb.getBoundingClientRect();\n          const x = event.clientX - thumbRect.left;\n          const y = event.clientY - thumbRect.top;\n          scrollbarContext.onThumbPointerDown({ x, y });\n        })}\n        onPointerUp={composeEventHandlers(props.onPointerUp, scrollbarContext.onThumbPointerUp)}\n      />\n    );\n  }\n);\n\nScrollAreaThumb.displayName = THUMB_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ScrollAreaCorner\n * -----------------------------------------------------------------------------------------------*/\n\nconst CORNER_NAME = 'ScrollAreaCorner';\n\ntype ScrollAreaCornerElement = ScrollAreaCornerImplElement;\ninterface ScrollAreaCornerProps extends ScrollAreaCornerImplProps {}\n\nconst ScrollAreaCorner = React.forwardRef<ScrollAreaCornerElement, ScrollAreaCornerProps>(\n  (props: ScopedProps<ScrollAreaCornerProps>, forwardedRef) => {\n    const context = useScrollAreaContext(CORNER_NAME, props.__scopeScrollArea);\n    const hasBothScrollbarsVisible = Boolean(context.scrollbarX && context.scrollbarY);\n    const hasCorner = context.type !== 'scroll' && hasBothScrollbarsVisible;\n    return hasCorner ? <ScrollAreaCornerImpl {...props} ref={forwardedRef} /> : null;\n  }\n);\n\nScrollAreaCorner.displayName = CORNER_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype ScrollAreaCornerImplElement = React.ElementRef<typeof Primitive.div>;\ninterface ScrollAreaCornerImplProps extends PrimitiveDivProps {}\n\nconst ScrollAreaCornerImpl = React.forwardRef<\n  ScrollAreaCornerImplElement,\n  ScrollAreaCornerImplProps\n>((props: ScopedProps<ScrollAreaCornerImplProps>, forwardedRef) => {\n  const { __scopeScrollArea, ...cornerProps } = props;\n  const context = useScrollAreaContext(CORNER_NAME, __scopeScrollArea);\n  const [width, setWidth] = React.useState(0);\n  const [height, setHeight] = React.useState(0);\n  const hasSize = Boolean(width && height);\n\n  useResizeObserver(context.scrollbarX, () => {\n    const height = context.scrollbarX?.offsetHeight || 0;\n    context.onCornerHeightChange(height);\n    setHeight(height);\n  });\n\n  useResizeObserver(context.scrollbarY, () => {\n    const width = context.scrollbarY?.offsetWidth || 0;\n    context.onCornerWidthChange(width);\n    setWidth(width);\n  });\n\n  return hasSize ? (\n    <Primitive.div\n      {...cornerProps}\n      ref={forwardedRef}\n      style={{\n        width,\n        height,\n        position: 'absolute',\n        right: context.dir === 'ltr' ? 0 : undefined,\n        left: context.dir === 'rtl' ? 0 : undefined,\n        bottom: 0,\n        ...props.style,\n      }}\n    />\n  ) : null;\n});\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction toInt(value?: string) {\n  return value ? parseInt(value, 10) : 0;\n}\n\nfunction getThumbRatio(viewportSize: number, contentSize: number) {\n  const ratio = viewportSize / contentSize;\n  return isNaN(ratio) ? 0 : ratio;\n}\n\nfunction getThumbSize(sizes: Sizes) {\n  const ratio = getThumbRatio(sizes.viewport, sizes.content);\n  const scrollbarPadding = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd;\n  const thumbSize = (sizes.scrollbar.size - scrollbarPadding) * ratio;\n  // minimum of 18 matches macOS minimum\n  return Math.max(thumbSize, 18);\n}\n\nfunction getScrollPositionFromPointer(\n  pointerPos: number,\n  pointerOffset: number,\n  sizes: Sizes,\n  dir: Direction = 'ltr'\n) {\n  const thumbSizePx = getThumbSize(sizes);\n  const thumbCenter = thumbSizePx / 2;\n  const offset = pointerOffset || thumbCenter;\n  const thumbOffsetFromEnd = thumbSizePx - offset;\n  const minPointerPos = sizes.scrollbar.paddingStart + offset;\n  const maxPointerPos = sizes.scrollbar.size - sizes.scrollbar.paddingEnd - thumbOffsetFromEnd;\n  const maxScrollPos = sizes.content - sizes.viewport;\n  const scrollRange = dir === 'ltr' ? [0, maxScrollPos] : [maxScrollPos * -1, 0];\n  const interpolate = linearScale([minPointerPos, maxPointerPos], scrollRange as [number, number]);\n  return interpolate(pointerPos);\n}\n\nfunction getThumbOffsetFromScroll(scrollPos: number, sizes: Sizes, dir: Direction = 'ltr') {\n  const thumbSizePx = getThumbSize(sizes);\n  const scrollbarPadding = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd;\n  const scrollbar = sizes.scrollbar.size - scrollbarPadding;\n  const maxScrollPos = sizes.content - sizes.viewport;\n  const maxThumbPos = scrollbar - thumbSizePx;\n  const scrollClampRange = dir === 'ltr' ? [0, maxScrollPos] : [maxScrollPos * -1, 0];\n  const scrollWithoutMomentum = clamp(scrollPos, scrollClampRange as [number, number]);\n  const interpolate = linearScale([0, maxScrollPos], [0, maxThumbPos]);\n  return interpolate(scrollWithoutMomentum);\n}\n\n// https://github.com/tmcw-up-for-adoption/simple-linear-scale/blob/master/index.js\nfunction linearScale(input: readonly [number, number], output: readonly [number, number]) {\n  return (value: number) => {\n    if (input[0] === input[1] || output[0] === output[1]) return output[0];\n    const ratio = (output[1] - output[0]) / (input[1] - input[0]);\n    return output[0] + ratio * (value - input[0]);\n  };\n}\n\nfunction isScrollingWithinScrollbarBounds(scrollPos: number, maxScrollPos: number) {\n  return scrollPos > 0 && scrollPos < maxScrollPos;\n}\n\n// Custom scroll handler to avoid scroll-linked effects\n// https://developer.mozilla.org/en-US/docs/Mozilla/Performance/Scroll-linked_effects\nconst addUnlinkedScrollListener = (node: HTMLElement, handler = () => {}) => {\n  let prevPosition = { left: node.scrollLeft, top: node.scrollTop };\n  let rAF = 0;\n  (function loop() {\n    const position = { left: node.scrollLeft, top: node.scrollTop };\n    const isHorizontalScroll = prevPosition.left !== position.left;\n    const isVerticalScroll = prevPosition.top !== position.top;\n    if (isHorizontalScroll || isVerticalScroll) handler();\n    prevPosition = position;\n    rAF = window.requestAnimationFrame(loop);\n  })();\n  return () => window.cancelAnimationFrame(rAF);\n};\n\nfunction useDebounceCallback(callback: () => void, delay: number) {\n  const handleCallback = useCallbackRef(callback);\n  const debounceTimerRef = React.useRef(0);\n  React.useEffect(() => () => window.clearTimeout(debounceTimerRef.current), []);\n  return React.useCallback(() => {\n    window.clearTimeout(debounceTimerRef.current);\n    debounceTimerRef.current = window.setTimeout(handleCallback, delay);\n  }, [handleCallback, delay]);\n}\n\nfunction useResizeObserver(element: HTMLElement | null, onResize: () => void) {\n  const handleResize = useCallbackRef(onResize);\n  useLayoutEffect(() => {\n    let rAF = 0;\n    if (element) {\n      /**\n       * Resize Observer will throw an often benign error that says `ResizeObserver loop\n       * completed with undelivered notifications`. This means that ResizeObserver was not\n       * able to deliver all observations within a single animation frame, so we use\n       * `requestAnimationFrame` to ensure we don't deliver unnecessary observations.\n       * Further reading: https://github.com/WICG/resize-observer/issues/38\n       */\n      const resizeObserver = new ResizeObserver(() => {\n        cancelAnimationFrame(rAF);\n        rAF = window.requestAnimationFrame(handleResize);\n      });\n      resizeObserver.observe(element);\n      return () => {\n        window.cancelAnimationFrame(rAF);\n        resizeObserver.unobserve(element);\n      };\n    }\n  }, [element, handleResize]);\n}\n\n/**\n * This is a helper function that is used when a component supports `asChild`\n * using the `Slot` component but its implementation contains nested DOM elements.\n *\n * Using it ensures if a consumer uses the `asChild` prop, the elements are in\n * correct order in the DOM, adopting the intended consumer `children`.\n */\nfunction getSubtree(\n  options: { asChild: boolean | undefined; children: React.ReactNode },\n  content: React.ReactNode | ((children: React.ReactNode) => React.ReactNode)\n) {\n  const { asChild, children } = options;\n  if (!asChild) return typeof content === 'function' ? content(children) : content;\n\n  const firstChild = React.Children.only(children) as React.ReactElement;\n  return React.cloneElement(firstChild, {\n    children: typeof content === 'function' ? content(firstChild.props.children) : content,\n  });\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = ScrollArea;\nconst Viewport = ScrollAreaViewport;\nconst Scrollbar = ScrollAreaScrollbar;\nconst Thumb = ScrollAreaThumb;\nconst Corner = ScrollAreaCorner;\n\nexport {\n  createScrollAreaScope,\n  //\n  ScrollArea,\n  ScrollAreaViewport,\n  ScrollAreaScrollbar,\n  ScrollAreaThumb,\n  ScrollAreaCorner,\n  //\n  Root,\n  Viewport,\n  Scrollbar,\n  Thumb,\n  Corner,\n};\nexport type {\n  ScrollAreaProps,\n  ScrollAreaViewportProps,\n  ScrollAreaScrollbarProps,\n  ScrollAreaThumbProps,\n  ScrollAreaCornerProps,\n};\n", "import * as React from 'react';\n\ntype Machine<S> = { [k: string]: { [k: string]: S } };\ntype MachineState<T> = keyof T;\ntype MachineEvent<T> = keyof UnionToIntersection<T[keyof T]>;\n\n// 🤯 https://fettblog.eu/typescript-union-to-intersection/\ntype UnionToIntersection<T> = (T extends any ? (x: T) => any : never) extends (x: infer R) => any\n  ? R\n  : never;\n\nexport function useStateMachine<M>(\n  initialState: MachineState<M>,\n  machine: M & Machine<MachineState<M>>\n) {\n  return React.useReducer((state: MachineState<M>, event: MachineEvent<M>): MachineState<M> => {\n    const nextState = (machine[state] as any)[event];\n    return nextState ?? state;\n  }, initialState);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,aAAuB;ACFvB,YAAuB;AD+Gf,yBAAA;ACpGD,SAAS,gBACd,cACA,SACA;AACA,SAAa,iBAAW,CAAC,OAAwB,UAA4C;AAC3F,UAAM,YAAa,QAAQ,KAAK,EAAU,KAAK;AAC/C,WAAO,aAAa;EACtB,GAAG,YAAY;AACjB;ADYA,IAAM,mBAAmB;AAGzB,IAAM,CAAC,yBAAyB,qBAAqB,IAAI,mBAAmB,gBAAgB;AAuB5F,IAAM,CAAC,oBAAoB,oBAAoB,IAC7C,wBAAgD,gBAAgB;AAUlE,IAAM,aAAmB;EACvB,CAAC,OAAqC,iBAAiB;AACrD,UAAM;MACJ;MACA,OAAO;MACP;MACA,kBAAkB;MAClB,GAAG;IACL,IAAI;AACJ,UAAM,CAAC,YAAY,aAAa,IAAU,gBAAmC,IAAI;AACjF,UAAM,CAAC,UAAU,WAAW,IAAU,gBAA2C,IAAI;AACrF,UAAM,CAAC,SAAS,UAAU,IAAU,gBAAgC,IAAI;AACxE,UAAM,CAAC,YAAY,aAAa,IAAU,gBAA4C,IAAI;AAC1F,UAAM,CAAC,YAAY,aAAa,IAAU,gBAA4C,IAAI;AAC1F,UAAM,CAAC,aAAa,cAAc,IAAU,gBAAS,CAAC;AACtD,UAAM,CAAC,cAAc,eAAe,IAAU,gBAAS,CAAC;AACxD,UAAM,CAAC,mBAAmB,oBAAoB,IAAU,gBAAS,KAAK;AACtE,UAAM,CAAC,mBAAmB,oBAAoB,IAAU,gBAAS,KAAK;AACtE,UAAM,eAAe,gBAAgB,cAAc,CAAC,SAAS,cAAc,IAAI,CAAC;AAChF,UAAM,YAAY,aAAa,GAAG;AAElC,eACE;MAAC;MAAA;QACC,OAAO;QACP;QACA,KAAK;QACL;QACA;QACA;QACA,kBAAkB;QAClB;QACA,iBAAiB;QACjB;QACA,oBAAoB;QACpB;QACA,2BAA2B;QAC3B;QACA,oBAAoB;QACpB;QACA,2BAA2B;QAC3B,qBAAqB;QACrB,sBAAsB;QAEtB,cAAA;UAAC,UAAU;UAAV;YACC,KAAK;YACJ,GAAG;YACJ,KAAK;YACL,OAAO;cACL,UAAU;;cAEV,CAAC,kCAAyC,GAAG,cAAc;cAC3D,CAAC,mCAA0C,GAAG,eAAe;cAC7D,GAAG,MAAM;YACX;UAAA;QACF;MAAA;IACF;EAEJ;AACF;AAEA,WAAW,cAAc;AAMzB,IAAM,gBAAgB;AAOtB,IAAM,qBAA2B;EAC/B,CAAC,OAA6C,iBAAiB;AAC7D,UAAM,EAAE,mBAAmB,UAAU,SAAS,OAAO,GAAG,cAAc,IAAI;AAC1E,UAAM,UAAU,qBAAqB,eAAe,iBAAiB;AACrE,UAAM,MAAY,cAAkC,IAAI;AACxD,UAAM,eAAe,gBAAgB,cAAc,KAAK,QAAQ,gBAAgB;AAChF,eACE,yBAAA,6BAAA,EACE,UAAA;UAAA;QAAC;QAAA;UACC,yBAAyB;YACvB,QAAQ;;;;;;;;;;;;;;;;;;UAkBV;UACA;QAAA;MACF;UACA;QAAC,UAAU;QAAV;UACC,mCAAgC;UAC/B,GAAG;UACJ;UACA,KAAK;UACL,OAAO;;;;;;;;;;;;YAYL,WAAW,QAAQ,oBAAoB,WAAW;YAClD,WAAW,QAAQ,oBAAoB,WAAW;YAClD,GAAG,MAAM;UACX;UAEC,UAAA,WAAW,EAAE,SAAS,SAAS,GAAG,CAACA,kBAClC;YAAC;YAAA;cACC,kCAA+B;cAC/B,KAAK,QAAQ;cAQb,OAAO,EAAE,UAAU,QAAQ,oBAAoB,gBAAgB,OAAU;cAExE,UAAAA;YAAA;UACH,CACD;QAAA;MACH;IAAA,EAAA,CACF;EAEJ;AACF;AAEA,mBAAmB,cAAc;AAMjC,IAAM,iBAAiB;AAOvB,IAAM,sBAA4B;EAChC,CAAC,OAA8C,iBAAiB;AAC9D,UAAM,EAAE,YAAY,GAAG,eAAe,IAAI;AAC1C,UAAM,UAAU,qBAAqB,gBAAgB,MAAM,iBAAiB;AAC5E,UAAM,EAAE,2BAA2B,0BAA0B,IAAI;AACjE,UAAM,eAAe,MAAM,gBAAgB;AAErC,IAAA,iBAAU,MAAM;AACpB,qBAAe,0BAA0B,IAAI,IAAI,0BAA0B,IAAI;AAC/E,aAAO,MAAM;AACX,uBAAe,0BAA0B,KAAK,IAAI,0BAA0B,KAAK;MACnF;IACF,GAAG,CAAC,cAAc,2BAA2B,yBAAyB,CAAC;AAEvE,WAAO,QAAQ,SAAS,cACtB,wBAAC,0BAAA,EAA0B,GAAG,gBAAgB,KAAK,cAAc,WAAA,CAAwB,IACvF,QAAQ,SAAS,eACnB,wBAAC,2BAAA,EAA2B,GAAG,gBAAgB,KAAK,cAAc,WAAA,CAAwB,IACxF,QAAQ,SAAS,aACnB,wBAAC,yBAAA,EAAyB,GAAG,gBAAgB,KAAK,cAAc,WAAA,CAAwB,IACtF,QAAQ,SAAS,eACnB,wBAAC,4BAAA,EAA4B,GAAG,gBAAgB,KAAK,aAAA,CAAc,IACjE;EACN;AACF;AAEA,oBAAoB,cAAc;AASlC,IAAM,2BAAiC,kBAGrC,CAAC,OAAmD,iBAAiB;AACrE,QAAM,EAAE,YAAY,GAAG,eAAe,IAAI;AAC1C,QAAM,UAAU,qBAAqB,gBAAgB,MAAM,iBAAiB;AAC5E,QAAM,CAAC,SAAS,UAAU,IAAU,gBAAS,KAAK;AAE5C,EAAA,iBAAU,MAAM;AACpB,UAAM,aAAa,QAAQ;AAC3B,QAAI,YAAY;AAChB,QAAI,YAAY;AACd,YAAM,qBAAqB,MAAM;AAC/B,eAAO,aAAa,SAAS;AAC7B,mBAAW,IAAI;MACjB;AACA,YAAM,qBAAqB,MAAM;AAC/B,oBAAY,OAAO,WAAW,MAAM,WAAW,KAAK,GAAG,QAAQ,eAAe;MAChF;AACA,iBAAW,iBAAiB,gBAAgB,kBAAkB;AAC9D,iBAAW,iBAAiB,gBAAgB,kBAAkB;AAC9D,aAAO,MAAM;AACX,eAAO,aAAa,SAAS;AAC7B,mBAAW,oBAAoB,gBAAgB,kBAAkB;AACjE,mBAAW,oBAAoB,gBAAgB,kBAAkB;MACnE;IACF;EACF,GAAG,CAAC,QAAQ,YAAY,QAAQ,eAAe,CAAC;AAEhD,aACE,wBAAC,UAAA,EAAS,SAAS,cAAc,SAC/B,cAAA;IAAC;IAAA;MACC,cAAY,UAAU,YAAY;MACjC,GAAG;MACJ,KAAK;IAAA;EACP,EAAA,CACF;AAEJ,CAAC;AAOD,IAAM,4BAAkC,kBAGtC,CAAC,OAAoD,iBAAiB;AACtE,QAAM,EAAE,YAAY,GAAG,eAAe,IAAI;AAC1C,QAAM,UAAU,qBAAqB,gBAAgB,MAAM,iBAAiB;AAC5E,QAAM,eAAe,MAAM,gBAAgB;AAC3C,QAAM,oBAAoB,oBAAoB,MAAM,KAAK,YAAY,GAAG,GAAG;AAC3E,QAAM,CAAC,OAAO,IAAI,IAAI,gBAAgB,UAAU;IAC9C,QAAQ;MACN,QAAQ;IACV;IACA,WAAW;MACT,YAAY;MACZ,eAAe;IACjB;IACA,aAAa;MACX,QAAQ;MACR,eAAe;IACjB;IACA,MAAM;MACJ,MAAM;MACN,QAAQ;MACR,eAAe;IACjB;EACF,CAAC;AAEK,EAAA,iBAAU,MAAM;AACpB,QAAI,UAAU,QAAQ;AACpB,YAAM,YAAY,OAAO,WAAW,MAAM,KAAK,MAAM,GAAG,QAAQ,eAAe;AAC/E,aAAO,MAAM,OAAO,aAAa,SAAS;IAC5C;EACF,GAAG,CAAC,OAAO,QAAQ,iBAAiB,IAAI,CAAC;AAEnC,EAAA,iBAAU,MAAM;AACpB,UAAM,WAAW,QAAQ;AACzB,UAAM,kBAAkB,eAAe,eAAe;AAEtD,QAAI,UAAU;AACZ,UAAI,gBAAgB,SAAS,eAAe;AAC5C,YAAM,eAAe,MAAM;AACzB,cAAM,YAAY,SAAS,eAAe;AAC1C,cAAM,8BAA8B,kBAAkB;AACtD,YAAI,6BAA6B;AAC/B,eAAK,QAAQ;AACb,4BAAkB;QACpB;AACA,wBAAgB;MAClB;AACA,eAAS,iBAAiB,UAAU,YAAY;AAChD,aAAO,MAAM,SAAS,oBAAoB,UAAU,YAAY;IAClE;EACF,GAAG,CAAC,QAAQ,UAAU,cAAc,MAAM,iBAAiB,CAAC;AAE5D,aACE,wBAAC,UAAA,EAAS,SAAS,cAAc,UAAU,UACzC,cAAA;IAAC;IAAA;MACC,cAAY,UAAU,WAAW,WAAW;MAC3C,GAAG;MACJ,KAAK;MACL,gBAAgB,qBAAqB,MAAM,gBAAgB,MAAM,KAAK,eAAe,CAAC;MACtF,gBAAgB,qBAAqB,MAAM,gBAAgB,MAAM,KAAK,eAAe,CAAC;IAAA;EACxF,EAAA,CACF;AAEJ,CAAC;AAOD,IAAM,0BAAgC,kBAGpC,CAAC,OAAkD,iBAAiB;AACpE,QAAM,UAAU,qBAAqB,gBAAgB,MAAM,iBAAiB;AAC5E,QAAM,EAAE,YAAY,GAAG,eAAe,IAAI;AAC1C,QAAM,CAAC,SAAS,UAAU,IAAU,gBAAS,KAAK;AAClD,QAAM,eAAe,MAAM,gBAAgB;AAC3C,QAAM,eAAe,oBAAoB,MAAM;AAC7C,QAAI,QAAQ,UAAU;AACpB,YAAM,cAAc,QAAQ,SAAS,cAAc,QAAQ,SAAS;AACpE,YAAM,cAAc,QAAQ,SAAS,eAAe,QAAQ,SAAS;AACrE,iBAAW,eAAe,cAAc,WAAW;IACrD;EACF,GAAG,EAAE;AAEL,oBAAkB,QAAQ,UAAU,YAAY;AAChD,oBAAkB,QAAQ,SAAS,YAAY;AAE/C,aACE,wBAAC,UAAA,EAAS,SAAS,cAAc,SAC/B,cAAA;IAAC;IAAA;MACC,cAAY,UAAU,YAAY;MACjC,GAAG;MACJ,KAAK;IAAA;EACP,EAAA,CACF;AAEJ,CAAC;AAUD,IAAM,6BAAmC,kBAGvC,CAAC,OAAqD,iBAAiB;AACvE,QAAM,EAAE,cAAc,YAAY,GAAG,eAAe,IAAI;AACxD,QAAM,UAAU,qBAAqB,gBAAgB,MAAM,iBAAiB;AAC5E,QAAM,WAAiB,cAAsC,IAAI;AACjE,QAAM,mBAAyB,cAAO,CAAC;AACvC,QAAM,CAAC,OAAO,QAAQ,IAAU,gBAAgB;IAC9C,SAAS;IACT,UAAU;IACV,WAAW,EAAE,MAAM,GAAG,cAAc,GAAG,YAAY,EAAE;EACvD,CAAC;AACD,QAAM,aAAa,cAAc,MAAM,UAAU,MAAM,OAAO;AAG9D,QAAM,cAAwE;IAC5E,GAAG;IACH;IACA,eAAe;IACf,UAAU,QAAQ,aAAa,KAAK,aAAa,CAAC;IAClD,eAAe,CAAC,UAAW,SAAS,UAAU;IAC9C,kBAAkB,MAAO,iBAAiB,UAAU;IACpD,oBAAoB,CAAC,eAAgB,iBAAiB,UAAU;EAClE;AAEA,WAAS,kBAAkB,YAAoB,KAAiB;AAC9D,WAAO,6BAA6B,YAAY,iBAAiB,SAAS,OAAO,GAAG;EACtF;AAEA,MAAI,gBAAgB,cAAc;AAChC,eACE;MAAC;MAAA;QACE,GAAG;QACJ,KAAK;QACL,uBAAuB,MAAM;AAC3B,cAAI,QAAQ,YAAY,SAAS,SAAS;AACxC,kBAAM,YAAY,QAAQ,SAAS;AACnC,kBAAM,SAAS,yBAAyB,WAAW,OAAO,QAAQ,GAAG;AACrE,qBAAS,QAAQ,MAAM,YAAY,eAAe,MAAM;UAC1D;QACF;QACA,eAAe,CAAC,cAAc;AAC5B,cAAI,QAAQ,SAAU,SAAQ,SAAS,aAAa;QACtD;QACA,cAAc,CAAC,eAAe;AAC5B,cAAI,QAAQ,UAAU;AACpB,oBAAQ,SAAS,aAAa,kBAAkB,YAAY,QAAQ,GAAG;UACzE;QACF;MAAA;IACF;EAEJ;AAEA,MAAI,gBAAgB,YAAY;AAC9B,eACE;MAAC;MAAA;QACE,GAAG;QACJ,KAAK;QACL,uBAAuB,MAAM;AAC3B,cAAI,QAAQ,YAAY,SAAS,SAAS;AACxC,kBAAM,YAAY,QAAQ,SAAS;AACnC,kBAAM,SAAS,yBAAyB,WAAW,KAAK;AACxD,qBAAS,QAAQ,MAAM,YAAY,kBAAkB,MAAM;UAC7D;QACF;QACA,eAAe,CAAC,cAAc;AAC5B,cAAI,QAAQ,SAAU,SAAQ,SAAS,YAAY;QACrD;QACA,cAAc,CAAC,eAAe;AAC5B,cAAI,QAAQ,SAAU,SAAQ,SAAS,YAAY,kBAAkB,UAAU;QACjF;MAAA;IACF;EAEJ;AAEA,SAAO;AACT,CAAC;AAqBD,IAAM,uBAA6B,kBAGjC,CAAC,OAAkD,iBAAiB;AACpE,QAAM,EAAE,OAAO,eAAe,GAAG,eAAe,IAAI;AACpD,QAAM,UAAU,qBAAqB,gBAAgB,MAAM,iBAAiB;AAC5E,QAAM,CAAC,eAAe,gBAAgB,IAAU,gBAA8B;AAC9E,QAAM,MAAY,cAAuC,IAAI;AAC7D,QAAM,cAAc,gBAAgB,cAAc,KAAK,QAAQ,kBAAkB;AAE3E,EAAA,iBAAU,MAAM;AACpB,QAAI,IAAI,QAAS,kBAAiB,iBAAiB,IAAI,OAAO,CAAC;EACjE,GAAG,CAAC,GAAG,CAAC;AAER,aACE;IAAC;IAAA;MACC,oBAAiB;MAChB,GAAG;MACJ,KAAK;MACL;MACA,OAAO;QACL,QAAQ;QACR,MAAM,QAAQ,QAAQ,QAAQ,0CAA0C;QACxE,OAAO,QAAQ,QAAQ,QAAQ,0CAA0C;QACzE,CAAC,iCAAwC,GAAG,aAAa,KAAK,IAAI;QAClE,GAAG,MAAM;MACX;MACA,oBAAoB,CAAC,eAAe,MAAM,mBAAmB,WAAW,CAAC;MACzE,cAAc,CAAC,eAAe,MAAM,aAAa,WAAW,CAAC;MAC7D,eAAe,CAAC,OAAO,iBAAiB;AACtC,YAAI,QAAQ,UAAU;AACpB,gBAAM,YAAY,QAAQ,SAAS,aAAa,MAAM;AACtD,gBAAM,cAAc,SAAS;AAE7B,cAAI,iCAAiC,WAAW,YAAY,GAAG;AAC7D,kBAAM,eAAe;UACvB;QACF;MACF;MACA,UAAU,MAAM;AACd,YAAI,IAAI,WAAW,QAAQ,YAAY,eAAe;AACpD,wBAAc;YACZ,SAAS,QAAQ,SAAS;YAC1B,UAAU,QAAQ,SAAS;YAC3B,WAAW;cACT,MAAM,IAAI,QAAQ;cAClB,cAAc,MAAM,cAAc,WAAW;cAC7C,YAAY,MAAM,cAAc,YAAY;YAC9C;UACF,CAAC;QACH;MACF;IAAA;EACF;AAEJ,CAAC;AAED,IAAM,uBAA6B,kBAGjC,CAAC,OAAkD,iBAAiB;AACpE,QAAM,EAAE,OAAO,eAAe,GAAG,eAAe,IAAI;AACpD,QAAM,UAAU,qBAAqB,gBAAgB,MAAM,iBAAiB;AAC5E,QAAM,CAAC,eAAe,gBAAgB,IAAU,gBAA8B;AAC9E,QAAM,MAAY,cAAuC,IAAI;AAC7D,QAAM,cAAc,gBAAgB,cAAc,KAAK,QAAQ,kBAAkB;AAE3E,EAAA,iBAAU,MAAM;AACpB,QAAI,IAAI,QAAS,kBAAiB,iBAAiB,IAAI,OAAO,CAAC;EACjE,GAAG,CAAC,GAAG,CAAC;AAER,aACE;IAAC;IAAA;MACC,oBAAiB;MAChB,GAAG;MACJ,KAAK;MACL;MACA,OAAO;QACL,KAAK;QACL,OAAO,QAAQ,QAAQ,QAAQ,IAAI;QACnC,MAAM,QAAQ,QAAQ,QAAQ,IAAI;QAClC,QAAQ;QACR,CAAC,kCAAyC,GAAG,aAAa,KAAK,IAAI;QACnE,GAAG,MAAM;MACX;MACA,oBAAoB,CAAC,eAAe,MAAM,mBAAmB,WAAW,CAAC;MACzE,cAAc,CAAC,eAAe,MAAM,aAAa,WAAW,CAAC;MAC7D,eAAe,CAAC,OAAO,iBAAiB;AACtC,YAAI,QAAQ,UAAU;AACpB,gBAAM,YAAY,QAAQ,SAAS,YAAY,MAAM;AACrD,gBAAM,cAAc,SAAS;AAE7B,cAAI,iCAAiC,WAAW,YAAY,GAAG;AAC7D,kBAAM,eAAe;UACvB;QACF;MACF;MACA,UAAU,MAAM;AACd,YAAI,IAAI,WAAW,QAAQ,YAAY,eAAe;AACpD,wBAAc;YACZ,SAAS,QAAQ,SAAS;YAC1B,UAAU,QAAQ,SAAS;YAC3B,WAAW;cACT,MAAM,IAAI,QAAQ;cAClB,cAAc,MAAM,cAAc,UAAU;cAC5C,YAAY,MAAM,cAAc,aAAa;YAC/C;UACF,CAAC;QACH;MACF;IAAA;EACF;AAEJ,CAAC;AAaD,IAAM,CAAC,mBAAmB,mBAAmB,IAC3C,wBAA0C,cAAc;AAkB1D,IAAM,0BAAgC,kBAGpC,CAAC,OAAkD,iBAAiB;AACpE,QAAM;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,GAAG;EACL,IAAI;AACJ,QAAM,UAAU,qBAAqB,gBAAgB,iBAAiB;AACtE,QAAM,CAAC,WAAW,YAAY,IAAU,gBAA4C,IAAI;AACxF,QAAM,cAAc,gBAAgB,cAAc,CAAC,SAAS,aAAa,IAAI,CAAC;AAC9E,QAAM,UAAgB,cAAuB,IAAI;AACjD,QAAM,0BAAgC,cAAe,EAAE;AACvD,QAAM,WAAW,QAAQ;AACzB,QAAM,eAAe,MAAM,UAAU,MAAM;AAC3C,QAAM,oBAAoB,eAAe,aAAa;AACtD,QAAM,4BAA4B,eAAe,qBAAqB;AACtE,QAAM,eAAe,oBAAoB,UAAU,EAAE;AAErD,WAAS,iBAAiB,OAAwC;AAChE,QAAI,QAAQ,SAAS;AACnB,YAAM,IAAI,MAAM,UAAU,QAAQ,QAAQ;AAC1C,YAAM,IAAI,MAAM,UAAU,QAAQ,QAAQ;AAC1C,mBAAa,EAAE,GAAG,EAAE,CAAC;IACvB;EACF;AAMM,EAAA,iBAAU,MAAM;AACpB,UAAM,cAAc,CAAC,UAAsB;AACzC,YAAM,UAAU,MAAM;AACtB,YAAM,mBAAmB,uCAAW,SAAS;AAC7C,UAAI,iBAAkB,mBAAkB,OAAO,YAAY;IAC7D;AACA,aAAS,iBAAiB,SAAS,aAAa,EAAE,SAAS,MAAM,CAAC;AAClE,WAAO,MAAM,SAAS,oBAAoB,SAAS,aAAa,EAAE,SAAS,MAAM,CAAQ;EAC3F,GAAG,CAAC,UAAU,WAAW,cAAc,iBAAiB,CAAC;AAKnD,EAAA,iBAAU,2BAA2B,CAAC,OAAO,yBAAyB,CAAC;AAE7E,oBAAkB,WAAW,YAAY;AACzC,oBAAkB,QAAQ,SAAS,YAAY;AAE/C,aACE;IAAC;IAAA;MACC,OAAO;MACP;MACA;MACA,eAAe,eAAe,aAAa;MAC3C,kBAAkB,eAAe,gBAAgB;MACjD,uBAAuB;MACvB,oBAAoB,eAAe,kBAAkB;MAErD,cAAA;QAAC,UAAU;QAAV;UACE,GAAG;UACJ,KAAK;UACL,OAAO,EAAE,UAAU,YAAY,GAAG,eAAe,MAAM;UACvD,eAAe,qBAAqB,MAAM,eAAe,CAAC,UAAU;AAClE,kBAAM,cAAc;AACpB,gBAAI,MAAM,WAAW,aAAa;AAChC,oBAAM,UAAU,MAAM;AACtB,sBAAQ,kBAAkB,MAAM,SAAS;AACzC,sBAAQ,UAAU,UAAW,sBAAsB;AAGnD,sCAAwB,UAAU,SAAS,KAAK,MAAM;AACtD,uBAAS,KAAK,MAAM,mBAAmB;AACvC,kBAAI,QAAQ,SAAU,SAAQ,SAAS,MAAM,iBAAiB;AAC9D,+BAAiB,KAAK;YACxB;UACF,CAAC;UACD,eAAe,qBAAqB,MAAM,eAAe,gBAAgB;UACzE,aAAa,qBAAqB,MAAM,aAAa,CAAC,UAAU;AAC9D,kBAAM,UAAU,MAAM;AACtB,gBAAI,QAAQ,kBAAkB,MAAM,SAAS,GAAG;AAC9C,sBAAQ,sBAAsB,MAAM,SAAS;YAC/C;AACA,qBAAS,KAAK,MAAM,mBAAmB,wBAAwB;AAC/D,gBAAI,QAAQ,SAAU,SAAQ,SAAS,MAAM,iBAAiB;AAC9D,oBAAQ,UAAU;UACpB,CAAC;QAAA;MACH;IAAA;EACF;AAEJ,CAAC;AAMD,IAAM,aAAa;AAWnB,IAAM,kBAAwB;EAC5B,CAAC,OAA0C,iBAAiB;AAC1D,UAAM,EAAE,YAAY,GAAG,WAAW,IAAI;AACtC,UAAM,mBAAmB,oBAAoB,YAAY,MAAM,iBAAiB;AAChF,eACE,wBAAC,UAAA,EAAS,SAAS,cAAc,iBAAiB,UAChD,cAAA,wBAAC,qBAAA,EAAoB,KAAK,cAAe,GAAG,WAAA,CAAY,EAAA,CAC1D;EAEJ;AACF;AAKA,IAAM,sBAA4B;EAChC,CAAC,OAA8C,iBAAiB;AAC9D,UAAM,EAAE,mBAAmB,OAAO,GAAG,WAAW,IAAI;AACpD,UAAM,oBAAoB,qBAAqB,YAAY,iBAAiB;AAC5E,UAAM,mBAAmB,oBAAoB,YAAY,iBAAiB;AAC1E,UAAM,EAAE,sBAAsB,IAAI;AAClC,UAAM,cAAc;MAAgB;MAAc,CAAC,SACjD,iBAAiB,cAAc,IAAI;IACrC;AACA,UAAM,kCAAwC,cAAmB;AACjE,UAAM,oBAAoB,oBAAoB,MAAM;AAClD,UAAI,gCAAgC,SAAS;AAC3C,wCAAgC,QAAQ;AACxC,wCAAgC,UAAU;MAC5C;IACF,GAAG,GAAG;AAEA,IAAA,iBAAU,MAAM;AACpB,YAAM,WAAW,kBAAkB;AACnC,UAAI,UAAU;AAQZ,cAAM,eAAe,MAAM;AACzB,4BAAkB;AAClB,cAAI,CAAC,gCAAgC,SAAS;AAC5C,kBAAM,WAAW,0BAA0B,UAAU,qBAAqB;AAC1E,4CAAgC,UAAU;AAC1C,kCAAsB;UACxB;QACF;AACA,8BAAsB;AACtB,iBAAS,iBAAiB,UAAU,YAAY;AAChD,eAAO,MAAM,SAAS,oBAAoB,UAAU,YAAY;MAClE;IACF,GAAG,CAAC,kBAAkB,UAAU,mBAAmB,qBAAqB,CAAC;AAEzE,eACE;MAAC,UAAU;MAAV;QACC,cAAY,iBAAiB,WAAW,YAAY;QACnD,GAAG;QACJ,KAAK;QACL,OAAO;UACL,OAAO;UACP,QAAQ;UACR,GAAG;QACL;QACA,sBAAsB,qBAAqB,MAAM,sBAAsB,CAAC,UAAU;AAChF,gBAAM,QAAQ,MAAM;AACpB,gBAAM,YAAY,MAAM,sBAAsB;AAC9C,gBAAM,IAAI,MAAM,UAAU,UAAU;AACpC,gBAAM,IAAI,MAAM,UAAU,UAAU;AACpC,2BAAiB,mBAAmB,EAAE,GAAG,EAAE,CAAC;QAC9C,CAAC;QACD,aAAa,qBAAqB,MAAM,aAAa,iBAAiB,gBAAgB;MAAA;IACxF;EAEJ;AACF;AAEA,gBAAgB,cAAc;AAM9B,IAAM,cAAc;AAKpB,IAAM,mBAAyB;EAC7B,CAAC,OAA2C,iBAAiB;AAC3D,UAAM,UAAU,qBAAqB,aAAa,MAAM,iBAAiB;AACzE,UAAM,2BAA2B,QAAQ,QAAQ,cAAc,QAAQ,UAAU;AACjF,UAAM,YAAY,QAAQ,SAAS,YAAY;AAC/C,WAAO,gBAAY,wBAAC,sBAAA,EAAsB,GAAG,OAAO,KAAK,aAAA,CAAc,IAAK;EAC9E;AACF;AAEA,iBAAiB,cAAc;AAO/B,IAAM,uBAA6B,kBAGjC,CAAC,OAA+C,iBAAiB;AACjE,QAAM,EAAE,mBAAmB,GAAG,YAAY,IAAI;AAC9C,QAAM,UAAU,qBAAqB,aAAa,iBAAiB;AACnE,QAAM,CAAC,OAAO,QAAQ,IAAU,gBAAS,CAAC;AAC1C,QAAM,CAAC,QAAQ,SAAS,IAAU,gBAAS,CAAC;AAC5C,QAAM,UAAU,QAAQ,SAAS,MAAM;AAEvC,oBAAkB,QAAQ,YAAY,MAAM;;AAC1C,UAAMC,YAAS,aAAQ,eAAR,mBAAoB,iBAAgB;AACnD,YAAQ,qBAAqBA,OAAM;AACnC,cAAUA,OAAM;EAClB,CAAC;AAED,oBAAkB,QAAQ,YAAY,MAAM;;AAC1C,UAAMC,WAAQ,aAAQ,eAAR,mBAAoB,gBAAe;AACjD,YAAQ,oBAAoBA,MAAK;AACjC,aAASA,MAAK;EAChB,CAAC;AAED,SAAO,cACL;IAAC,UAAU;IAAV;MACE,GAAG;MACJ,KAAK;MACL,OAAO;QACL;QACA;QACA,UAAU;QACV,OAAO,QAAQ,QAAQ,QAAQ,IAAI;QACnC,MAAM,QAAQ,QAAQ,QAAQ,IAAI;QAClC,QAAQ;QACR,GAAG,MAAM;MACX;IAAA;EACF,IACE;AACN,CAAC;AAID,SAAS,MAAM,OAAgB;AAC7B,SAAO,QAAQ,SAAS,OAAO,EAAE,IAAI;AACvC;AAEA,SAAS,cAAc,cAAsB,aAAqB;AAChE,QAAM,QAAQ,eAAe;AAC7B,SAAO,MAAM,KAAK,IAAI,IAAI;AAC5B;AAEA,SAAS,aAAa,OAAc;AAClC,QAAM,QAAQ,cAAc,MAAM,UAAU,MAAM,OAAO;AACzD,QAAM,mBAAmB,MAAM,UAAU,eAAe,MAAM,UAAU;AACxE,QAAM,aAAa,MAAM,UAAU,OAAO,oBAAoB;AAE9D,SAAO,KAAK,IAAI,WAAW,EAAE;AAC/B;AAEA,SAAS,6BACP,YACA,eACA,OACA,MAAiB,OACjB;AACA,QAAM,cAAc,aAAa,KAAK;AACtC,QAAM,cAAc,cAAc;AAClC,QAAM,SAAS,iBAAiB;AAChC,QAAM,qBAAqB,cAAc;AACzC,QAAM,gBAAgB,MAAM,UAAU,eAAe;AACrD,QAAM,gBAAgB,MAAM,UAAU,OAAO,MAAM,UAAU,aAAa;AAC1E,QAAM,eAAe,MAAM,UAAU,MAAM;AAC3C,QAAM,cAAc,QAAQ,QAAQ,CAAC,GAAG,YAAY,IAAI,CAAC,eAAe,IAAI,CAAC;AAC7E,QAAM,cAAc,YAAY,CAAC,eAAe,aAAa,GAAG,WAA+B;AAC/F,SAAO,YAAY,UAAU;AAC/B;AAEA,SAAS,yBAAyB,WAAmB,OAAc,MAAiB,OAAO;AACzF,QAAM,cAAc,aAAa,KAAK;AACtC,QAAM,mBAAmB,MAAM,UAAU,eAAe,MAAM,UAAU;AACxE,QAAM,YAAY,MAAM,UAAU,OAAO;AACzC,QAAM,eAAe,MAAM,UAAU,MAAM;AAC3C,QAAM,cAAc,YAAY;AAChC,QAAM,mBAAmB,QAAQ,QAAQ,CAAC,GAAG,YAAY,IAAI,CAAC,eAAe,IAAI,CAAC;AAClF,QAAM,wBAAwB,MAAM,WAAW,gBAAoC;AACnF,QAAM,cAAc,YAAY,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,WAAW,CAAC;AACnE,SAAO,YAAY,qBAAqB;AAC1C;AAGA,SAAS,YAAY,OAAkC,QAAmC;AACxF,SAAO,CAAC,UAAkB;AACxB,QAAI,MAAM,CAAC,MAAM,MAAM,CAAC,KAAK,OAAO,CAAC,MAAM,OAAO,CAAC,EAAG,QAAO,OAAO,CAAC;AACrE,UAAM,SAAS,OAAO,CAAC,IAAI,OAAO,CAAC,MAAM,MAAM,CAAC,IAAI,MAAM,CAAC;AAC3D,WAAO,OAAO,CAAC,IAAI,SAAS,QAAQ,MAAM,CAAC;EAC7C;AACF;AAEA,SAAS,iCAAiC,WAAmB,cAAsB;AACjF,SAAO,YAAY,KAAK,YAAY;AACtC;AAIA,IAAM,4BAA4B,CAAC,MAAmB,UAAU,MAAM;AAAC,MAAM;AAC3E,MAAI,eAAe,EAAE,MAAM,KAAK,YAAY,KAAK,KAAK,UAAU;AAChE,MAAI,MAAM;AACV,GAAC,SAAS,OAAO;AACf,UAAM,WAAW,EAAE,MAAM,KAAK,YAAY,KAAK,KAAK,UAAU;AAC9D,UAAM,qBAAqB,aAAa,SAAS,SAAS;AAC1D,UAAM,mBAAmB,aAAa,QAAQ,SAAS;AACvD,QAAI,sBAAsB,iBAAkB,SAAQ;AACpD,mBAAe;AACf,UAAM,OAAO,sBAAsB,IAAI;EACzC,GAAG;AACH,SAAO,MAAM,OAAO,qBAAqB,GAAG;AAC9C;AAEA,SAAS,oBAAoB,UAAsB,OAAe;AAChE,QAAM,iBAAiB,eAAe,QAAQ;AAC9C,QAAM,mBAAyB,cAAO,CAAC;AACjC,EAAA,iBAAU,MAAM,MAAM,OAAO,aAAa,iBAAiB,OAAO,GAAG,CAAC,CAAC;AAC7E,SAAa,mBAAY,MAAM;AAC7B,WAAO,aAAa,iBAAiB,OAAO;AAC5C,qBAAiB,UAAU,OAAO,WAAW,gBAAgB,KAAK;EACpE,GAAG,CAAC,gBAAgB,KAAK,CAAC;AAC5B;AAEA,SAAS,kBAAkB,SAA6B,UAAsB;AAC5E,QAAM,eAAe,eAAe,QAAQ;AAC5C,mBAAgB,MAAM;AACpB,QAAI,MAAM;AACV,QAAI,SAAS;AAQX,YAAM,iBAAiB,IAAI,eAAe,MAAM;AAC9C,6BAAqB,GAAG;AACxB,cAAM,OAAO,sBAAsB,YAAY;MACjD,CAAC;AACD,qBAAe,QAAQ,OAAO;AAC9B,aAAO,MAAM;AACX,eAAO,qBAAqB,GAAG;AAC/B,uBAAe,UAAU,OAAO;MAClC;IACF;EACF,GAAG,CAAC,SAAS,YAAY,CAAC;AAC5B;AASA,SAAS,WACP,SACA,SACA;AACA,QAAM,EAAE,SAAS,SAAS,IAAI;AAC9B,MAAI,CAAC,QAAS,QAAO,OAAO,YAAY,aAAa,QAAQ,QAAQ,IAAI;AAEzE,QAAM,aAAmB,gBAAS,KAAK,QAAQ;AAC/C,SAAa,oBAAa,YAAY;IACpC,UAAU,OAAO,YAAY,aAAa,QAAQ,WAAW,MAAM,QAAQ,IAAI;EACjF,CAAC;AACH;AAIA,IAAM,OAAO;AACb,IAAM,WAAW;AACjB,IAAM,YAAY;AAClB,IAAM,QAAQ;AACd,IAAM,SAAS;", "names": ["children", "height", "width"]}