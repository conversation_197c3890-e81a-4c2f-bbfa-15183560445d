
import {
  CustomDialog,
  CustomDialogContent,
  CustomDialogHeader,
  CustomDialogTitle,
  CustomDialogFooter,
} from '@/components/custom-dialog';
import { Button } from '@/components/ui/button';
import { Department, Voucher } from '@/lib/types';
import { formatVMSDate, formatVMSDateTime } from '@/utils/formatUtils';
import { Trash, X } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

interface ViewVoucherModalProps {
  viewingVoucher: Voucher | null;
  isOpen: boolean;
  onClose: () => void;
  department: Department;
  onDelete?: (voucherId: string) => void;
}

export function ViewVoucherModal({
  viewingVoucher,
  isOpen,
  onClose,
  department,
  onDelete
}: ViewVoucherModalProps) {
  if (!viewingVoucher || !isOpen) {
    return null;
  }

  const isReturnedVoucher = viewingVoucher.isReturned || viewingVoucher.status === "VOUCHER RETURNED";

  return (
    <CustomDialog onClose={onClose}>
      <CustomDialogContent className="sm:max-w-xl">
        <CustomDialogHeader>
          <CustomDialogTitle className="text-center flex items-center justify-center gap-2">
            VOUCHER DETAILS
            {isReturnedVoucher && (
              <Badge variant="outline" className="ml-2">RETURNED</Badge>
            )}
          </CustomDialogTitle>
        </CustomDialogHeader>

        <div className="py-4 grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <div>
              <span className="font-semibold">Voucher ID:</span> {viewingVoucher.voucherId}
            </div>
            <div>
              <span className="font-semibold">Claimant:</span> {viewingVoucher.claimant}
            </div>
            <div>
              <span className="font-semibold">Description:</span> {viewingVoucher.description}
            </div>
            <div>
              <span className="font-semibold">Amount:</span> {viewingVoucher.amount} {viewingVoucher.currency}
            </div>
            <div>
              <span className="font-semibold">Department:</span> {viewingVoucher.department}
            </div>
          </div>

          <div className="space-y-2">
            <div>
              <span className="font-semibold">Date:</span> {formatVMSDate(viewingVoucher.date)}
            </div>
            <div>
              <span className="font-semibold">Status:</span> {viewingVoucher.status}
            </div>
            {viewingVoucher.preAuditedAmount && (
              <div>
                <span className="font-semibold">Pre-Audited Amount:</span> {viewingVoucher.preAuditedAmount} {viewingVoucher.currency}
              </div>
            )}
            {viewingVoucher.dispatchedBy && (
              <div>
                <span className="font-semibold">Dispatched By:</span> {viewingVoucher.dispatchedBy}
              </div>
            )}
            {viewingVoucher.isReturned && viewingVoucher.returnTime && (
              <div>
                <span className="font-semibold">Return Time:</span> {formatVMSDateTime(viewingVoucher.returnTime)}
              </div>
            )}
          </div>
        </div>

        <CustomDialogFooter className="flex justify-between items-center">
          {onDelete && (
            <Button
              variant="destructive"
              size="sm"
              onClick={() => {
                onDelete(viewingVoucher.id);
                onClose();
              }}
            >
              <Trash className="h-4 w-4 mr-2" />
              Delete
            </Button>
          )}
          <Button onClick={onClose}>
            Close
          </Button>
        </CustomDialogFooter>
      </CustomDialogContent>
    </CustomDialog>
  );
}
