const mysql = require('mysql2/promise');

async function debugCurrentBatches() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root', 
    password: 'vms@2025@1989',
    database: 'vms_production'
  });
  
  console.log('🔍 DEBUGGING CURRENT BATCH STATE');
  console.log('=================================');
  
  // Check all current batches for FINANCE department from audit
  const [batches] = await connection.execute(`
    SELECT 
      vb.id,
      vb.department,
      vb.sent_by,
      vb.sent_time,
      vb.received,
      vb.from_audit,
      COUNT(bv.voucher_id) as voucher_count
    FROM voucher_batches vb
    LEFT JOIN batch_vouchers bv ON vb.id = bv.batch_id
    WHERE vb.department = 'FINANCE' 
    AND vb.from_audit = TRUE
    GROUP BY vb.id
    ORDER BY vb.sent_time DESC
  `);
  
  console.log(`📊 Total FINANCE batches from audit: ${batches.length}`);
  console.log('');
  
  for (let i = 0; i < batches.length; i++) {
    const batch = batches[i];
    const time = new Date(batch.sent_time).toLocaleString();
    console.log(`${i + 1}. BATCH ${batch.id}`);
    console.log(`   📅 Sent: ${time}`);
    console.log(`   👤 Sent by: ${batch.sent_by}`);
    console.log(`   📋 Vouchers: ${batch.voucher_count}`);
    console.log(`   ✅ Received: ${batch.received ? 'YES' : 'NO'}`);
    
    // Get voucher details for this batch
    const [voucherDetails] = await connection.execute(`
      SELECT
        v.id,
        v.voucher_id,
        v.amount,
        v.description,
        v.created_at
      FROM batch_vouchers bv
      JOIN vouchers v ON bv.voucher_id = v.id
      WHERE bv.batch_id = ?
      ORDER BY v.created_at
    `, [batch.id]);

    console.log(`   📝 Voucher Details:`);
    voucherDetails.forEach((voucher, idx) => {
      console.log(`      ${idx + 1}. ${voucher.voucher_id} - $${voucher.amount} - ${voucher.description}`);
    });
    console.log('');
  }
  
  // Check for duplicate vouchers across batches
  console.log('🔍 CHECKING FOR DUPLICATE VOUCHERS:');
  console.log('-----------------------------------');
  
  const [duplicates] = await connection.execute(`
    SELECT
      bv1.voucher_id,
      v.voucher_id as voucher_number,
      bv1.batch_id as batch1,
      bv2.batch_id as batch2,
      vb1.sent_time as time1,
      vb2.sent_time as time2
    FROM batch_vouchers bv1
    JOIN batch_vouchers bv2 ON bv1.voucher_id = bv2.voucher_id AND bv1.batch_id != bv2.batch_id
    JOIN voucher_batches vb1 ON bv1.batch_id = vb1.id
    JOIN voucher_batches vb2 ON bv2.batch_id = vb2.id
    JOIN vouchers v ON bv1.voucher_id = v.id
    WHERE vb1.department = 'FINANCE'
    AND vb2.department = 'FINANCE'
    AND vb1.from_audit = TRUE
    AND vb2.from_audit = TRUE
    ORDER BY v.voucher_id
  `);
  
  if (duplicates.length > 0) {
    console.log(`❌ Found ${duplicates.length} duplicate voucher entries across batches:`);
    duplicates.forEach(dup => {
      const time1 = new Date(dup.time1).toLocaleString();
      const time2 = new Date(dup.time2).toLocaleString();
      console.log(`   📋 Voucher ${dup.voucher_number}:`);
      console.log(`      - Batch ${dup.batch1} (${time1})`);
      console.log(`      - Batch ${dup.batch2} (${time2})`);
    });
  } else {
    console.log('✅ No duplicate vouchers found across batches');
  }
  
  // Check if batches have different vouchers (legitimate separate batches)
  console.log('\n🔍 ANALYZING BATCH CONTENT:');
  console.log('---------------------------');
  
  if (batches.length >= 2) {
    const batch1 = batches[0];
    const batch2 = batches[1];
    
    const [batch1Vouchers] = await connection.execute(`
      SELECT voucher_id FROM batch_vouchers WHERE batch_id = ?
    `, [batch1.id]);
    
    const [batch2Vouchers] = await connection.execute(`
      SELECT voucher_id FROM batch_vouchers WHERE batch_id = ?
    `, [batch2.id]);
    
    const batch1VoucherIds = batch1Vouchers.map(v => v.voucher_id);
    const batch2VoucherIds = batch2Vouchers.map(v => v.voucher_id);
    
    const commonVouchers = batch1VoucherIds.filter(id => batch2VoucherIds.includes(id));
    
    if (commonVouchers.length > 0) {
      console.log(`❌ DUPLICATE DETECTED: ${commonVouchers.length} vouchers appear in both batches`);
      console.log(`   This confirms duplicate batch creation issue`);
    } else {
      console.log(`✅ LEGITIMATE BATCHES: No common vouchers between the two most recent batches`);
      console.log(`   These appear to be separate legitimate dispatch operations`);
    }
  }
  
  await connection.end();
}

debugCurrentBatches().catch(console.error);
