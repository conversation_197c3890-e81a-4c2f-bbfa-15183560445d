const axios = require('axios');

async function testArchitecturalFixes() {
  try {
    console.log('🧪 TESTING ARCHITECTURAL FIXES...\n');

    // Test 1: Voucher Creation with UUID Validation
    console.log('🔍 TEST 1: Voucher Creation with UUID Validation');
    const testVoucher = {
      claimant: 'TEST USER - ARCHITECTURAL FIX',
      description: 'Testing unified ID system',
      amount: 100.00,
      currency: 'GHS',
      department: 'FINANCE',
      taxType: 'VAT',
      taxAmount: 15.00,
      comment: 'Testing architectural fixes'
    };

    console.log('📤 Creating voucher via API...');
    const response = await axios.post('http://localhost:8080/api/vouchers', testVoucher, {
      headers: {
        'Content-Type': 'application/json'
      },
      withCredentials: true
    });

    console.log('✅ Voucher created successfully!');
    const voucherId = response.data.id;
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    
    if (uuidRegex.test(voucherId)) {
      console.log('✅ ARCHITECTURAL FIX VERIFIED: Voucher has valid UUID format:', voucherId);
    } else {
      console.log('❌ CRITICAL: Voucher has invalid ID format:', voucherId);
      return;
    }

    // Test 2: Batch Creation with UUID Validation
    console.log('\n🔍 TEST 2: Batch Creation with UUID Validation');
    const batchData = {
      department: 'FINANCE',
      voucherIds: [voucherId],
      dispatchedBy: 'TEST DISPATCHER'
    };

    console.log('📤 Creating batch with voucher ID:', voucherId);
    const batchResponse = await axios.post('http://localhost:8080/api/batches', batchData, {
      headers: {
        'Content-Type': 'application/json'
      },
      withCredentials: true
    });

    console.log('✅ Batch created successfully!');
    console.log('📊 Batch ID:', batchResponse.data.id);

    // Test 3: Verify voucher was updated
    console.log('\n🔍 TEST 3: Verify Voucher Status Update');
    const voucherCheck = await axios.get(`http://localhost:8080/api/vouchers/${voucherId}`, {
      withCredentials: true
    });

    console.log('✅ Voucher status after batch creation:', voucherCheck.data.status);
    console.log('✅ Sent to audit:', voucherCheck.data.sent_to_audit);

    console.log('\n🎉 ALL ARCHITECTURAL FIXES VERIFIED SUCCESSFULLY!');
    console.log('✅ UUID-based ID system working correctly');
    console.log('✅ Voucher-to-audit workflow functioning properly');
    console.log('✅ No more ID mismatch errors');

  } catch (error) {
    console.error('❌ ARCHITECTURAL TEST FAILED:', error.response?.data || error.message);
    
    if (error.response?.status === 404 && error.response?.data?.error?.includes('not found')) {
      console.error('🚨 CRITICAL: The old ID mismatch issue is still present!');
      console.error('🔧 This indicates the architectural fixes need further investigation.');
    }
    
    throw error;
  }
}

testArchitecturalFixes();
