const mysql = require('mysql2/promise');

// Database configuration (matching server config)
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: 'vms@2025@1989',
  database: 'vms_clean_db'
};

async function checkDatabaseStatus() {
  let connection;
  
  try {
    console.log('🔍 Checking VMS database status...');
    
    // Connect to database
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Connected to database: vms_clean_db');

    // Check what tables exist
    console.log('\n📋 Checking existing tables...');
    const [tables] = await connection.execute('SHOW TABLES');
    
    if (tables.length === 0) {
      console.log('📭 No tables found - database is empty');
      console.log('💡 This means the server will initialize tables when it starts');
    } else {
      const tableNames = tables.map(row => Object.values(row)[0]);
      console.log('📋 Found tables:', tableNames);
      
      // Check record counts for voucher-related tables
      const voucherTables = ['vouchers', 'voucher_batches', 'batch_vouchers', 'notifications'];
      
      console.log('\n📊 Record counts:');
      for (const tableName of voucherTables) {
        if (tableNames.includes(tableName)) {
          try {
            const [count] = await connection.execute(`SELECT COUNT(*) as count FROM ${tableName}`);
            console.log(`   ${tableName}: ${count[0].count} records`);
          } catch (error) {
            console.log(`   ${tableName}: Error counting - ${error.message}`);
          }
        } else {
          console.log(`   ${tableName}: Table does not exist`);
        }
      }
    }

    console.log('\n✅ Database status check completed');

  } catch (error) {
    console.error('\n❌ Error checking database:', error.message);
    
    if (error.code === 'ER_BAD_DB_ERROR') {
      console.log('💡 Database "vms_clean_db" does not exist yet');
      console.log('💡 The server will create it when it starts');
    }
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the check
checkDatabaseStatus()
  .then(() => {
    console.log('\n🎯 SUMMARY: The VMS system is ready for fresh voucher creation!');
    console.log('🚀 Start the server and begin creating vouchers');
  })
  .catch((error) => {
    console.error('\n💥 Database check failed:', error.message);
  });
