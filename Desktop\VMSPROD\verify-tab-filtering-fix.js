const mysql = require('mysql2/promise');

async function verifyTabFilteringFix() {
  console.log('✅ VERIFYING TAB FILTERING FIX');
  console.log('=' .repeat(60));

  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });

  try {
    // Get all FINANCE vouchers
    const [allVouchers] = await connection.execute(`
      SELECT 
        voucher_id, status, department, original_department, 
        sent_to_audit, dispatched, deleted
      FROM vouchers 
      WHERE (department = 'FINANCE' OR original_department = 'FINANCE')
        AND deleted = FALSE
      ORDER BY voucher_id
    `);

    console.log(`📊 Found ${allVouchers.length} FINANCE-related vouchers`);

    // Test each voucher against the filtering logic
    console.log('\n🧪 TESTING TAB FILTERING LOGIC:');
    console.log('-'.repeat(60));

    let pendingCount = 0;
    let processingCount = 0;
    let certifiedCount = 0;
    let rejectedCount = 0;
    let returnedCount = 0;
    let duplicateCount = 0;

    allVouchers.forEach((voucher, index) => {
      console.log(`${index + 1}. ${voucher.voucher_id} (${voucher.status})`);
      console.log(`   Dept: ${voucher.department}, Original: ${voucher.original_department}`);
      console.log(`   Sent to Audit: ${voucher.sent_to_audit}, Dispatched: ${voucher.dispatched}`);

      // PENDING filter: v.department === 'FINANCE' && !v.sentToAudit && (v.status === 'PENDING' || v.status === 'PENDING SUBMISSION')
      const shouldBeInPending = 
        voucher.department === 'FINANCE' && 
        !voucher.sent_to_audit && 
        (voucher.status === 'PENDING' || voucher.status === 'PENDING SUBMISSION');

      // PROCESSING filter: v.originalDepartment === 'FINANCE' && v.sentToAudit === true && v.status !== 'VOUCHER CERTIFIED' && v.status !== 'VOUCHER REJECTED' && v.status !== 'VOUCHER RETURNED'
      const shouldBeInProcessing = 
        voucher.original_department === 'FINANCE' && 
        voucher.sent_to_audit === 1 &&
        voucher.status !== 'VOUCHER CERTIFIED' &&
        voucher.status !== 'VOUCHER REJECTED' &&
        voucher.status !== 'VOUCHER RETURNED';

      // CERTIFIED filter: v.originalDepartment === 'FINANCE' && v.status === 'VOUCHER CERTIFIED'
      const shouldBeInCertified = 
        voucher.original_department === 'FINANCE' && 
        voucher.status === 'VOUCHER CERTIFIED';

      // REJECTED filter: v.originalDepartment === 'FINANCE' && v.status === 'VOUCHER REJECTED'
      const shouldBeInRejected = 
        voucher.original_department === 'FINANCE' && 
        voucher.status === 'VOUCHER REJECTED';

      // RETURNED filter: v.originalDepartment === 'FINANCE' && v.status === 'VOUCHER RETURNED'
      const shouldBeInReturned = 
        voucher.original_department === 'FINANCE' && 
        voucher.status === 'VOUCHER RETURNED';

      // Count tabs
      const tabCount = [shouldBeInPending, shouldBeInProcessing, shouldBeInCertified, shouldBeInRejected, shouldBeInReturned].filter(Boolean).length;
      
      if (tabCount > 1) {
        duplicateCount++;
        console.log(`   ❌ APPEARS IN ${tabCount} TABS - DUPLICATE ISSUE!`);
      }

      if (shouldBeInPending) {
        pendingCount++;
        console.log(`   📋 PENDING tab: ✅`);
      }
      if (shouldBeInProcessing) {
        processingCount++;
        console.log(`   🔄 PROCESSING tab: ✅`);
      }
      if (shouldBeInCertified) {
        certifiedCount++;
        console.log(`   ✅ CERTIFIED tab: ✅`);
      }
      if (shouldBeInRejected) {
        rejectedCount++;
        console.log(`   ❌ REJECTED tab: ✅`);
      }
      if (shouldBeInReturned) {
        returnedCount++;
        console.log(`   ↩️ RETURNED tab: ✅`);
      }

      if (tabCount === 0) {
        console.log(`   ⚪ No tab (not visible in Finance dashboard)`);
      }

      console.log('');
    });

    // Summary
    console.log('📊 TAB FILTERING SUMMARY:');
    console.log('-'.repeat(40));
    console.log(`   PENDING tab: ${pendingCount} vouchers`);
    console.log(`   PROCESSING tab: ${processingCount} vouchers`);
    console.log(`   CERTIFIED tab: ${certifiedCount} vouchers`);
    console.log(`   REJECTED tab: ${rejectedCount} vouchers`);
    console.log(`   RETURNED tab: ${returnedCount} vouchers`);
    console.log(`   Total vouchers: ${allVouchers.length}`);

    if (duplicateCount === 0) {
      console.log('\n🎉 SUCCESS: No vouchers appear in multiple tabs!');
      console.log('✅ Tab filtering is working correctly');
    } else {
      console.log(`\n❌ ISSUE: ${duplicateCount} vouchers appear in multiple tabs`);
      console.log('🔧 Additional fixes needed');
    }

    // Specific test for certified vouchers
    console.log('\n🔍 CERTIFIED VOUCHER VERIFICATION:');
    const certifiedVouchers = allVouchers.filter(v => v.status === 'VOUCHER CERTIFIED');
    if (certifiedVouchers.length > 0) {
      certifiedVouchers.forEach(v => {
        const inPending = v.department === 'FINANCE' && !v.sent_to_audit && (v.status === 'PENDING' || v.status === 'PENDING SUBMISSION');
        console.log(`   ${v.voucher_id}: ${inPending ? '❌ STILL IN PENDING' : '✅ NOT IN PENDING'}`);
      });
    } else {
      console.log('   No certified vouchers found');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await connection.end();
  }
}

verifyTabFilteringFix().catch(console.error);
