const mysql = require('mysql2/promise');

async function analyzeVoucherIDMismatch() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });

  console.log('🔍 ANALYZING VOUCHER ID MISMATCH ISSUE...\n');

  // Check vouchers in database
  const [vouchers] = await connection.execute(`
    SELECT id, voucher_id, claimant, department, status, sent_to_audit, created_at
    FROM vouchers
    ORDER BY created_at DESC
    LIMIT 5
  `);

  console.log('📊 Recent vouchers in database:');
  vouchers.forEach(v => {
    console.log(`  ID: ${v.id}`);
    console.log(`  Voucher ID: ${v.voucher_id}`);
    console.log(`  Claimant: ${v.claimant}`);
    console.log(`  Department: ${v.department}`);
    console.log(`  Status: ${v.status}`);
    console.log(`  Sent to Audit: ${v.sent_to_audit}`);
    console.log(`  Created: ${v.created_at}`);
    console.log('  ---');
  });

  // Check if there are any vouchers with ID pattern 'v1752154807515'
  const [timestampVouchers] = await connection.execute(`
    SELECT id, voucher_id, claimant, department, status
    FROM vouchers
    WHERE id LIKE 'v%' AND CHAR_LENGTH(id) > 10
    ORDER BY created_at DESC
  `);

  console.log(`\n🔍 Vouchers with timestamp-like IDs (v + timestamp): ${timestampVouchers.length}`);
  timestampVouchers.forEach(v => {
    console.log(`  ID: ${v.id} | Voucher ID: ${v.voucher_id} | ${v.claimant}`);
  });

  await connection.end();
}

analyzeVoucherIDMismatch();
