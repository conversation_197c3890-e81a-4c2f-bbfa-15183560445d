const axios = require('axios');

async function testFrontendDispatchedData() {
  try {
    console.log('🔍 Testing frontend data for DISPATCHED tab...');
    
    // Test API endpoint
    const response = await axios.get('http://localhost:3000/api/vouchers', {
      withCredentials: true
    });
    
    console.log(`📊 Total vouchers from API: ${response.data.length}`);
    
    // Find our test voucher
    const testVoucher = response.data.find(v => v.id === 'd34ca623-d30c-4fd9-97e3-90ca0193bf84');
    
    if (testVoucher) {
      console.log('📄 Test voucher found in API response:');
      console.log('   Voucher ID:', testVoucher.voucher_id || testVoucher.voucherId);
      console.log('   Department:', testVoucher.department);
      console.log('   Original Department:', testVoucher.original_department || testVoucher.originalDepartment);
      console.log('   Status:', testVoucher.status);
      console.log('   Dispatched:', testVoucher.dispatched);
      console.log('   Audit Dispatched By:', testVoucher.audit_dispatched_by || testVoucher.auditDispatchedBy);
      
      console.log('\n🧪 DISPATCHED tab filtering test:');
      const originalDept = testVoucher.original_department || testVoucher.originalDepartment;
      const dispatched = testVoucher.dispatched;
      
      console.log('   Filter: originalDepartment === FINANCE && dispatched === true');
      console.log('   originalDepartment === FINANCE:', originalDept === 'FINANCE');
      console.log('   dispatched === true:', dispatched === true || dispatched === 1);
      console.log('   Should appear in DISPATCHED tab:', (originalDept === 'FINANCE' && (dispatched === true || dispatched === 1)) ? '✅ YES' : '❌ NO');
      
      // Test all FINANCE vouchers that should be in DISPATCHED tab
      const financeDispatchedVouchers = response.data.filter(v => {
        const origDept = v.original_department || v.originalDepartment;
        const isDispatched = v.dispatched === true || v.dispatched === 1;
        return origDept === 'FINANCE' && isDispatched;
      });
      
      console.log(`\n📋 All FINANCE vouchers that should appear in DISPATCHED tab: ${financeDispatchedVouchers.length}`);
      financeDispatchedVouchers.forEach((v, index) => {
        const voucherId = v.voucher_id || v.voucherId;
        const auditDispatchedBy = v.audit_dispatched_by || v.auditDispatchedBy;
        console.log(`   ${index + 1}. ${voucherId} - Dispatched by: ${auditDispatchedBy || 'N/A'}`);
      });
      
    } else {
      console.log('❌ Test voucher NOT found in API response');
    }
    
  } catch (error) {
    console.error('❌ Error testing frontend data:', error.message);
  }
}

testFrontendDispatchedData();
