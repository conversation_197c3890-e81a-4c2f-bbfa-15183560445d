const mysql = require('mysql2/promise');

async function testFinanceAPI() {
  console.log('🧪 Testing Finance Dashboard API');
  console.log('='.repeat(40));

  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'vms@2025@1989',
      database: 'vms_production'
    });
    
    console.log('✅ Connected to database');

    // Test what the API should return for Finance department
    console.log('\n1. BATCHES API SHOULD RETURN FOR FINANCE:');
    console.log('-'.repeat(40));
    
    const [financeBatches] = await connection.execute(`
      SELECT 
        id,
        department,
        sent_by,
        sent_time,
        from_audit,
        received
      FROM voucher_batches 
      WHERE department = 'FINANCE'
      ORDER BY sent_time DESC
    `);

    console.log(`📦 Total batches for FINANCE department: ${financeBatches.length}`);
    
    const unreceivedFromAudit = financeBatches.filter(b => b.from_audit && !b.received);
    const unreceivedToAudit = financeBatches.filter(b => !b.from_audit && !b.received);
    const received = financeBatches.filter(b => b.received);
    
    console.log(`   📥 Unreceived FROM audit: ${unreceivedFromAudit.length} (should show in Finance dashboard)`);
    console.log(`   📤 Unreceived TO audit: ${unreceivedToAudit.length} (should show in Audit dashboard)`);
    console.log(`   ✅ Received: ${received.length}`);

    // Show the unreceived batches from audit
    if (unreceivedFromAudit.length > 0) {
      console.log('\n📥 BATCHES THAT SHOULD APPEAR IN FINANCE DASHBOARD:');
      unreceivedFromAudit.forEach((batch, index) => {
        console.log(`   ${index + 1}. ${batch.id.substring(0, 8)}... sent by ${batch.sent_by} at ${batch.sent_time}`);
      });
    }

    // Test the frontend filtering logic
    console.log('\n2. FRONTEND FILTERING SIMULATION:');
    console.log('-'.repeat(40));
    
    // This simulates what getVoucherBatchesForDepartment should return for FINANCE
    const financeFilteredBatches = financeBatches; // All batches for FINANCE department
    console.log(`🏦 getVoucherBatchesForDepartment('FINANCE') should return: ${financeFilteredBatches.length} batches`);
    
    // This simulates what useDepartmentData should return for batchesArray
    const batchesFromAudit = financeFilteredBatches.filter(batch => 
      batch.from_audit === 1 && batch.received === 0
    );
    console.log(`📋 useDepartmentData batchesArray should contain: ${batchesFromAudit.length} batches`);
    
    // This simulates what VoucherBatchNotification should check
    const hasProcessedVouchers = batchesFromAudit.length > 0;
    console.log(`🔔 VoucherBatchNotification should show: ${hasProcessedVouchers ? 'YES' : 'NO'}`);

    console.log('\n3. EXPECTED RESULT:');
    console.log('-'.repeat(40));
    if (batchesFromAudit.length > 0) {
      console.log('✅ Finance dashboard SHOULD show:');
      console.log('   - "NEWLY ARRIVED VOUCHERS FROM AUDIT" notification');
      console.log(`   - ${batchesFromAudit.length} batch(es) to receive`);
      console.log('   - "RECEIVE VOUCHERS" button');
    } else {
      console.log('❌ Finance dashboard will NOT show batch notification');
      console.log('   - No unreceived batches from audit found');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n✅ Database connection closed');
    }
  }
}

testFinanceAPI();
