const mysql = require('mysql2/promise');

async function testAPIFix() {
  console.log('🧪 TESTING API FIX');
  console.log('='.repeat(40));

  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'vms@2025@1989',
      database: 'vms_production'
    });
    
    console.log('✅ Connected to database');

    // Test what the API should now return for AUDIT
    console.log('\n1. WHAT AUDIT API SHOULD NOW RETURN:');
    console.log('-'.repeat(40));
    
    const [auditBatches] = await connection.execute(`
      SELECT 
        id,
        department,
        sent_by,
        sent_time,
        received,
        from_audit
      FROM voucher_batches 
      WHERE from_audit = 0
      ORDER BY sent_time DESC
    `);

    console.log(`🏛️  AUDIT should get: ${auditBatches.length} batches`);
    if (auditBatches.length === 0) {
      console.log('   ✅ PERFECT! Audit dashboard should be EMPTY');
    } else {
      auditBatches.forEach((batch, index) => {
        console.log(`   ${index + 1}. FROM ${batch.department} (${batch.received ? 'RECEIVED' : 'UNRECEIVED'})`);
      });
    }

    // Test what the API should return for FINANCE
    console.log('\n2. WHAT FINANCE API SHOULD RETURN:');
    console.log('-'.repeat(40));
    
    const [financeBatches] = await connection.execute(`
      SELECT 
        id,
        department,
        sent_by,
        sent_time,
        received,
        from_audit
      FROM voucher_batches 
      WHERE department = 'FINANCE'
      ORDER BY sent_time DESC
    `);

    console.log(`💰 FINANCE should get: ${financeBatches.length} batches`);
    
    const financeFromAudit = financeBatches.filter(b => b.from_audit && !b.received);
    console.log(`   📥 Unreceived FROM audit: ${financeFromAudit.length} (should show notification)`);
    
    if (financeFromAudit.length > 0) {
      console.log('   ✅ Finance dashboard should show "NEWLY ARRIVED VOUCHERS FROM AUDIT"');
      financeFromAudit.forEach((batch, index) => {
        console.log(`   ${index + 1}. Batch ${batch.id.substring(0, 8)}... from ${batch.sent_by}`);
      });
    }

    // Test the fix result
    console.log('\n3. FIX VERIFICATION:');
    console.log('-'.repeat(40));
    
    const auditPendingCount = auditBatches.filter(b => !b.received).length;
    const financePendingCount = financeFromAudit.length;
    
    console.log(`🏛️  AUDIT pending batches: ${auditPendingCount}`);
    console.log(`💰 FINANCE pending batches: ${financePendingCount}`);
    
    if (auditPendingCount === 0 && financePendingCount > 0) {
      console.log('\n🎯 FIX SUCCESSFUL!');
      console.log('   ✅ Audit dashboard will be empty');
      console.log('   ✅ Finance dashboard will show batches from audit');
      console.log('   ✅ No more confusion between dashboards');
    } else {
      console.log('\n❌ Fix may not be complete');
    }

    console.log('\n4. NEXT STEPS:');
    console.log('-'.repeat(40));
    console.log('1. 🚪 Logout from current session');
    console.log('2. 🔐 Login as AUDIT user - should see EMPTY dashboard');
    console.log('3. 🔐 Login as FINANCE user - should see 6 batches from audit');
    console.log('4. 🔄 Hard refresh browser if needed');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n✅ Database connection closed');
    }
  }
}

testAPIFix();
