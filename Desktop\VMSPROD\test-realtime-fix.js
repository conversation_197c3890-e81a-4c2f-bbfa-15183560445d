const axios = require('axios');

async function testRealtimeUpdates() {
  console.log('🧪 Testing Real-time Updates Fix');
  console.log('='.repeat(50));

  const baseURL = 'http://localhost:8080';
  
  try {
    // Step 1: Login as AUDIT user
    console.log('\n1. Logging in as AUDIT user...');
    const loginResponse = await axios.post(`${baseURL}/api/auth/login`, {
      department: 'AUDIT',
      username: 'SAMUEL ASIEDU',
      password: '123'
    });

    const token = loginResponse.data.token;
    console.log('✅ Login successful');

    // Step 2: Get current vouchers
    console.log('\n2. Getting current vouchers...');
    const vouchersResponse = await axios.get(`${baseURL}/api/vouchers`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });

    const financeVouchers = vouchersResponse.data.filter(v => 
      v.originalDepartment === 'FINANCE' && 
      v.department === 'AUDIT' && 
      v.status === 'AUDIT: PROCESSING' &&
      v.receivedByAudit === true
    );

    console.log(`📊 Found ${financeVouchers.length} FINANCE vouchers in AUDIT`);

    if (financeVouchers.length === 0) {
      console.log('❌ No vouchers available for testing. Please create some vouchers first.');
      return;
    }

    // Step 3: Test workStarted field update
    const testVoucher = financeVouchers[0];
    console.log(`\n3. Testing workStarted update on voucher: ${testVoucher.voucherId}`);
    console.log(`   Current workStarted: ${testVoucher.workStarted}`);
    console.log(`   Current tab: ${testVoucher.workStarted ? 'PENDING DISPATCH' : 'NEW VOUCHERS'}`);

    // Update the voucher to set workStarted = true
    const updateResponse = await axios.put(`${baseURL}/api/vouchers/${testVoucher.id}`, {
      preAuditedAmount: 500.00,
      preAuditedBy: 'SAMUEL ASIEDU',
      workStarted: true,
      comment: 'Real-time test - work started'
    }, {
      headers: { 'Authorization': `Bearer ${token}` }
    });

    console.log('✅ Voucher updated successfully');
    console.log(`   New workStarted: ${updateResponse.data.work_started}`);
    console.log(`   Should now appear in: PENDING DISPATCH tab`);

    // Step 4: Verify the update
    console.log('\n4. Verifying update...');
    const verifyResponse = await axios.get(`${baseURL}/api/vouchers/${testVoucher.id}`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });

    console.log('📊 Verification results:');
    console.log(`   workStarted: ${verifyResponse.data.workStarted}`);
    console.log(`   preAuditedAmount: ${verifyResponse.data.preAuditedAmount}`);
    console.log(`   preAuditedBy: ${verifyResponse.data.preAuditedBy}`);
    console.log(`   comment: ${verifyResponse.data.comment}`);

    console.log('\n✅ Real-time update test completed!');
    console.log('🔄 The voucher should now appear in PENDING DISPATCH tab in real-time');
    console.log('📱 Check the frontend to verify the real-time update worked');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

testRealtimeUpdates();
