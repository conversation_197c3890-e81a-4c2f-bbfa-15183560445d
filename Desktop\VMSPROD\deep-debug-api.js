const mysql = require('mysql2/promise');

async function deepDebugAPI() {
  console.log('🔍 DEEP DEBUGGING API ENDPOINTS');
  console.log('='.repeat(50));

  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'vms@2025@1989',
      database: 'vms_production'
    });
    
    console.log('✅ Connected to database');

    // Check what the /api/batches endpoint should return
    console.log('\n1. WHAT /api/batches SHOULD RETURN:');
    console.log('-'.repeat(50));
    
    const [allBatches] = await connection.execute(`
      SELECT 
        id,
        department,
        sent_by,
        sent_time,
        received,
        from_audit,
        (SELECT COUNT(*) FROM batch_vouchers WHERE batch_id = voucher_batches.id) as voucher_count
      FROM voucher_batches 
      ORDER BY sent_time DESC
    `);

    console.log(`📦 Raw API data: ${allBatches.length} batches`);
    allBatches.forEach((batch, index) => {
      console.log(`   ${index + 1}. ID: ${batch.id.substring(0, 8)}...`);
      console.log(`      department: "${batch.department}"`);
      console.log(`      from_audit: ${batch.from_audit}`);
      console.log(`      received: ${batch.received}`);
      console.log(`      voucher_count: ${batch.voucher_count}`);
      console.log(`      sent_by: "${batch.sent_by}"`);
      console.log('      ---');
    });

    // Test the exact filtering logic used by frontend
    console.log('\n2. FRONTEND FILTERING SIMULATION:');
    console.log('-'.repeat(50));
    
    // Simulate getVoucherBatchesForDepartment for AUDIT
    console.log('🏛️  AUDIT getVoucherBatchesForDepartment:');
    const auditBatches = allBatches.filter(batch => !batch.from_audit);
    console.log(`   Should return: ${auditBatches.length} batches`);
    auditBatches.forEach((batch, index) => {
      console.log(`   ${index + 1}. FROM ${batch.department} (${batch.received ? 'RECEIVED' : 'UNRECEIVED'})`);
    });
    
    // Simulate getVoucherBatchesForDepartment for FINANCE
    console.log('\n💰 FINANCE getVoucherBatchesForDepartment:');
    const financeBatches = allBatches.filter(batch => batch.department === 'FINANCE');
    console.log(`   Should return: ${financeBatches.length} batches`);
    financeBatches.forEach((batch, index) => {
      const direction = batch.from_audit ? 'FROM AUDIT' : 'TO AUDIT';
      console.log(`   ${index + 1}. ${direction} (${batch.received ? 'RECEIVED' : 'UNRECEIVED'})`);
    });

    // Test the pendingBatches filtering for each dashboard
    console.log('\n3. PENDING BATCHES FILTERING:');
    console.log('-'.repeat(50));
    
    // Audit dashboard pendingBatches
    const auditPendingBatches = auditBatches.filter(batch => !batch.received && !batch.from_audit);
    console.log(`🏛️  AUDIT pendingBatches: ${auditPendingBatches.length}`);
    if (auditPendingBatches.length === 0) {
      console.log('   ✅ Should show NO "NEWLY ARRIVED VOUCHERS" section');
    } else {
      console.log('   ❌ Should show batches but there are none');
    }
    
    // Finance dashboard batchesArray (from useDepartmentData)
    const financeBatchesFromAudit = financeBatches.filter(batch => batch.from_audit && !batch.received);
    console.log(`💰 FINANCE batchesArray: ${financeBatchesFromAudit.length}`);
    if (financeBatchesFromAudit.length > 0) {
      console.log('   ✅ Should show "NEWLY ARRIVED VOUCHERS FROM AUDIT" notification');
      financeBatchesFromAudit.forEach((batch, index) => {
        console.log(`   ${index + 1}. Batch ${batch.id.substring(0, 8)}... (${batch.voucher_count} vouchers)`);
      });
    }

    // Check for any data inconsistencies
    console.log('\n4. DATA CONSISTENCY CHECK:');
    console.log('-'.repeat(50));
    
    // Check if there are any batches with invalid data
    const invalidBatches = allBatches.filter(batch => 
      !batch.department || 
      batch.from_audit === null || 
      batch.received === null
    );
    
    if (invalidBatches.length > 0) {
      console.log('❌ FOUND INVALID BATCHES:');
      invalidBatches.forEach(batch => {
        console.log(`   - ${batch.id}: department=${batch.department}, from_audit=${batch.from_audit}, received=${batch.received}`);
      });
    } else {
      console.log('✅ All batches have valid data');
    }

    // Check if the issue is with the API response format
    console.log('\n5. API RESPONSE FORMAT CHECK:');
    console.log('-'.repeat(50));
    
    // Simulate what the API should return for each department
    const auditAPIResponse = auditBatches.map(batch => ({
      id: batch.id,
      department: batch.department,
      sentBy: batch.sent_by,
      sentTime: batch.sent_time,
      received: batch.received,
      fromAudit: batch.from_audit,
      voucherCount: batch.voucher_count
    }));
    
    const financeAPIResponse = financeBatches.map(batch => ({
      id: batch.id,
      department: batch.department,
      sentBy: batch.sent_by,
      sentTime: batch.sent_time,
      received: batch.received,
      fromAudit: batch.from_audit,
      voucherCount: batch.voucher_count
    }));
    
    console.log(`🏛️  AUDIT API should return: ${auditAPIResponse.length} batches`);
    console.log(`💰 FINANCE API should return: ${financeAPIResponse.length} batches`);

    // Final diagnosis
    console.log('\n6. FINAL DIAGNOSIS:');
    console.log('-'.repeat(50));
    
    if (auditPendingBatches.length === 0 && financeBatchesFromAudit.length > 0) {
      console.log('🎯 EXPECTED BEHAVIOR:');
      console.log('   - Audit dashboard: EMPTY (no notification)');
      console.log('   - Finance dashboard: Show 6 batches from audit');
      console.log('');
      console.log('🚨 IF AUDIT DASHBOARD IS SHOWING BATCHES:');
      console.log('   - There is a bug in the frontend filtering logic');
      console.log('   - OR the API is returning wrong data');
      console.log('   - OR there is a caching issue in the browser/server');
    }

  } catch (error) {
    console.error('❌ Debug failed:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n✅ Database connection closed');
    }
  }
}

deepDebugAPI();
