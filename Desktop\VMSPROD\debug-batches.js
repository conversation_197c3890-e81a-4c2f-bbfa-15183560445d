/**
 * Debug Batches Script
 * Check current state of vouchers and batches to identify duplication issue
 */

const mysql = require('mysql2/promise');

// Database configuration
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: 'vms@2025@1989',
  database: 'vms_production'
};

async function debugBatches() {
  let connection;
  
  try {
    console.log('🔄 Connecting to database...');
    connection = await mysql.createConnection(dbConfig);
    
    console.log('✅ Connected to database');
    
    // Check current vouchers
    console.log('\n📄 CURRENT VOUCHERS:');
    const [vouchers] = await connection.execute(`
      SELECT id, voucher_id, department, original_department, status, 
             sent_to_audit, created_by, created_at
      FROM vouchers 
      ORDER BY created_at DESC
    `);
    
    if (vouchers.length === 0) {
      console.log('   No vouchers found');
    } else {
      vouchers.forEach((v, index) => {
        console.log(`   ${index + 1}. ${v.voucher_id} | ${v.department} | ${v.status} | SentToAudit: ${v.sent_to_audit} | By: ${v.created_by}`);
      });
    }
    
    // Check current batches
    console.log('\n📦 CURRENT VOUCHER BATCHES:');
    const [batches] = await connection.execute(`
      SELECT id, department, sent_by, sent_time, received, from_audit
      FROM voucher_batches 
      ORDER BY sent_time DESC
    `);
    
    if (batches.length === 0) {
      console.log('   No batches found');
    } else {
      batches.forEach((b, index) => {
        console.log(`   ${index + 1}. ${b.id.substring(0, 8)}... | To: ${b.department} | From: ${b.sent_by} | FromAudit: ${b.from_audit} | Received: ${b.received}`);
      });
    }
    
    // Check batch-voucher relationships
    console.log('\n🔗 BATCH-VOUCHER RELATIONSHIPS:');
    const [relationships] = await connection.execute(`
      SELECT bv.batch_id, bv.voucher_id, v.voucher_id as voucher_number, vb.department, vb.from_audit
      FROM batch_vouchers bv
      JOIN vouchers v ON bv.voucher_id = v.id
      JOIN voucher_batches vb ON bv.batch_id = vb.id
      ORDER BY vb.sent_time DESC
    `);
    
    if (relationships.length === 0) {
      console.log('   No batch-voucher relationships found');
    } else {
      relationships.forEach((r, index) => {
        console.log(`   ${index + 1}. Batch: ${r.batch_id.substring(0, 8)}... | Voucher: ${r.voucher_number} | To: ${r.department} | FromAudit: ${r.from_audit}`);
      });
    }
    
    // Check for duplicates
    console.log('\n🔍 CHECKING FOR DUPLICATES:');
    
    // Check if same voucher appears in multiple batches
    const [duplicateVouchers] = await connection.execute(`
      SELECT voucher_id, COUNT(*) as batch_count, GROUP_CONCAT(batch_id) as batch_ids
      FROM batch_vouchers
      GROUP BY voucher_id
      HAVING COUNT(*) > 1
    `);
    
    if (duplicateVouchers.length === 0) {
      console.log('   ✅ No vouchers appear in multiple batches');
    } else {
      console.log('   ❌ DUPLICATE VOUCHERS FOUND:');
      duplicateVouchers.forEach((d, index) => {
        console.log(`      ${index + 1}. Voucher ${d.voucher_id} appears in ${d.batch_count} batches: ${d.batch_ids}`);
      });
    }
    
    // Check if same batch has duplicate vouchers
    const [duplicateBatches] = await connection.execute(`
      SELECT batch_id, voucher_id, COUNT(*) as occurrence_count
      FROM batch_vouchers
      GROUP BY batch_id, voucher_id
      HAVING COUNT(*) > 1
    `);
    
    if (duplicateBatches.length === 0) {
      console.log('   ✅ No duplicate vouchers within same batch');
    } else {
      console.log('   ❌ DUPLICATE VOUCHERS IN SAME BATCH:');
      duplicateBatches.forEach((d, index) => {
        console.log(`      ${index + 1}. Batch ${d.batch_id.substring(0, 8)}... has voucher ${d.voucher_id} ${d.occurrence_count} times`);
      });
    }
    
    console.log('\n🎯 DEBUG COMPLETED!');
    
  } catch (error) {
    console.error('❌ Error debugging batches:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the debug
debugBatches();
