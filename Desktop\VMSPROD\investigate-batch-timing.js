const mysql = require('mysql2/promise');

async function investigateBatchTiming() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root', 
    password: 'vms@2025@1989',
    database: 'vms_production'
  });
  
  console.log('🔍 INVESTIGATING BATCH CREATION TIMING');
  console.log('=====================================');
  
  // Get detailed batch information with exact timing
  const [batches] = await connection.execute(`
    SELECT 
      vb.id,
      vb.department,
      vb.sent_by,
      vb.sent_time,
      vb.received,
      vb.from_audit,
      COUNT(bv.voucher_id) as voucher_count,
      GROUP_CONCAT(v.voucher_id ORDER BY v.voucher_id) as voucher_ids
    FROM voucher_batches vb
    LEFT JOIN batch_vouchers bv ON vb.id = bv.batch_id
    LEFT JOIN vouchers v ON bv.voucher_id = v.id
    WHERE vb.department = 'FINANCE' 
    AND vb.from_audit = TRUE
    GROUP BY vb.id
    ORDER BY vb.sent_time DESC
  `);
  
  console.log(`📊 Found ${batches.length} FINANCE batches from audit:`);
  console.log('');
  
  for (let i = 0; i < batches.length; i++) {
    const batch = batches[i];
    const sentTime = new Date(batch.sent_time);
    console.log(`${i + 1}. BATCH ${batch.id}`);
    console.log(`   📅 Sent Time: ${sentTime.toISOString()}`);
    console.log(`   📅 Local Time: ${sentTime.toLocaleString()}`);
    console.log(`   👤 Sent By: ${batch.sent_by}`);
    console.log(`   📋 Voucher Count: ${batch.voucher_count}`);
    console.log(`   📝 Voucher IDs: ${batch.voucher_ids}`);
    console.log(`   ✅ Received: ${batch.received ? 'YES' : 'NO'}`);
    console.log('');
  }
  
  // Check for suspicious timing patterns (batches created very close together)
  if (batches.length >= 2) {
    console.log('🕐 TIMING ANALYSIS:');
    console.log('-------------------');
    
    for (let i = 0; i < batches.length - 1; i++) {
      const batch1 = batches[i];
      const batch2 = batches[i + 1];
      
      const time1 = new Date(batch1.sent_time);
      const time2 = new Date(batch2.sent_time);
      const timeDiff = Math.abs(time1 - time2) / 1000; // seconds
      
      console.log(`⏱️ Time between Batch ${i + 1} and Batch ${i + 2}: ${timeDiff} seconds`);
      
      if (timeDiff < 60) { // Less than 1 minute apart
        console.log(`   ⚠️ SUSPICIOUS: Batches created within ${timeDiff} seconds of each other`);
        console.log(`   🔍 This could indicate duplicate batch creation from same operation`);
        
        // Check if they have the same vouchers
        const vouchers1 = batch1.voucher_ids ? batch1.voucher_ids.split(',') : [];
        const vouchers2 = batch2.voucher_ids ? batch2.voucher_ids.split(',') : [];
        
        const commonVouchers = vouchers1.filter(v => vouchers2.includes(v));
        if (commonVouchers.length > 0) {
          console.log(`   ❌ DUPLICATE CONFIRMED: ${commonVouchers.length} common vouchers found`);
          console.log(`   📋 Common vouchers: ${commonVouchers.join(', ')}`);
        } else {
          console.log(`   ✅ DIFFERENT VOUCHERS: No common vouchers, likely legitimate separate operations`);
        }
      } else {
        console.log(`   ✅ NORMAL: Sufficient time gap between batches`);
      }
      console.log('');
    }
  }
  
  // Check audit dispatch history for the vouchers in these batches
  console.log('🔍 AUDIT DISPATCH HISTORY:');
  console.log('--------------------------');
  
  const [auditHistory] = await connection.execute(`
    SELECT 
      v.voucher_id,
      v.audit_dispatch_time,
      v.audit_dispatched_by,
      v.batch_id,
      v.department_receipt_time,
      v.department_received_by
    FROM vouchers v
    WHERE v.id IN (
      SELECT bv.voucher_id 
      FROM batch_vouchers bv 
      JOIN voucher_batches vb ON bv.batch_id = vb.id 
      WHERE vb.department = 'FINANCE' AND vb.from_audit = TRUE
    )
    ORDER BY v.audit_dispatch_time DESC
  `);
  
  console.log('📋 Voucher dispatch history:');
  auditHistory.forEach((record, idx) => {
    console.log(`   ${idx + 1}. ${record.voucher_id}:`);
    console.log(`      🚀 Dispatched: ${record.audit_dispatch_time} by ${record.audit_dispatched_by}`);
    console.log(`      📦 Batch ID: ${record.batch_id}`);
    console.log(`      📥 Received: ${record.department_receipt_time || 'NOT YET'} by ${record.department_received_by || 'N/A'}`);
  });
  
  await connection.end();
}

investigateBatchTiming().catch(console.error);
