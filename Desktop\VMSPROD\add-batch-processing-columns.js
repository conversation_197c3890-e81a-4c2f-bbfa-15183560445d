/**
 * ADD MISSING BATCH PROCESSING COLUMNS
 * 
 * This script adds the missing batch_processed and batch_processed_time columns
 * that are causing the voucher update to fail during dispatch.
 */

import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: './server/.env' });

const connection = await mysql.createConnection({
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'vms@2025@1989',
  database: process.env.DB_NAME || 'vms_production'
});

console.log('🔧 ADDING MISSING BATCH PROCESSING COLUMNS');
console.log('==========================================');

try {
  // Check if columns exist
  console.log('1. Checking existing columns...');
  const [columns] = await connection.execute(`
    SELECT COLUMN_NAME 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'vouchers'
    AND COLUMN_NAME IN ('batch_processed', 'batch_processed_time')
  `, [process.env.DB_NAME || 'vms_production']);

  console.log(`Found ${columns.length} existing batch processing columns`);
  
  const existingColumns = columns.map(col => col.COLUMN_NAME);
  
  // Add batch_processed column if missing
  if (!existingColumns.includes('batch_processed')) {
    console.log('2. Adding batch_processed column...');
    await connection.execute(`
      ALTER TABLE vouchers 
      ADD COLUMN batch_processed BOOLEAN DEFAULT FALSE
    `);
    console.log('✅ Added batch_processed column');
  } else {
    console.log('✅ batch_processed column already exists');
  }

  // Add batch_processed_time column if missing
  if (!existingColumns.includes('batch_processed_time')) {
    console.log('3. Adding batch_processed_time column...');
    await connection.execute(`
      ALTER TABLE vouchers 
      ADD COLUMN batch_processed_time TIMESTAMP NULL DEFAULT NULL
    `);
    console.log('✅ Added batch_processed_time column');
  } else {
    console.log('✅ batch_processed_time column already exists');
  }

  // Verify the columns were added
  console.log('\n4. Verifying columns...');
  const [finalColumns] = await connection.execute(`
    SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'vouchers'
    AND COLUMN_NAME IN ('batch_processed', 'batch_processed_time')
  `, [process.env.DB_NAME || 'vms_production']);

  console.log('📊 Final column status:');
  console.table(finalColumns);

  if (finalColumns.length === 2) {
    console.log('\n🎉 SUCCESS! Both batch processing columns are now available');
    console.log('✅ batch_processed: BOOLEAN DEFAULT FALSE');
    console.log('✅ batch_processed_time: TIMESTAMP NULL DEFAULT NULL');
    console.log('✅ Voucher dispatch should now work correctly');
  } else {
    console.log('\n❌ ERROR: Not all columns were added successfully');
  }

} catch (error) {
  console.error('❌ Failed to add batch processing columns:', error);
} finally {
  await connection.end();
  console.log('\n🔌 Database connection closed');
}
