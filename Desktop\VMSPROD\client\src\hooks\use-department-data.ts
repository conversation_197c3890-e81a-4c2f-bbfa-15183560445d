
import { useAppStore } from '@/lib/store';
import { Department, Voucher } from '@/lib/types';
import { useEffect, useState } from 'react';
import { VOUCHER_STATUSES } from '@/lib/constants/voucher-statuses';

export function useDepartmentData(department?: Department, refreshTrigger: number = 0) {
  // ARCHITECTURAL FIX: Direct store subscription for real-time updates
  const allVouchers = useAppStore((state) => state.vouchers);
  const voucherBatches = useAppStore((state) => state.voucherBatches);
  const getVouchersForDepartment = useAppStore((state) => state.getVouchersForDepartment);
  const getPendingVouchersForDepartment = useAppStore((state) => state.getPendingVouchersForDepartment);

  // PRODUCTION FIX: Add store version for change detection
  const storeVersion = useAppStore((state) => state.version || 0);
  const lastUpdate = useAppStore((state) => state.lastUpdate || 0);
  const forceUpdate = useAppStore((state) => state.forceUpdate || 0);

  // FORCE RE-RENDER: Local state to trigger component updates
  const [renderTrigger, setRenderTrigger] = useState(0);

  const [data, setData] = useState({
    vouchers: [] as Voucher[],
    pendingVouchers: [] as Voucher[],
    pendingSubmissionVouchers: [] as Voucher[],
    processingVouchers: [] as Voucher[],
    certifiedVouchers: [] as Voucher[],
    rejectedVouchers: [] as Voucher[],
    returnedVouchers: [] as Voucher[],
    vouchersToReceive: [] as Voucher[],
    departmentBatches: [] as any[],
    batchesArray: [] as any[]
  });

  // REMOVED: Duplicate declarations - already defined above

  // ARCHITECTURAL FIX: React to store changes in real-time
  useEffect(() => {
    if (!department) {
      return;
    }



    // FORCE RE-RENDER: Trigger local state update
    setRenderTrigger(prev => prev + 1);

    // SIMPLE APPROACH: Get all vouchers that belong to this department (current or original)
    const allRelevantVouchers = allVouchers.filter(v =>
      v.department === department || v.originalDepartment === department
    );



    // SIMPLE FILTERING: Clear business rules

    // PENDING: Vouchers still in this department that haven't been sent to audit
    const pendingVouchers = allRelevantVouchers.filter(v =>
      v.department === department && !v.sentToAudit
    );

    // For backward compatibility
    const pendingSubmissionVouchers = pendingVouchers;

    // PROCESSING: Vouchers that originated from this department and were sent to audit
    const processingVouchers = allRelevantVouchers.filter(v =>
      v.originalDepartment === department && v.sentToAudit === true
    );



    // CERTIFIED: Vouchers that were certified by audit and returned to department
    const certifiedVouchers = allRelevantVouchers.filter(v =>
      v.originalDepartment === department && v.status === VOUCHER_STATUSES.VOUCHER_CERTIFIED
    );

    // REJECTED: Vouchers that were rejected by audit and returned to department
    const rejectedVouchers = allRelevantVouchers.filter(v =>
      v.originalDepartment === department && v.status === VOUCHER_STATUSES.VOUCHER_REJECTED
    );

    // RETURNED: Vouchers that were returned by audit for rework
    const returnedVouchers = allRelevantVouchers.filter(v =>
      v.originalDepartment === department && v.status === VOUCHER_STATUSES.VOUCHER_RETURNED
    );

    // Get batches for the current department
    const departmentBatches = voucherBatches.filter(batch =>
      batch.department === department
    );


    setData({
      vouchers: allRelevantVouchers,
      pendingVouchers,
      pendingSubmissionVouchers,
      processingVouchers,
      certifiedVouchers,
      rejectedVouchers,
      returnedVouchers,
      vouchersToReceive: [], // Simplified - remove complex logic
      departmentBatches,
      batchesArray: [] // Simplified - remove complex logic
    });

  }, [department, allVouchers, voucherBatches]);

  return data;
}
