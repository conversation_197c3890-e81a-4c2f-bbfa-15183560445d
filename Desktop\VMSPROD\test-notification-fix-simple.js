const mysql = require('mysql2/promise');

async function testNotificationFixSimple() {
  console.log('🧪 TESTING DUPLICATE NOTIFICATION FIX (Database Analysis)');
  console.log('=' .repeat(60));

  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });

  try {
    // Check recent notifications to see if duplicates exist
    console.log('📊 Analyzing recent notifications...');
    
    const [notifications] = await connection.execute(`
      SELECT
        user_id,
        message,
        batch_id,
        type,
        from_audit,
        MAX(timestamp) as latest_timestamp,
        COUNT(*) as count
      FROM notifications
      WHERE type = 'NEW_BATCH'
        AND timestamp > DATE_SUB(NOW(), INTERVAL 1 HOUR)
      GROUP BY user_id, message, batch_id, type, from_audit
      HAVING COUNT(*) > 1
      ORDER BY latest_timestamp DESC
    `);

    console.log(`🔍 Found ${notifications.length} duplicate notification groups in the last hour:`);
    
    if (notifications.length === 0) {
      console.log('✅ NO DUPLICATE NOTIFICATIONS FOUND - Fix appears to be working!');
    } else {
      console.log('❌ DUPLICATE NOTIFICATIONS DETECTED:');
      notifications.forEach((notif, index) => {
        console.log(`   ${index + 1}. ${notif.message}`);
        console.log(`      User: ${notif.user_id}, Batch: ${notif.batch_id}`);
        console.log(`      Count: ${notif.count} duplicates`);
        console.log(`      Time: ${notif.latest_timestamp}`);
        console.log('');
      });
    }

    // Check all recent notifications for Finance department
    console.log('\n📋 Recent FINANCE notifications:');
    const [financeNotifs] = await connection.execute(`
      SELECT message, batch_id, from_audit, timestamp
      FROM notifications 
      WHERE user_id = 'FINANCE' 
        AND type = 'NEW_BATCH'
        AND timestamp > DATE_SUB(NOW(), INTERVAL 2 HOUR)
      ORDER BY timestamp DESC
      LIMIT 10
    `);

    financeNotifs.forEach((notif, index) => {
      console.log(`   ${index + 1}. ${notif.message} (${notif.timestamp})`);
      console.log(`      Batch: ${notif.batch_id}, From Audit: ${notif.from_audit}`);
    });

    // Check recent batches created
    console.log('\n📦 Recent batches created:');
    const [batches] = await connection.execute(`
      SELECT id, department, sent_by, sent_time, from_audit
      FROM voucher_batches 
      WHERE sent_time > DATE_SUB(NOW(), INTERVAL 2 HOUR)
      ORDER BY sent_time DESC
      LIMIT 10
    `);

    batches.forEach((batch, index) => {
      console.log(`   ${index + 1}. ${batch.id}`);
      console.log(`      To: ${batch.department}, By: ${batch.sent_by}`);
      console.log(`      Time: ${batch.sent_time}, From Audit: ${batch.from_audit}`);
    });

    // Summary analysis
    console.log('\n' + '=' .repeat(60));
    console.log('📊 ANALYSIS SUMMARY:');
    console.log(`   • Duplicate notification groups found: ${notifications.length}`);
    console.log(`   • Recent Finance notifications: ${financeNotifs.length}`);
    console.log(`   • Recent batches created: ${batches.length}`);
    
    if (notifications.length === 0) {
      console.log('   • Status: ✅ NO DUPLICATES DETECTED');
      console.log('   • Result: 🎉 COMPREHENSIVE FIX APPEARS SUCCESSFUL');
    } else {
      console.log('   • Status: ❌ DUPLICATES STILL PRESENT');
      console.log('   • Result: 🚨 ADDITIONAL INVESTIGATION NEEDED');
    }

    // Check if the fix is in place by examining the code structure
    console.log('\n🔍 Checking if fix is properly implemented...');
    
    // This is a simple check - in a real scenario, we'd examine the actual code
    console.log('   • Database analysis shows current state');
    console.log('   • Code changes should eliminate event bus notification emission');
    console.log('   • Only database notifications should be created');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    await connection.end();
  }
}

testNotificationFixSimple().catch(console.error);
