
import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { VoucherBatch } from '@/lib/types';
import { formatVMSDateTime } from '@/utils/formatUtils';

interface PendingBatchesProps {
  batches: VoucherBatch[];
  onOpenBatch: (batchId: string) => void;
}

export function PendingBatches({ batches, onOpenBatch }: PendingBatchesProps) {
  // Strictly filter batches to only show those FROM audit that have not been received
  const incomingBatches = batches.filter(batch => 
    batch.fromAudit === true && 
    batch.received === false
  );
  
  if (incomingBatches.length === 0) return null;
  
  return (
    <div className="space-y-4 mb-6 bg-black text-white">
      <h2 className="text-xl font-semibold uppercase text-white">PENDING BATCHES</h2>
      <Card className="bg-[#0a0a0a] border-gray-800">
        <CardHeader className="pb-3">
          <CardTitle className="uppercase text-white">NEW VOUCHER BATCHES</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {incomingBatches.map((batch) => (
            <Card key={batch.id} className="border border-gray-800 bg-[#0f0f0f]">
              <CardContent className="p-4">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="font-medium uppercase text-white">BATCH FROM AUDIT</h3>
                    <p className="text-sm text-gray-400 uppercase">
                      SENT BY: {batch.sentBy} ON {formatVMSDateTime(batch.sentTime)}
                    </p>
                    <p className="text-sm font-medium mt-1 text-white">
                      {batch.voucherIds.length} VOUCHER(S)
                    </p>
                  </div>
                  <Button 
                    onClick={() => onOpenBatch(batch.id)}
                    className="uppercase bg-blue-600 hover:bg-blue-700 text-white"
                  >
                    PROCESS BATCH
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </CardContent>
      </Card>
    </div>
  );
}
