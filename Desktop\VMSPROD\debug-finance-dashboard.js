const mysql = require('mysql2/promise');

async function debugFinanceDashboard() {
  console.log('🔍 DEBUGGING FINANCE DASHBOARD DATA FLOW');
  console.log('='.repeat(60));

  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'vms@2025@1989',
      database: 'vms_production'
    });
    
    console.log('✅ Connected to database');

    // 1. Check raw batch data
    console.log('\n1. RAW BATCH DATA:');
    console.log('-'.repeat(60));
    
    const [allBatches] = await connection.execute(`
      SELECT 
        id,
        department,
        sent_by,
        sent_time,
        received,
        from_audit,
        (SELECT COUNT(*) FROM batch_vouchers WHERE batch_id = voucher_batches.id) as voucher_count
      FROM voucher_batches 
      ORDER BY sent_time DESC
    `);

    console.log(`📦 Total batches in database: ${allBatches.length}`);
    allBatches.forEach((batch, index) => {
      console.log(`   ${index + 1}. ${batch.id.substring(0, 8)}...`);
      console.log(`      department: "${batch.department}"`);
      console.log(`      from_audit: ${batch.from_audit} (${batch.from_audit ? 'AUDIT→DEPT' : 'DEPT→AUDIT'})`);
      console.log(`      received: ${batch.received} (${batch.received ? 'YES' : 'NO'})`);
      console.log(`      vouchers: ${batch.voucher_count}`);
      console.log(`      sent_by: "${batch.sent_by}"`);
      console.log('      ---');
    });

    // 2. Simulate Finance API call
    console.log('\n2. FINANCE API SIMULATION:');
    console.log('-'.repeat(60));
    
    const financeBatches = allBatches.filter(batch => batch.department === 'FINANCE');
    console.log(`💰 Finance should get ${financeBatches.length} batches from API`);
    
    financeBatches.forEach((batch, index) => {
      const direction = batch.from_audit ? 'FROM AUDIT' : 'TO AUDIT';
      const status = batch.received ? 'RECEIVED' : 'UNRECEIVED';
      console.log(`   ${index + 1}. ${direction} - ${status} (${batch.voucher_count} vouchers)`);
    });

    // 3. Check useDepartmentData filtering
    console.log('\n3. useDepartmentData FILTERING SIMULATION:');
    console.log('-'.repeat(60));
    
    // This simulates the batchesFromAudit filtering in useDepartmentData
    const batchesFromAudit = financeBatches.filter(batch => 
      batch.from_audit === 1 && batch.received === 0
    );
    
    console.log(`📋 batchesFromAudit should contain: ${batchesFromAudit.length} batches`);
    if (batchesFromAudit.length > 0) {
      console.log('   ✅ These should appear in Finance dashboard:');
      batchesFromAudit.forEach((batch, index) => {
        console.log(`   ${index + 1}. Batch ${batch.id.substring(0, 8)}... (${batch.voucher_count} vouchers)`);
      });
    } else {
      console.log('   ❌ NO batches to show - this is the problem!');
    }

    // 4. Check VoucherBatchNotification logic
    console.log('\n4. VoucherBatchNotification LOGIC:');
    console.log('-'.repeat(60));
    
    // Get vouchers for the batches to simulate the notification logic
    for (const batch of batchesFromAudit) {
      const [vouchers] = await connection.execute(`
        SELECT v.*, bv.batch_id
        FROM vouchers v
        JOIN batch_vouchers bv ON v.id = bv.voucher_id
        WHERE bv.batch_id = ?
      `, [batch.id]);
      
      console.log(`📋 Batch ${batch.id.substring(0, 8)}... contains ${vouchers.length} vouchers:`);
      vouchers.forEach((voucher, index) => {
        console.log(`   ${index + 1}. ${voucher.voucher_number} - Status: ${voucher.status}`);
        console.log(`      certifiedBy: ${voucher.certified_by || 'null'}`);
        console.log(`      auditDispatchedBy: ${voucher.audit_dispatched_by || 'null'}`);
        console.log(`      departmentReceiptTime: ${voucher.department_receipt_time || 'null'}`);
      });
      
      // Check if this batch would trigger the notification
      const hasProcessedVouchers = vouchers.some(v => {
        const isProcessedVoucher = (
          v.certified_by ||
          v.status === "VOUCHER REJECTED" ||
          v.status === "VOUCHER PROCESSING" ||
          v.is_returned ||
          v.pending_return ||
          (v.dispatched && v.audit_dispatched_by)
        );
        const notYetReceived = !v.department_receipt_time;
        return isProcessedVoucher && notYetReceived;
      });
      
      console.log(`   🔔 Should trigger notification: ${hasProcessedVouchers ? 'YES' : 'NO'}`);
    }

    // 5. Check for potential issues
    console.log('\n5. POTENTIAL ISSUES CHECK:');
    console.log('-'.repeat(60));
    
    if (batchesFromAudit.length === 0) {
      console.log('❌ ISSUE: No batches from audit found for Finance');
      console.log('   Possible causes:');
      console.log('   1. All batches are marked as received');
      console.log('   2. Batches have wrong from_audit flag');
      console.log('   3. Batches have wrong department');
      
      // Check if there are any unreceived batches at all
      const unreceived = allBatches.filter(b => !b.received);
      console.log(`   📊 Total unreceived batches: ${unreceived.length}`);
      
      const financeUnreceived = unreceived.filter(b => b.department === 'FINANCE');
      console.log(`   📊 Finance unreceived batches: ${financeUnreceived.length}`);
      
      const auditToFinance = financeUnreceived.filter(b => b.from_audit);
      console.log(`   📊 Audit→Finance unreceived: ${auditToFinance.length}`);
    }

    // 6. Final diagnosis
    console.log('\n6. FINAL DIAGNOSIS:');
    console.log('-'.repeat(60));
    
    if (batchesFromAudit.length > 0) {
      console.log('✅ Data looks correct - issue might be in frontend');
      console.log('   Check browser console for errors');
      console.log('   Try hard refresh (Ctrl+F5)');
      console.log('   Check if useDepartmentData hook is working');
    } else {
      console.log('❌ No batches found - data issue');
      console.log('   Need to check why batches are not showing up');
    }

  } catch (error) {
    console.error('❌ Debug failed:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n✅ Database connection closed');
    }
  }
}

debugFinanceDashboard();
