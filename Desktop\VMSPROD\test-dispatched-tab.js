const mysql = require('mysql2/promise');

async function testDispatchedTab() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root', 
    password: 'vms@2025@1989',
    database: 'vms_production'
  });
  
  console.log('🔍 Testing DISPATCHED tab filtering logic...');
  
  const [vouchers] = await connection.execute('SELECT voucher_id, department, original_department, status, dispatched, audit_dispatched_by FROM vouchers WHERE id = ?', ['d34ca623-d30c-4fd9-97e3-90ca0193bf84']);
  const voucher = vouchers[0];
  
  console.log('📄 Current voucher state:');
  console.log('   Voucher ID:', voucher.voucher_id);
  console.log('   Department:', voucher.department);
  console.log('   Original Department:', voucher.original_department);
  console.log('   Status:', voucher.status);
  console.log('   Dispatched:', voucher.dispatched);
  console.log('   Audit Dispatched By:', voucher.audit_dispatched_by);
  
  console.log('\n🧪 DISPATCHED tab filtering analysis:');
  
  // Test the new audit filtering logic
  const isAudit = true;
  const department = 'FINANCE'; // When viewing FINANCE VOUCHER HUB in audit dashboard
  
  const shouldAppearInAuditDispatchedTab = 
    voucher.audit_dispatched_by && 
    voucher.audit_dispatched_by.trim() !== '' && 
    voucher.original_department === department;
  
  console.log('   NEW AUDIT DISPATCHED tab filter:');
  console.log('     audit_dispatched_by exists:', voucher.audit_dispatched_by ? 'YES' : 'NO');
  console.log('     audit_dispatched_by not empty:', voucher.audit_dispatched_by && voucher.audit_dispatched_by.trim() !== '' ? 'YES' : 'NO');
  console.log('     original_department === FINANCE:', voucher.original_department === department ? 'YES' : 'NO');
  console.log('     Should appear in AUDIT DISPATCHED tab:', shouldAppearInAuditDispatchedTab ? '✅ YES' : '❌ NO');
  
  // Test the department filtering logic
  const shouldAppearInDeptDispatchedTab = 
    voucher.original_department === department && voucher.dispatched === 1;
  
  console.log('\n   DEPARTMENT DISPATCHED tab filter:');
  console.log('     original_department === FINANCE:', voucher.original_department === department ? 'YES' : 'NO');
  console.log('     dispatched === true:', voucher.dispatched === 1 ? 'YES' : 'NO');
  console.log('     Should appear in DEPT DISPATCHED tab:', shouldAppearInDeptDispatchedTab ? '✅ YES' : '❌ NO');
  
  await connection.end();
}

testDispatchedTab().catch(console.error);
