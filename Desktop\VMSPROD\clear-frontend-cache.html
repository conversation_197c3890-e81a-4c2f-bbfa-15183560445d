<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VMS Frontend Cache Cleaner</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 500px;
            width: 100%;
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 24px;
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: background 0.3s;
        }
        button:hover {
            background: #5a6fd8;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            text-align: left;
            font-family: monospace;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 VMS Frontend Cache Cleaner</h1>
        <p>This tool will clear all frontend cache and storage for a fresh start.</p>
        
        <div id="status" class="status info">
            Ready to clean frontend cache...
        </div>
        
        <button onclick="clearAllCache()">🗑️ Clear All Frontend Cache</button>
        <button onclick="checkCacheStatus()">🔍 Check Cache Status</button>
        
        <div id="log" class="log" style="display: none;"></div>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            logDiv.style.display = 'block';
            logDiv.innerHTML += new Date().toLocaleTimeString() + ': ' + message + '<br>';
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = 'status ' + type;
        }

        async function clearAllCache() {
            log('🧹 Starting frontend cache cleanup...');
            updateStatus('Clearing frontend cache...', 'info');
            
            try {
                // Clear localStorage
                const localStorageKeys = Object.keys(localStorage);
                localStorage.clear();
                log(`✅ Cleared localStorage (${localStorageKeys.length} items)`);
                
                // Clear sessionStorage
                const sessionStorageKeys = Object.keys(sessionStorage);
                sessionStorage.clear();
                log(`✅ Cleared sessionStorage (${sessionStorageKeys.length} items)`);
                
                // Clear IndexedDB
                if ('indexedDB' in window) {
                    try {
                        const databases = await indexedDB.databases();
                        for (const db of databases) {
                            if (db.name) {
                                indexedDB.deleteDatabase(db.name);
                                log(`✅ Cleared IndexedDB: ${db.name}`);
                            }
                        }
                    } catch (e) {
                        log(`⚠️ Could not clear IndexedDB: ${e.message}`);
                    }
                }
                
                // Clear cookies for current domain
                const cookies = document.cookie.split(";");
                for (let cookie of cookies) {
                    const eqPos = cookie.indexOf("=");
                    const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
                    document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/";
                }
                log(`✅ Cleared cookies (${cookies.length} items)`);
                
                // Clear cache if available
                if ('caches' in window) {
                    try {
                        const cacheNames = await caches.keys();
                        for (const cacheName of cacheNames) {
                            await caches.delete(cacheName);
                            log(`✅ Cleared cache: ${cacheName}`);
                        }
                    } catch (e) {
                        log(`⚠️ Could not clear caches: ${e.message}`);
                    }
                }
                
                log('🎉 Frontend cache cleanup completed!');
                updateStatus('✅ Frontend cache cleared successfully! Ready for fresh start.', 'success');
                
            } catch (error) {
                log(`❌ Error during cleanup: ${error.message}`);
                updateStatus('❌ Error clearing cache. Check console for details.', 'warning');
            }
        }

        function checkCacheStatus() {
            log('🔍 Checking cache status...');
            
            const localStorageCount = Object.keys(localStorage).length;
            const sessionStorageCount = Object.keys(sessionStorage).length;
            const cookieCount = document.cookie.split(";").filter(c => c.trim()).length;
            
            log(`📊 localStorage items: ${localStorageCount}`);
            log(`📊 sessionStorage items: ${sessionStorageCount}`);
            log(`📊 Cookies: ${cookieCount}`);
            
            if (localStorageCount === 0 && sessionStorageCount === 0 && cookieCount === 0) {
                updateStatus('✅ Frontend cache is clean!', 'success');
                log('✅ Frontend cache is completely clean');
            } else {
                updateStatus('⚠️ Frontend cache contains data', 'warning');
                log('⚠️ Frontend cache still contains data');
            }
        }

        // Auto-check status on load
        window.onload = function() {
            checkCacheStatus();
        };
    </script>
</body>
</html>
