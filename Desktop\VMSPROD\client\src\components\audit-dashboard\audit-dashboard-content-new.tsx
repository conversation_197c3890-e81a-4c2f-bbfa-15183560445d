import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { AlertTriangle } from 'lucide-react';
import { useAppStore } from '@/lib/store';
import { Department } from '@/lib/types';
import { NewlyArrivedVouchers } from './newly-arrived-vouchers';
import { DepartmentVoucherHubs } from './department-voucher-hubs';
import { DepartmentVoucherHub } from '@/components/department-voucher-hub';
import { DepartmentProvisionalCash } from '@/components/department-provisional-cash';
import { VoucherBatchReceiving } from '@/components/voucher-batch-receiving';
import { toast } from '@/hooks/use-toast';
import { DispatchControls } from './dispatch-controls';
import { AuditVoucherBatchNotification } from './audit-voucher-batch-notification-new';

export function AuditDashboardContent() {
  const navigate = useNavigate();
  const currentUser = useAppStore((state) => state.currentUser);
  const voucherBatches = useAppStore((state) => state.voucherBatches);
  const vouchers = useAppStore((state) => state.vouchers);
  const updateVoucher = useAppStore((state) => state.updateVoucher);
  const sendVouchersFromAuditToDepartment = useAppStore((state) => state.sendVouchersFromAuditToDepartment);
  const fetchBatches = useAppStore((state) => state.fetchBatches);
  const notifications = useAppStore((state) =>
    currentUser ? state.getNotificationsForUser(currentUser.id) : []
  );

  const [activeBatchId, setActiveBatchId] = useState<string | null>(null);
  const [selectedDepartment, setSelectedDepartment] = useState<Department | null>(null);
  const [showProvisionalCash, setShowProvisionalCash] = useState(false);
  const [selectedDispatchVouchers, setSelectedDispatchVouchers] = useState<string[]>([]);
  const [dispatchPerson, setDispatchPerson] = useState<string>("SELECT_PERSON");
  const [previousDepartment, setPreviousDepartment] = useState<Department | null>(null);

  // Redirect if not logged in or not in AUDIT department
  useEffect(() => {
    if (!currentUser) {
      navigate('/');
      return;
    }

    if (currentUser.department !== 'AUDIT') {
      navigate('/dashboard');
    }
  }, [currentUser, navigate]);

  // Fetch batches when AUDIT dashboard loads
  useEffect(() => {
    if (currentUser && currentUser.department === 'AUDIT') {
      fetchBatches().catch((error) => {
        console.error('❌ AUDIT Dashboard: Failed to fetch batches:', error);
      });
    }
  }, [currentUser?.id]); // Remove fetchBatches from dependencies to prevent infinite loop

  // Get unread notifications
  const unreadNotifications = notifications.filter(n => !n.isRead);
  const unreadBatchNotifications = unreadNotifications.filter(
    n => n.type === 'NEW_BATCH' && !n.fromAudit
  );

  // Safely filter pending batches with error handling
  const pendingBatches = Array.isArray(voucherBatches)
    ? voucherBatches.filter(batch => batch && !batch.received && !batch.fromAudit)
    : [];

  // Handle opening a batch
  const handleOpenBatch = (batchId: string) => {
    try {
      // Store current state before opening batch
      if (selectedDepartment) {
        setPreviousDepartment(selectedDepartment);
      }
      setActiveBatchId(batchId);
    } catch (error) {
      console.error("Error opening batch:", error);
      toast({
        title: "Error",
        description: "Failed to open batch. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Handle closing a batch
  const handleCloseBatch = () => {
    try {
      setActiveBatchId(null);

      // Restore previous state if it exists
      if (previousDepartment) {
        setSelectedDepartment(previousDepartment);
        // Clear the previous department to avoid unexpected redirects
        setPreviousDepartment(null);
      }
    } catch (error) {
      console.error("Error closing batch:", error);
    }
  };

  // Handle selecting a department
  const handleSelectDepartment = (department: Department) => {
    setSelectedDepartment(department);
    setShowProvisionalCash(false);
    // Reset selected vouchers when changing departments
    setSelectedDispatchVouchers([]);
    setDispatchPerson("SELECT_PERSON");
  };

  // Handle showing provisional cash
  const handleShowProvisionalCash = (department: Department) => {
    setSelectedDepartment(department);
    setShowProvisionalCash(true);
  };

  // Handle going back to department hubs
  const handleBack = () => {
    if (selectedDepartment) {
      setSelectedDepartment(null);
      setShowProvisionalCash(false);
      // Reset selected vouchers when going back
      setSelectedDispatchVouchers([]);
      setDispatchPerson("SELECT_PERSON");
    }
  };

  // Handle dispatching vouchers
  const handleDispatchVouchers = () => {
    if (!selectedDepartment || selectedDispatchVouchers.length === 0) {
      toast({
        title: "Error",
        description: "Please select vouchers and a department.",
        variant: "destructive",
      });
      return;
    }

    if (!dispatchPerson || dispatchPerson === "SELECT_PERSON") {
      toast({
        title: "Error",
        description: "Please select a dispatch person.",
        variant: "destructive",
      });
      return;
    }

    try {
      // Update vouchers with dispatcher information
      selectedDispatchVouchers.forEach(voucherId => {
        // Get the current voucher to check its status
        const voucher = vouchers.find(v => v.id === voucherId);
        if (!voucher) return;

        // WORKFLOW FIX: Don't change status yet - only mark as dispatched
        // Status will change when department receives the batch
        updateVoucher(voucherId, {
          auditDispatchedBy: dispatchPerson,
          dispatched: true,
          // Keep current status until department receives it
          // status: voucher.status, // Keep existing status
          // If it was pending return, mark it as returned
          isReturned: voucher.pendingReturn ? true : undefined,
          pendingReturn: voucher.pendingReturn ? false : undefined
        });
      });

      // Send vouchers to department
      sendVouchersFromAuditToDepartment(selectedDepartment, selectedDispatchVouchers);

      toast({
        title: "Success",
        description: `${selectedDispatchVouchers.length} vouchers dispatched to ${selectedDepartment}.`,
      });

      // Reset selection after dispatch
      setSelectedDispatchVouchers([]);
      setDispatchPerson("SELECT_PERSON");
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to dispatch vouchers.",
        variant: "destructive",
      });
    }
  };

  // Check if there are pending batches
  const hasPendingBatches = pendingBatches.length > 0;

  // Determine if the view should be locked
  const isViewLocked = hasPendingBatches && !activeBatchId;

  // Get the first valid batch ID for receiving
  const firstBatchId = hasPendingBatches && pendingBatches[0]?.id;

  // Handle receiving vouchers
  const handleReceiveVouchers = () => {
    if (firstBatchId) {
      handleOpenBatch(firstBatchId);
    }
  };

  return (
    <div className="container space-y-6">
      {/* Unread notifications */}
      {unreadBatchNotifications.length > 0 && (
        <div className="bg-amber-100 dark:bg-amber-900/20 p-4 rounded-lg flex items-center gap-2">
          <AlertTriangle className="h-5 w-5 text-amber-500" />
          <p className="text-sm text-amber-800 dark:text-amber-300 uppercase">
            YOU HAVE {unreadBatchNotifications.length} UNREAD NOTIFICATION{unreadBatchNotifications.length !== 1 ? 'S' : ''} FOR NEW VOUCHER BATCH{unreadBatchNotifications.length !== 1 ? 'ES' : ''}.
          </p>
        </div>
      )}

      {/* Main content */}
      {isViewLocked ? (
        <div className="space-y-6 mb-8">
          {/* Show newly arrived vouchers */}
          <NewlyArrivedVouchers
            pendingBatches={pendingBatches}
            onOpenBatch={handleOpenBatch}
          />

          {/* Show department hubs but make them disabled */}
          <div className="opacity-60 pointer-events-none">
            <DepartmentVoucherHubs
              onSelectDepartment={handleSelectDepartment}
              onShowProvisionalCash={handleShowProvisionalCash}
            />
          </div>
        </div>
      ) : selectedDepartment ? (
        showProvisionalCash ? (
          <DepartmentProvisionalCash
            department={selectedDepartment}
            onBack={handleBack}
          />
        ) : (
          <>
            <DepartmentVoucherHub
              department={selectedDepartment}
              auditUsers={getAuditUsers().filter(user => user !== "SELECT_PERSON")}
              onBackToHubs={handleBack}
            />

            {selectedDispatchVouchers.length > 0 && (
              <DispatchControls
                selectedDispatchVouchers={selectedDispatchVouchers}
                selectedDepartment={selectedDepartment}
                dispatchPerson={dispatchPerson}
                setDispatchPerson={setDispatchPerson}
                handleDispatchVouchers={handleDispatchVouchers}
              />
            )}
          </>
        )
      ) : (
        <>
          {hasPendingBatches && (
            <div className="space-y-6 mb-8">
              <NewlyArrivedVouchers
                pendingBatches={pendingBatches}
                onOpenBatch={handleOpenBatch}
              />
            </div>
          )}

          <DepartmentVoucherHubs
            onSelectDepartment={handleSelectDepartment}
            onShowProvisionalCash={handleShowProvisionalCash}
          />
        </>
      )}

      {/* Batch receiving dialog */}
      {activeBatchId && (
        <VoucherBatchReceiving
          batchId={activeBatchId}
          open={!!activeBatchId}
          onClose={handleCloseBatch}
        />
      )}
    </div>
  );

  function getAuditUsers() {
    return [
      "SELECT_PERSON",
      "FIRANG BOAKYE",
      "HARRISON A. SARPONG",
      "WILLIAM AKUAMOAH",
      "RICHARD ARTHUR",
      "SAMUEL ASIEDU",
      "SAMUEL CATOE",
      "BRIGHT OMANE",
      "KWAME A. FRIMPNG",
      "ABENA Y. FORSON",
      "ELIZABETH A. KWAKYE",
      "NAOMI O. ANSAH",
      "AKUA BRIFAA",
      "SYLVESTER M. NYARKO",
      "DEBORAH ANIM",
      "PRINCE O. BONSU",
      "GUEST"
    ];
  }
}
