const mysql = require('mysql2/promise');

async function fixDispatchedFlag() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root', 
    password: 'vms@2025@1989',
    database: 'vms_production'
  });
  
  console.log('🔧 Fixing dispatched flag for voucher that was dispatched by audit...');
  
  // Update the voucher to set dispatched = TRUE since it was dispatched by audit
  await connection.execute(`
    UPDATE vouchers SET
      dispatched = TRUE
    WHERE id = 'd34ca623-d30c-4fd9-97e3-90ca0193bf84'
      AND audit_dispatched_by IS NOT NULL
  `);
  
  console.log('✅ Updated dispatched flag to TRUE');
  
  // Check the result
  const [vouchers] = await connection.execute('SELECT voucher_id, department, original_department, status, dispatched, audit_dispatched_by FROM vouchers WHERE id = ?', ['d34ca623-d30c-4fd9-97e3-90ca0193bf84']);
  const voucher = vouchers[0];
  
  console.log('📄 Updated voucher state:');
  console.log('   Voucher ID:', voucher.voucher_id);
  console.log('   Department:', voucher.department);
  console.log('   Original Department:', voucher.original_department);
  console.log('   Status:', voucher.status);
  console.log('   Dispatched:', voucher.dispatched);
  console.log('   Audit Dispatched By:', voucher.audit_dispatched_by);
  
  console.log('\n🧪 DISPATCHED tab filtering analysis:');
  console.log('   Filter: originalDepartment === FINANCE && dispatched === true');
  console.log('   originalDepartment === FINANCE:', voucher.original_department === 'FINANCE');
  console.log('   dispatched === true:', voucher.dispatched === 1);
  console.log('   Should appear in DISPATCHED tab:', voucher.original_department === 'FINANCE' && voucher.dispatched === 1 ? '✅ YES' : '❌ NO');
  
  await connection.end();
}

fixDispatchedFlag().catch(console.error);
