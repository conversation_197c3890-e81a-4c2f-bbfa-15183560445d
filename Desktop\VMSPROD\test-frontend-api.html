<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VMS Frontend API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .voucher-item { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>🔍 VMS Frontend API Test</h1>
    <p>This page tests the VMS API endpoints to diagnose voucher display issues.</p>

    <div class="test-section info">
        <h3>📊 Test Results</h3>
        <div id="results"></div>
    </div>

    <div class="test-section">
        <h3>🔐 Authentication Test</h3>
        <button onclick="testLogin('FINANCE', 'FELIX AYISI', '123')">Login as FINANCE User</button>
        <button onclick="testLogin('AUDIT', 'SAMUEL ASIEDU', '123')">Login as AUDIT User</button>
        <button onclick="testCurrentUser()">Check Current User</button>
        <button onclick="testLogout()">Logout</button>
        <div id="auth-results"></div>
    </div>

    <div class="test-section">
        <h3>📋 Voucher API Test</h3>
        <button onclick="testVouchers()">Fetch All Vouchers</button>
        <button onclick="testVouchers('FINANCE')">Fetch FINANCE Vouchers</button>
        <button onclick="testVouchers('AUDIT')">Fetch AUDIT Vouchers</button>
        <div id="voucher-results"></div>
    </div>

    <div class="test-section">
        <h3>🔧 Direct Database Test</h3>
        <button onclick="testDirectAPI()">Test Direct API Calls</button>
        <div id="direct-results"></div>
    </div>

    <script>
        let currentUser = null;
        
        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            const div = document.createElement('div');
            div.className = `test-section ${type}`;
            div.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            results.appendChild(div);
            console.log(`[${timestamp}] ${message}`);
        }

        async function testLogin(department, username, password) {
            try {
                log(`🔐 Testing login: ${username} (${department})...`);
                
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify({ department, username, password })
                });

                const data = await response.json();
                
                if (response.ok) {
                    currentUser = data.user;
                    log(`✅ Login successful: ${data.user.name} (${data.user.department})`, 'success');
                    document.getElementById('auth-results').innerHTML = `
                        <div class="success">
                            <h4>✅ Login Successful</h4>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    log(`❌ Login failed: ${data.error}`, 'error');
                    document.getElementById('auth-results').innerHTML = `
                        <div class="error">
                            <h4>❌ Login Failed</h4>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                log(`❌ Login error: ${error.message}`, 'error');
                document.getElementById('auth-results').innerHTML = `
                    <div class="error">
                        <h4>❌ Login Error</h4>
                        <pre>${error.message}</pre>
                    </div>
                `;
            }
        }

        async function testCurrentUser() {
            try {
                log('🔍 Checking current user...');
                
                const response = await fetch('/api/auth/me', {
                    credentials: 'include'
                });

                const data = await response.json();
                
                if (response.ok) {
                    currentUser = data;
                    log(`✅ Current user: ${data.name} (${data.department})`, 'success');
                    document.getElementById('auth-results').innerHTML = `
                        <div class="success">
                            <h4>✅ Current User</h4>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    log(`❌ Not authenticated: ${data.error}`, 'error');
                    document.getElementById('auth-results').innerHTML = `
                        <div class="error">
                            <h4>❌ Not Authenticated</h4>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                log(`❌ Current user error: ${error.message}`, 'error');
            }
        }

        async function testLogout() {
            try {
                log('🚪 Testing logout...');
                
                const response = await fetch('/api/auth/logout', {
                    method: 'POST',
                    credentials: 'include'
                });

                if (response.ok) {
                    currentUser = null;
                    log('✅ Logout successful', 'success');
                } else {
                    log('❌ Logout failed', 'error');
                }
            } catch (error) {
                log(`❌ Logout error: ${error.message}`, 'error');
            }
        }

        async function testVouchers(department = null) {
            try {
                const deptParam = department ? `?department=${department}` : '';
                log(`📋 Fetching vouchers${department ? ` for ${department}` : ''}...`);
                
                const response = await fetch(`/api/vouchers${deptParam}`, {
                    credentials: 'include'
                });

                const data = await response.json();
                
                if (response.ok) {
                    log(`✅ Fetched ${data.length} vouchers`, 'success');
                    
                    let voucherHtml = `
                        <div class="success">
                            <h4>✅ Vouchers (${data.length} found)</h4>
                    `;
                    
                    if (data.length > 0) {
                        voucherHtml += '<div>';
                        data.slice(0, 5).forEach((voucher, index) => {
                            voucherHtml += `
                                <div class="voucher-item">
                                    <strong>${voucher.voucher_id}</strong> - ${voucher.claimant}<br>
                                    Department: ${voucher.department} | Original: ${voucher.original_department || 'N/A'}<br>
                                    Status: ${voucher.status} | Created: ${new Date(voucher.created_at).toLocaleString()}
                                </div>
                            `;
                        });
                        if (data.length > 5) {
                            voucherHtml += `<p><em>... and ${data.length - 5} more vouchers</em></p>`;
                        }
                        voucherHtml += '</div>';
                    } else {
                        voucherHtml += '<p><em>No vouchers found</em></p>';
                    }
                    
                    voucherHtml += '</div>';
                    document.getElementById('voucher-results').innerHTML = voucherHtml;
                } else {
                    log(`❌ Failed to fetch vouchers: ${data.error}`, 'error');
                    document.getElementById('voucher-results').innerHTML = `
                        <div class="error">
                            <h4>❌ Failed to Fetch Vouchers</h4>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                log(`❌ Voucher fetch error: ${error.message}`, 'error');
                document.getElementById('voucher-results').innerHTML = `
                    <div class="error">
                        <h4>❌ Voucher Fetch Error</h4>
                        <pre>${error.message}</pre>
                    </div>
                `;
            }
        }

        async function testDirectAPI() {
            try {
                log('🔧 Testing direct API endpoints...');
                
                // Test health endpoint
                const healthResponse = await fetch('/health');
                const healthData = await healthResponse.json();
                log(`✅ Health check: ${healthData.status}`, 'success');
                
                // Test users endpoint
                const usersResponse = await fetch('/api/auth/users-by-department');
                const usersData = await usersResponse.json();
                log(`✅ Users endpoint: ${usersData.length} users found`, 'success');
                
                document.getElementById('direct-results').innerHTML = `
                    <div class="success">
                        <h4>✅ Direct API Tests</h4>
                        <h5>Health Check:</h5>
                        <pre>${JSON.stringify(healthData, null, 2)}</pre>
                        <h5>Users (${usersData.length} found):</h5>
                        <pre>${JSON.stringify(usersData, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                log(`❌ Direct API error: ${error.message}`, 'error');
                document.getElementById('direct-results').innerHTML = `
                    <div class="error">
                        <h4>❌ Direct API Error</h4>
                        <pre>${error.message}</pre>
                    </div>
                `;
            }
        }

        // Auto-run initial tests
        window.onload = function() {
            log('🚀 VMS Frontend API Test Started');
            testCurrentUser();
            testDirectAPI();
        };
    </script>
</body>
</html>
