<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VMS Frontend Store Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .voucher { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 3px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
    </style>
</head>
<body>
    <h1>🔍 VMS Frontend Store Debug</h1>
    
    <div class="section">
        <h3>🔐 Step 1: Login</h3>
        <button onclick="testLogin()">Login as FINANCE User</button>
        <div id="login-result"></div>
    </div>

    <div class="section">
        <h3>📊 Step 2: Check Store State</h3>
        <button onclick="checkStoreState()">Check Zustand Store</button>
        <div id="store-result"></div>
    </div>

    <div class="section">
        <h3>🔍 Step 3: Analyze Voucher Fields</h3>
        <button onclick="analyzeVoucherFields()">Analyze Voucher Fields</button>
        <div id="field-result"></div>
    </div>

    <div class="section">
        <h3>🧪 Step 4: Test Filtering Logic</h3>
        <button onclick="testFilteringLogic()">Test Filtering Logic</button>
        <div id="filter-result"></div>
    </div>

    <div class="section">
        <h3>🔄 Step 5: Manual Refresh</h3>
        <button onclick="refreshAnalysis()">Refresh All Analysis</button>
        <button onclick="window.location.reload()">Reload Page</button>
    </div>

    <script>
        async function testLogin() {
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'include',
                    body: JSON.stringify({
                        department: 'FINANCE',
                        username: 'FELIX AYISI',
                        password: '123'
                    })
                });

                const data = await response.json();
                
                if (response.ok) {
                    document.getElementById('login-result').innerHTML = `
                        <div class="success">
                            <h4>✅ Login Successful</h4>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    document.getElementById('login-result').innerHTML = `
                        <div class="error">
                            <h4>❌ Login Failed</h4>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                document.getElementById('login-result').innerHTML = `
                    <div class="error">
                        <h4>❌ Login Error</h4>
                        <pre>${error.message}</pre>
                    </div>
                `;
            }
        }

        function checkStoreState() {
            // Access the Zustand store from the window object (if available)
            if (window.useVoucherStore) {
                const store = window.useVoucherStore.getState();
                const vouchers = store.vouchers || [];
                
                document.getElementById('store-result').innerHTML = `
                    <div class="success">
                        <h4>📊 Store State</h4>
                        <p><strong>Total vouchers in store:</strong> ${vouchers.length}</p>
                        <p><strong>Store version:</strong> ${store.version || 'N/A'}</p>
                        <p><strong>Last update:</strong> ${store.lastUpdate || 'N/A'}</p>
                        <h5>First 3 vouchers:</h5>
                        <pre>${JSON.stringify(vouchers.slice(0, 3), null, 2)}</pre>
                    </div>
                `;
            } else {
                document.getElementById('store-result').innerHTML = `
                    <div class="error">
                        <h4>❌ Store Not Accessible</h4>
                        <p>Zustand store not found on window object. Try this after the main app loads.</p>
                    </div>
                `;
            }
        }

        function analyzeVoucherFields() {
            if (window.useVoucherStore) {
                const store = window.useVoucherStore.getState();
                const vouchers = store.vouchers || [];
                
                if (vouchers.length === 0) {
                    document.getElementById('field-result').innerHTML = `
                        <div class="error">
                            <h4>❌ No Vouchers in Store</h4>
                            <p>Store is empty. Make sure to login and fetch vouchers first.</p>
                        </div>
                    `;
                    return;
                }

                let analysis = '<div class="success"><h4>🔍 Voucher Field Analysis</h4>';
                
                // Check field presence
                const hasOriginalDepartment = vouchers.filter(v => v.originalDepartment !== undefined).length;
                const hasOriginal_department = vouchers.filter(v => v.original_department !== undefined).length;
                const hasSentToAudit = vouchers.filter(v => v.sentToAudit !== undefined).length;
                const hasSent_to_audit = vouchers.filter(v => v.sent_to_audit !== undefined).length;
                
                analysis += `
                    <p><strong>Field Presence Analysis:</strong></p>
                    <ul>
                        <li>originalDepartment (camel): ${hasOriginalDepartment}/${vouchers.length}</li>
                        <li>original_department (snake): ${hasOriginal_department}/${vouchers.length}</li>
                        <li>sentToAudit (camel): ${hasSentToAudit}/${vouchers.length}</li>
                        <li>sent_to_audit (snake): ${hasSent_to_audit}/${vouchers.length}</li>
                    </ul>
                `;

                // Sample voucher analysis
                const sampleVoucher = vouchers[0];
                analysis += `
                    <h5>Sample Voucher Fields:</h5>
                    <div class="voucher">
                        <p><strong>ID:</strong> ${sampleVoucher.id}</p>
                        <p><strong>voucherId:</strong> ${sampleVoucher.voucherId}</p>
                        <p><strong>department:</strong> ${sampleVoucher.department}</p>
                        <p><strong>originalDepartment:</strong> ${sampleVoucher.originalDepartment}</p>
                        <p><strong>original_department:</strong> ${sampleVoucher.original_department}</p>
                        <p><strong>sentToAudit:</strong> ${sampleVoucher.sentToAudit} (${typeof sampleVoucher.sentToAudit})</p>
                        <p><strong>sent_to_audit:</strong> ${sampleVoucher.sent_to_audit} (${typeof sampleVoucher.sent_to_audit})</p>
                    </div>
                `;

                analysis += '</div>';
                document.getElementById('field-result').innerHTML = analysis;
            } else {
                document.getElementById('field-result').innerHTML = `
                    <div class="error">
                        <h4>❌ Store Not Accessible</h4>
                        <p>Zustand store not found. Try this after the main app loads.</p>
                    </div>
                `;
            }
        }

        function testFilteringLogic() {
            if (window.useVoucherStore) {
                const store = window.useVoucherStore.getState();
                const vouchers = store.vouchers || [];
                
                if (vouchers.length === 0) {
                    document.getElementById('filter-result').innerHTML = `
                        <div class="error">
                            <h4>❌ No Vouchers to Filter</h4>
                            <p>Store is empty. Make sure to login and fetch vouchers first.</p>
                        </div>
                    `;
                    return;
                }

                // Test the exact filtering logic from use-department-data.ts
                const department = 'FINANCE';
                
                // Current filtering logic
                const allRelevantVouchers = vouchers.filter(v =>
                    v.department === department || v.originalDepartment === department
                );
                
                // Alternative filtering logic (with snake_case)
                const allRelevantVouchersAlt = vouchers.filter(v =>
                    v.department === department || v.original_department === department
                );

                // Pending vouchers
                const pendingVouchers = allRelevantVouchers.filter(v =>
                    v.department === department && !v.sentToAudit
                );

                // Processing vouchers
                const processingVouchers = allRelevantVouchers.filter(v =>
                    v.originalDepartment === department && v.sentToAudit === true
                );

                // Processing vouchers (alternative)
                const processingVouchersAlt = allRelevantVouchersAlt.filter(v =>
                    v.original_department === department && (v.sentToAudit === true || v.sent_to_audit === true)
                );

                let result = `
                    <div class="success">
                        <h4>🧪 Filtering Logic Test Results</h4>
                        <p><strong>Total vouchers in store:</strong> ${vouchers.length}</p>
                        <p><strong>Current filtering (camelCase):</strong> ${allRelevantVouchers.length}</p>
                        <p><strong>Alternative filtering (snake_case):</strong> ${allRelevantVouchersAlt.length}</p>
                        <p><strong>Pending vouchers:</strong> ${pendingVouchers.length}</p>
                        <p><strong>Processing vouchers (camel):</strong> ${processingVouchers.length}</p>
                        <p><strong>Processing vouchers (snake):</strong> ${processingVouchersAlt.length}</p>
                        
                        <h5>Vouchers by Department:</h5>
                `;

                // Group by department
                const byDept = {};
                vouchers.forEach(v => {
                    if (!byDept[v.department]) byDept[v.department] = [];
                    byDept[v.department].push(v);
                });

                Object.keys(byDept).forEach(dept => {
                    result += `<p><strong>${dept}:</strong> ${byDept[dept].length} vouchers</p>`;
                });

                result += `
                        <h5>Sample Relevant Vouchers:</h5>
                        <pre>${JSON.stringify(allRelevantVouchersAlt.slice(0, 3), null, 2)}</pre>
                    </div>
                `;

                document.getElementById('filter-result').innerHTML = result;
            } else {
                document.getElementById('filter-result').innerHTML = `
                    <div class="error">
                        <h4>❌ Store Not Accessible</h4>
                        <p>Zustand store not found. Try this after the main app loads.</p>
                    </div>
                `;
            }
        }

        // Auto-check store state when page loads
        window.onload = function() {
            setTimeout(() => {
                if (window.useVoucherStore) {
                    checkStoreState();
                    analyzeVoucherFields();
                    testFilteringLogic();
                } else {
                    // Try to access store from React DevTools or global scope
                    console.log('Checking for store in different locations...');
                    if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
                        console.log('React DevTools found');
                    }
                }
            }, 1000);
        };

        // Add a manual refresh button
        function refreshAnalysis() {
            checkStoreState();
            analyzeVoucherFields();
            testFilteringLogic();
        }
    </script>
</body>
</html>
