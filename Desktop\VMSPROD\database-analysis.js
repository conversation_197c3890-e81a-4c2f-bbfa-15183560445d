const mysql = require('mysql2/promise');

async function databaseAnalysis() {
  console.log('🔍 DATABASE ANALYSIS FOR VOUCHER DISPLAY ISSUE\n');
  
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });

  try {
    // 1. Get exact voucher structure
    console.log('1. 📊 VOUCHER TABLE STRUCTURE:');
    const [columns] = await connection.execute('DESCRIBE vouchers');
    columns.forEach(col => {
      console.log(`   ${col.Field} - ${col.Type} (${col.Null === 'YES' ? 'NULL' : 'NOT NULL'})`);
    });

    // 2. Get all vouchers with detailed info
    console.log('\n2. 📋 ALL VOUCHERS DETAILED:');
    const [vouchers] = await connection.execute(`
      SELECT 
        id, voucher_id, claimant, department, 
        original_department,
        status, sent_to_audit, deleted, created_at
      FROM vouchers 
      ORDER BY created_at DESC
    `);
    
    console.log(`Total vouchers: ${vouchers.length}`);
    
    vouchers.forEach((v, i) => {
      console.log(`\n${i+1}. ${v.voucher_id}`);
      console.log(`   ID: ${v.id}`);
      console.log(`   Claimant: ${v.claimant}`);
      console.log(`   Department: "${v.department}"`);
      console.log(`   Original Dept: ${v.original_department === null ? 'NULL' : `"${v.original_department}"`}`);
      console.log(`   Status: "${v.status}"`);
      console.log(`   Sent to Audit: ${v.sent_to_audit} (${typeof v.sent_to_audit})`);
      console.log(`   Deleted: ${v.deleted} (${typeof v.deleted})`);
      console.log(`   Created: ${v.created_at}`);
    });

    // 3. FINANCE filtering analysis
    console.log('\n3. 🎯 FINANCE FILTERING ANALYSIS:');
    
    // Current department = FINANCE
    const [currentFinance] = await connection.execute(`
      SELECT COUNT(*) as count FROM vouchers 
      WHERE department = 'FINANCE' AND deleted = FALSE
    `);
    console.log(`   Current department = FINANCE: ${currentFinance[0].count}`);
    
    // Original department = FINANCE
    const [originalFinance] = await connection.execute(`
      SELECT COUNT(*) as count FROM vouchers 
      WHERE original_department = 'FINANCE' AND deleted = FALSE
    `);
    console.log(`   Original department = FINANCE: ${originalFinance[0].count}`);
    
    // Either current OR original = FINANCE
    const [eitherFinance] = await connection.execute(`
      SELECT COUNT(*) as count FROM vouchers 
      WHERE (department = 'FINANCE' OR original_department = 'FINANCE') AND deleted = FALSE
    `);
    console.log(`   Either current OR original = FINANCE: ${eitherFinance[0].count}`);

    // 4. Pending vs Processing breakdown
    console.log('\n4. 📊 PENDING vs PROCESSING BREAKDOWN:');
    
    // Pending: department = FINANCE AND sent_to_audit = FALSE
    const [pending] = await connection.execute(`
      SELECT voucher_id, claimant, status, sent_to_audit FROM vouchers 
      WHERE department = 'FINANCE' AND sent_to_audit = FALSE AND deleted = FALSE
    `);
    console.log(`   PENDING (in FINANCE, not sent): ${pending.length}`);
    pending.forEach(v => {
      console.log(`     - ${v.voucher_id} (${v.claimant}) - Status: ${v.status}, SentToAudit: ${v.sent_to_audit}`);
    });
    
    // Processing: original_department = FINANCE AND sent_to_audit = TRUE
    const [processing] = await connection.execute(`
      SELECT voucher_id, claimant, department, status, sent_to_audit FROM vouchers 
      WHERE original_department = 'FINANCE' AND sent_to_audit = TRUE AND deleted = FALSE
    `);
    console.log(`   PROCESSING (from FINANCE, sent to audit): ${processing.length}`);
    processing.forEach(v => {
      console.log(`     - ${v.voucher_id} (${v.claimant}) - Dept: ${v.department}, Status: ${v.status}, SentToAudit: ${v.sent_to_audit}`);
    });

    // 5. Data type analysis
    console.log('\n5. 🔍 DATA TYPE ANALYSIS:');
    const sampleVoucher = vouchers[0];
    if (sampleVoucher) {
      console.log('   Sample voucher data types:');
      Object.keys(sampleVoucher).forEach(key => {
        const value = sampleVoucher[key];
        console.log(`     ${key}: ${value} (${typeof value})`);
      });
    }

    // 6. Check for data inconsistencies
    console.log('\n6. ⚠️ DATA INCONSISTENCY CHECK:');
    
    // Check for vouchers with NULL original_department
    const [nullOriginal] = await connection.execute(`
      SELECT COUNT(*) as count FROM vouchers 
      WHERE original_department IS NULL AND deleted = FALSE
    `);
    console.log(`   Vouchers with NULL original_department: ${nullOriginal[0].count}`);
    
    // Check for invalid boolean values
    const [invalidBooleans] = await connection.execute(`
      SELECT voucher_id, sent_to_audit, deleted FROM vouchers 
      WHERE sent_to_audit NOT IN (0, 1) OR deleted NOT IN (0, 1)
    `);
    console.log(`   Vouchers with invalid boolean values: ${invalidBooleans.length}`);
    
    if (invalidBooleans.length > 0) {
      invalidBooleans.forEach(v => {
        console.log(`     - ${v.voucher_id}: sent_to_audit=${v.sent_to_audit}, deleted=${v.deleted}`);
      });
    }

    console.log('\n🎯 SUMMARY:');
    console.log(`   Total vouchers: ${vouchers.length}`);
    console.log(`   FINANCE-related: ${eitherFinance[0].count}`);
    console.log(`   Expected pending: ${pending.length}`);
    console.log(`   Expected processing: ${processing.length}`);
    console.log(`   Expected total for FINANCE: ${pending.length + processing.length}`);

  } catch (error) {
    console.error('❌ Database analysis error:', error);
  } finally {
    await connection.end();
  }
}

databaseAnalysis().catch(console.error);
