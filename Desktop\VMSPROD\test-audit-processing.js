/**
 * Test Audit Processing
 * Simulate what happens when audit processes a voucher to identify duplication
 */

const mysql = require('mysql2/promise');

// Database configuration
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: 'vms@2025@1989',
  database: 'vms_production'
};

async function testAuditProcessing() {
  let connection;
  
  try {
    console.log('🔄 Connecting to database...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Connected to database');
    
    // Get the current voucher and batch
    const [vouchers] = await connection.execute('SELECT * FROM vouchers ORDER BY created_at DESC LIMIT 1');
    if (vouchers.length === 0) {
      console.log('❌ No vouchers found');
      return;
    }
    
    const voucher = vouchers[0];
    console.log(`\n📄 Current voucher: ${voucher.voucher_id} | Status: ${voucher.status} | Department: ${voucher.department}`);
    
    // Get the batch that contains this voucher
    const [batches] = await connection.execute(`
      SELECT vb.*, bv.voucher_id as voucher_uuid
      FROM voucher_batches vb
      JOIN batch_vouchers bv ON vb.id = bv.batch_id
      WHERE bv.voucher_id = ? AND vb.from_audit = 0 AND vb.received = 1
      ORDER BY vb.sent_time DESC
      LIMIT 1
    `, [voucher.id]);
    
    if (batches.length === 0) {
      console.log('❌ No received batch found for this voucher');
      return;
    }
    
    const batch = batches[0];
    console.log(`\n📦 Found batch: ${batch.id.substring(0, 8)}... | Department: ${batch.department} | FromAudit: ${batch.from_audit} | Received: ${batch.received}`);
    
    // Now simulate audit processing this voucher (accepting it)
    console.log('\n🔄 Simulating audit processing (accepting voucher)...');
    
    // This simulates what the frontend sends to the API
    const receivedVoucherIds = [voucher.id];  // Accept the voucher
    const rejectedVoucherIds = [];            // No rejections
    const rejectionComments = {};
    
    console.log(`📤 API Call: POST /api/batches/${batch.id}/receive`);
    console.log(`   receivedVoucherIds: [${voucher.id.substring(0, 8)}...]`);
    console.log(`   rejectedVoucherIds: []`);
    
    // Make the actual API call to see what happens
    const response = await fetch(`http://localhost:8080/api/batches/${batch.id}/receive`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': 'connect.sid=test-session' // Simulate session
      },
      body: JSON.stringify({
        receivedVoucherIds,
        rejectedVoucherIds,
        rejectionComments
      })
    });
    
    if (response.ok) {
      const result = await response.json();
      console.log('✅ API call successful');
      console.log(`   Response: ${JSON.stringify(result, null, 2).substring(0, 200)}...`);
    } else {
      const error = await response.text();
      console.log(`❌ API call failed: ${response.status} ${response.statusText}`);
      console.log(`   Error: ${error}`);
    }
    
    // Check what batches exist now
    console.log('\n🔍 Checking batches after processing...');
    const [allBatches] = await connection.execute(`
      SELECT id, department, sent_by, from_audit, received, sent_time
      FROM voucher_batches 
      ORDER BY sent_time DESC
    `);
    
    console.log(`📦 Total batches: ${allBatches.length}`);
    allBatches.forEach((b, index) => {
      console.log(`   ${index + 1}. ${b.id.substring(0, 8)}... | To: ${b.department} | From: ${b.sent_by} | FromAudit: ${b.from_audit} | Received: ${b.received}`);
    });
    
    // Check batch-voucher relationships
    const [relationships] = await connection.execute(`
      SELECT bv.batch_id, bv.voucher_id, v.voucher_id as voucher_number, vb.department, vb.from_audit
      FROM batch_vouchers bv
      JOIN vouchers v ON bv.voucher_id = v.id
      JOIN voucher_batches vb ON bv.batch_id = vb.id
      ORDER BY vb.sent_time DESC
    `);
    
    console.log(`\n🔗 Batch-voucher relationships: ${relationships.length}`);
    relationships.forEach((r, index) => {
      console.log(`   ${index + 1}. Batch: ${r.batch_id.substring(0, 8)}... | Voucher: ${r.voucher_number} | To: ${r.department} | FromAudit: ${r.from_audit}`);
    });
    
    console.log('\n🎯 TEST COMPLETED!');
    
  } catch (error) {
    console.error('❌ Error testing audit processing:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the test
testAuditProcessing();
