const mysql = require('mysql2/promise');
const axios = require('axios');

async function comprehensiveRootCauseAnalysis() {
  console.log('🔍 COMPREHENSIVE ROOT CAUSE ANALYSIS FOR VOUCHER DISPLAY ISSUE\n');
  console.log('=' .repeat(80));
  
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });

  try {
    // ========================================
    // 1. DATABASE VERIFICATION
    // ========================================
    console.log('\n1. 📊 DATABASE VERIFICATION');
    console.log('-'.repeat(50));
    
    // Get exact voucher structure and content
    const [vouchers] = await connection.execute(`
      SELECT 
        id, voucher_id, claimant, department, original_department, 
        status, sent_to_audit, deleted, created_at,
        CASE WHEN original_department IS NULL THEN 'NULL' ELSE original_department END as orig_dept_display
      FROM vouchers 
      ORDER BY created_at DESC
    `);
    
    console.log(`📊 Total vouchers in database: ${vouchers.length}`);
    console.log(`📊 Non-deleted vouchers: ${vouchers.filter(v => !v.deleted).length}`);
    
    console.log('\n📋 DETAILED VOUCHER BREAKDOWN:');
    vouchers.forEach((v, i) => {
      console.log(`${i+1}. ${v.voucher_id} (${v.id.substring(0,8)}...)`);
      console.log(`   Claimant: ${v.claimant}`);
      console.log(`   Department: ${v.department}`);
      console.log(`   Original Dept: ${v.orig_dept_display}`);
      console.log(`   Status: ${v.status}`);
      console.log(`   Sent to Audit: ${v.sent_to_audit}`);
      console.log(`   Deleted: ${v.deleted}`);
      console.log(`   Created: ${v.created_at}`);
      console.log('   ---');
    });

    // Check FINANCE-related vouchers specifically
    console.log('\n📊 FINANCE-RELATED VOUCHERS ANALYSIS:');
    const financeCurrentDept = vouchers.filter(v => !v.deleted && v.department === 'FINANCE');
    const financeOriginalDept = vouchers.filter(v => !v.deleted && v.original_department === 'FINANCE');
    const financeEither = vouchers.filter(v => !v.deleted && (v.department === 'FINANCE' || v.original_department === 'FINANCE'));
    
    console.log(`   Current department = FINANCE: ${financeCurrentDept.length}`);
    console.log(`   Original department = FINANCE: ${financeOriginalDept.length}`);
    console.log(`   Either current OR original = FINANCE: ${financeEither.length}`);

    // ========================================
    // 2. API RESPONSE ANALYSIS
    // ========================================
    console.log('\n\n2. 🌐 API RESPONSE ANALYSIS');
    console.log('-'.repeat(50));
    
    try {
      // First login to get session
      console.log('🔐 Logging in as FINANCE user...');
      const loginResponse = await axios.post('http://localhost:8080/api/auth/login', {
        department: 'FINANCE',
        username: 'FELIX AYISI',
        password: '123'
      }, {
        withCredentials: true,
        timeout: 10000
      });
      
      console.log('✅ Login successful');
      
      // Extract cookies for subsequent requests
      const cookies = loginResponse.headers['set-cookie'];
      const cookieHeader = cookies ? cookies.map(cookie => cookie.split(';')[0]).join('; ') : '';
      
      // Test vouchers API
      console.log('📋 Fetching vouchers from API...');
      const vouchersResponse = await axios.get('http://localhost:8080/api/vouchers', {
        withCredentials: true,
        headers: {
          'Cookie': cookieHeader
        },
        timeout: 10000
      });
      
      const apiVouchers = vouchersResponse.data;
      console.log(`📊 API returned ${apiVouchers.length} vouchers`);
      
      if (apiVouchers.length > 0) {
        console.log('\n📋 FIRST 3 API VOUCHERS DETAILED ANALYSIS:');
        apiVouchers.slice(0, 3).forEach((v, i) => {
          console.log(`${i+1}. API Voucher: ${v.voucher_id || v.voucherId}`);
          console.log(`   Raw department: ${v.department}`);
          console.log(`   Raw original_department: ${v.original_department}`);
          console.log(`   Camel originalDepartment: ${v.originalDepartment}`);
          console.log(`   Raw sent_to_audit: ${v.sent_to_audit} (${typeof v.sent_to_audit})`);
          console.log(`   Camel sentToAudit: ${v.sentToAudit} (${typeof v.sentToAudit})`);
          console.log(`   Raw status: ${v.status}`);
          console.log(`   Raw deleted: ${v.deleted} (${typeof v.deleted})`);
          console.log('   ---');
        });
        
        // Check field consistency
        console.log('\n🔍 FIELD CONSISTENCY CHECK:');
        const hasOriginalDepartment = apiVouchers.filter(v => v.originalDepartment !== undefined).length;
        const hasOriginal_department = apiVouchers.filter(v => v.original_department !== undefined).length;
        const hasSentToAudit = apiVouchers.filter(v => v.sentToAudit !== undefined).length;
        const hasSent_to_audit = apiVouchers.filter(v => v.sent_to_audit !== undefined).length;
        
        console.log(`   Vouchers with originalDepartment (camel): ${hasOriginalDepartment}`);
        console.log(`   Vouchers with original_department (snake): ${hasOriginal_department}`);
        console.log(`   Vouchers with sentToAudit (camel): ${hasSentToAudit}`);
        console.log(`   Vouchers with sent_to_audit (snake): ${hasSent_to_audit}`);
        
        // Test filtering logic on API data
        console.log('\n🧪 TESTING FILTERING LOGIC ON API DATA:');
        const apiFinanceEither = apiVouchers.filter(v => 
          v.department === 'FINANCE' || v.originalDepartment === 'FINANCE' || v.original_department === 'FINANCE'
        );
        console.log(`   API vouchers matching FINANCE filter: ${apiFinanceEither.length}`);
        
        const apiPending = apiFinanceEither.filter(v => 
          v.department === 'FINANCE' && !v.sentToAudit && !v.sent_to_audit
        );
        console.log(`   API pending vouchers: ${apiPending.length}`);
        
        const apiProcessing = apiFinanceEither.filter(v => 
          (v.originalDepartment === 'FINANCE' || v.original_department === 'FINANCE') && 
          (v.sentToAudit === true || v.sent_to_audit === true)
        );
        console.log(`   API processing vouchers: ${apiProcessing.length}`);
      }
      
    } catch (apiError) {
      console.log('❌ API test failed:', apiError.message);
      if (apiError.response) {
        console.log('   Status:', apiError.response.status);
        console.log('   Data:', apiError.response.data);
      }
    }

    // ========================================
    // 3. DATA TRANSFORMATION TRACKING
    // ========================================
    console.log('\n\n3. 🔄 DATA TRANSFORMATION TRACKING');
    console.log('-'.repeat(50));
    
    // Compare database vs API data
    if (vouchers.length > 0) {
      const dbVoucher = vouchers[0];
      console.log('📊 DATABASE → API TRANSFORMATION EXAMPLE:');
      console.log('   Database fields:');
      console.log(`     voucher_id: ${dbVoucher.voucher_id}`);
      console.log(`     department: ${dbVoucher.department}`);
      console.log(`     original_department: ${dbVoucher.original_department}`);
      console.log(`     sent_to_audit: ${dbVoucher.sent_to_audit} (${typeof dbVoucher.sent_to_audit})`);
      console.log(`     deleted: ${dbVoucher.deleted} (${typeof dbVoucher.deleted})`);
    }

    // ========================================
    // 4. FRONTEND STATE INVESTIGATION
    // ========================================
    console.log('\n\n4. 🎯 FRONTEND STATE INVESTIGATION');
    console.log('-'.repeat(50));
    console.log('   This requires browser console analysis');
    console.log('   Key things to check in browser:');
    console.log('   - Zustand store state after login');
    console.log('   - use-department-data filtering results');
    console.log('   - Component props and rendering');

    // ========================================
    // 5. SUMMARY AND RECOMMENDATIONS
    // ========================================
    console.log('\n\n5. 📋 SUMMARY AND RECOMMENDATIONS');
    console.log('-'.repeat(50));
    
    console.log(`📊 Database has ${vouchers.length} total vouchers`);
    console.log(`📊 ${financeEither.length} vouchers are FINANCE-related`);
    console.log(`📊 Expected: 1 pending + 13 processing = 14 total for FINANCE`);
    
    console.log('\n🔍 POTENTIAL ISSUES TO INVESTIGATE:');
    console.log('   1. Field name inconsistency (snake_case vs camelCase)');
    console.log('   2. Boolean type conversion issues');
    console.log('   3. Null/undefined handling in filtering logic');
    console.log('   4. Frontend state management problems');
    console.log('   5. Component rendering issues');

  } catch (error) {
    console.error('❌ Analysis error:', error);
  } finally {
    await connection.end();
  }
}

comprehensiveRootCauseAnalysis().catch(console.error);
