"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkPortAvailable = checkPortAvailable;
exports.findAvailablePort = findAvailablePort;
exports.getServerInfo = getServerInfo;
exports.broadcastServerInfo = broadcastServerInfo;
exports.saveServerInfoToNetwork = saveServerInfoToNetwork;
exports.monitorPort = monitorPort;
const net_1 = __importDefault(require("net"));
const logger_1 = require("./logger");
/**
 * Check if a port is available
 */
function checkPortAvailable(port, host = '0.0.0.0') {
    return new Promise((resolve) => {
        const server = net_1.default.createServer();
        server.listen(port, host, () => {
            server.once('close', () => {
                resolve(true);
            });
            server.close();
        });
        server.on('error', () => {
            resolve(false);
        });
    });
}
/**
 * Find an available port from a list of preferred ports
 */
async function findAvailablePort(config) {
    const { preferredPort, fallbackPorts, maxRetries } = config;
    // Try preferred port first
    logger_1.logger.info(`🔍 Checking preferred port: ${preferredPort}`);
    if (await checkPortAvailable(preferredPort)) {
        logger_1.logger.info(`✅ Preferred port ${preferredPort} is available`);
        return preferredPort;
    }
    logger_1.logger.warn(`⚠️ Preferred port ${preferredPort} is in use, trying fallback ports...`);
    // Try fallback ports
    for (const port of fallbackPorts) {
        logger_1.logger.info(`🔍 Checking fallback port: ${port}`);
        if (await checkPortAvailable(port)) {
            logger_1.logger.info(`✅ Fallback port ${port} is available`);
            return port;
        }
    }
    // Try random ports in range
    logger_1.logger.warn(`⚠️ All fallback ports in use, trying random ports...`);
    for (let i = 0; i < maxRetries; i++) {
        const randomPort = Math.floor(Math.random() * (9000 - 8000) + 8000);
        if (await checkPortAvailable(randomPort)) {
            logger_1.logger.info(`✅ Random port ${randomPort} is available`);
            return randomPort;
        }
    }
    throw new Error(`❌ Could not find available port after ${maxRetries} attempts`);
}
/**
 * Get server network information
 */
function getServerInfo(port) {
    const os = require('os');
    const networkInterfaces = os.networkInterfaces();
    // Find the primary network interface (usually the one with internet access)
    let primaryIP = 'localhost';
    for (const interfaceName in networkInterfaces) {
        const addresses = networkInterfaces[interfaceName];
        if (addresses) {
            for (const address of addresses) {
                // Skip loopback and non-IPv4 addresses
                if (!address.internal && address.family === 'IPv4') {
                    primaryIP = address.address;
                    break;
                }
            }
            if (primaryIP !== 'localhost')
                break;
        }
    }
    return {
        port,
        host: primaryIP,
        url: `http://${primaryIP}:${port}`
    };
}
/**
 * Broadcast server information for client discovery
 */
function broadcastServerInfo(serverInfo) {
    const dgram = require('dgram');
    const socket = dgram.createSocket('udp4');
    const message = JSON.stringify({
        type: 'VMS_SERVER_DISCOVERY',
        serverInfo,
        timestamp: Date.now(),
        version: '5.0.0'
    });
    // Broadcast on local network
    socket.bind(() => {
        socket.setBroadcast(true);
        // Send broadcast every 30 seconds
        const broadcastInterval = setInterval(() => {
            socket.send(message, 0, message.length, 8888, '***************', (err) => {
                if (err) {
                    logger_1.logger.error('❌ Broadcast error:', err);
                }
                else {
                    logger_1.logger.info(`📡 Broadcasting server info: ${serverInfo.url}`);
                }
            });
        }, 30000);
        // Initial broadcast
        socket.send(message, 0, message.length, 8888, '***************');
        // Cleanup on process exit
        process.on('SIGINT', () => {
            clearInterval(broadcastInterval);
            socket.close();
        });
    });
}
/**
 * Save server info to a shared network location for client discovery
 */
function saveServerInfoToNetwork(serverInfo) {
    const fs = require('fs');
    const path = require('path');
    const serverInfoFile = {
        ...serverInfo,
        lastUpdated: new Date().toISOString(),
        pid: process.pid
    };
    try {
        // Save to multiple locations for redundancy
        const locations = [
            path.join(process.cwd(), 'server-info.json'),
            path.join(require('os').homedir(), 'vms-server-info.json'),
            // Add network shared folder if available
            // 'Z:\\shared\\vms-server-info.json'
        ];
        locations.forEach(location => {
            try {
                fs.writeFileSync(location, JSON.stringify(serverInfoFile, null, 2));
                logger_1.logger.info(`💾 Server info saved to: ${location}`);
            }
            catch (err) {
                logger_1.logger.warn(`⚠️ Could not save to ${location}:`, err.message);
            }
        });
    }
    catch (error) {
        logger_1.logger.error('❌ Error saving server info:', error);
    }
}
/**
 * Monitor port and restart if needed
 */
function monitorPort(port, restartCallback) {
    const checkInterval = setInterval(async () => {
        const isAvailable = await checkPortAvailable(port);
        if (isAvailable) {
            logger_1.logger.error(`❌ Port ${port} is no longer in use by our server!`);
            clearInterval(checkInterval);
            restartCallback();
        }
    }, 60000); // Check every minute
    process.on('SIGINT', () => {
        clearInterval(checkInterval);
    });
}
//# sourceMappingURL=port-manager.js.map