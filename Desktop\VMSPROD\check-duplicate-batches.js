const mysql = require('mysql2/promise');

async function checkDuplicateBatches() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root', 
    password: 'vms@2025@1989',
    database: 'vms_production'
  });
  
  console.log('🔍 CHECKING FOR DUPLICATE BATCHES');
  console.log('==================================');
  
  // Check all batches sent to FINANCE department
  const [batches] = await connection.execute(`
    SELECT 
      id, 
      department, 
      sent_by, 
      sent_time, 
      received, 
      from_audit,
      (SELECT COUNT(*) FROM batch_vouchers WHERE batch_id = voucher_batches.id) as voucher_count
    FROM voucher_batches 
    WHERE department = 'FINANCE' 
    AND from_audit = TRUE
    ORDER BY sent_time DESC
    LIMIT 10
  `);
  
  console.log(`📊 Recent batches sent to FINANCE from audit: ${batches.length}`);
  
  if (batches.length > 0) {
    console.log('\n📋 BATCH DETAILS:');
    console.log('------------------');
    
    batches.forEach((batch, index) => {
      const sentTime = new Date(batch.sent_time).toLocaleString();
      console.log(`${index + 1}. Batch ID: ${batch.id}`);
      console.log(`   Department: ${batch.department}`);
      console.log(`   Sent By: ${batch.sent_by}`);
      console.log(`   Sent Time: ${sentTime}`);
      console.log(`   Received: ${batch.received ? 'YES' : 'NO'}`);
      console.log(`   From Audit: ${batch.from_audit ? 'YES' : 'NO'}`);
      console.log(`   Voucher Count: ${batch.voucher_count}`);
      console.log('');
    });
    
    // Check for potential duplicates (same vouchers in multiple batches)
    console.log('\n🔍 CHECKING FOR DUPLICATE VOUCHERS:');
    console.log('------------------------------------');
    
    for (const batch of batches) {
      const [voucherIds] = await connection.execute(`
        SELECT voucher_id 
        FROM batch_vouchers 
        WHERE batch_id = ?
      `, [batch.id]);
      
      console.log(`Batch ${batch.id}: ${voucherIds.map(v => v.voucher_id).join(', ')}`);
    }
    
    // Check if our test voucher appears in multiple batches
    console.log('\n🎯 CHECKING TEST VOUCHER FINJUL0001:');
    console.log('------------------------------------');
    
    const [testVoucherBatches] = await connection.execute(`
      SELECT 
        vb.id as batch_id,
        vb.department,
        vb.sent_by,
        vb.sent_time,
        vb.received,
        vb.from_audit
      FROM voucher_batches vb
      JOIN batch_vouchers bv ON vb.id = bv.batch_id
      JOIN vouchers v ON bv.voucher_id = v.id
      WHERE v.voucher_id = 'FINJUL0001'
      ORDER BY vb.sent_time DESC
    `);
    
    console.log(`📊 Batches containing FINJUL0001: ${testVoucherBatches.length}`);
    
    if (testVoucherBatches.length > 1) {
      console.log('🚨 DUPLICATE BATCH ISSUE CONFIRMED!');
      console.log('FINJUL0001 appears in multiple batches:');
      
      testVoucherBatches.forEach((batch, index) => {
        const sentTime = new Date(batch.sent_time).toLocaleString();
        console.log(`   ${index + 1}. Batch ${batch.batch_id} - Sent: ${sentTime} - Received: ${batch.received ? 'YES' : 'NO'}`);
      });
    } else if (testVoucherBatches.length === 1) {
      console.log('✅ No duplicate batches found for FINJUL0001');
    } else {
      console.log('⚠️ FINJUL0001 not found in any batches');
    }
    
  } else {
    console.log('❌ No batches found sent to FINANCE from audit');
  }
  
  await connection.end();
}

checkDuplicateBatches().catch(console.error);
