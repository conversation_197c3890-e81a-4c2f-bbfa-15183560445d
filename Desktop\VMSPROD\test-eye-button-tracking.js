const mysql = require('mysql2/promise');

async function testEyeButtonTracking() {
  console.log('🧪 Testing Eye Button Details and Tracking Flow');
  console.log('='.repeat(50));

  let connection;
  
  try {
    // Connect to database
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'vms@2025@1989',
      database: 'vms_production'
    });
    
    console.log('✅ Connected to database');

    // Step 1: Check vouchers with tracking information
    console.log('\n1. VOUCHERS WITH TRACKING INFORMATION:');
    console.log('-'.repeat(40));
    
    const [trackingVouchers] = await connection.execute(`
      SELECT 
        voucher_id, 
        status, 
        department, 
        original_department,
        reference_id,
        created_by,
        dispatch_to_audit_by,
        received_by,
        pre_audited_by,
        certified_by,
        audit_dispatched_by,
        department_received_by,
        comment
      FROM vouchers 
      WHERE status IN ('VOUCHER CERTIFIED', 'VOUCHER PROCESSING', 'VOUCHER REJECTED', 'AUDIT: PROCESSING')
      ORDER BY voucher_id
      LIMIT 10
    `);

    if (trackingVouchers.length === 0) {
      console.log('📋 No vouchers with tracking information found');
      console.log('   Create some vouchers and process them through the workflow to test tracking');
    } else {
      console.log(`📋 Found ${trackingVouchers.length} vouchers with tracking information:`);
      trackingVouchers.forEach((voucher, index) => {
        console.log(`   ${index + 1}. ${voucher.voucher_id} (${voucher.status})`);
        console.log(`      Department: ${voucher.department} | Original: ${voucher.original_department || 'N/A'}`);
        console.log(`      Reference ID: ${voucher.reference_id || 'N/A'}`);
        console.log(`      Created By: ${voucher.created_by || 'N/A'}`);
        console.log(`      Sent to Audit By: ${voucher.dispatch_to_audit_by || 'N/A'}`);
        console.log(`      Received by Audit: ${voucher.received_by || 'N/A'}`);
        console.log(`      Pre-audited By: ${voucher.pre_audited_by || 'N/A'}`);
        console.log(`      Certified By: ${voucher.certified_by || 'N/A'}`);
        console.log(`      Audit Dispatched By: ${voucher.audit_dispatched_by || 'N/A'}`);
        console.log(`      Dept Received By: ${voucher.department_received_by || 'N/A'}`);
        console.log(`      Comments: ${voucher.comment || 'N/A'}`);
        console.log('      ---');
      });
    }

    // Step 2: Check vouchers with complete workflow tracking
    console.log('\n2. COMPLETE WORKFLOW TRACKING ANALYSIS:');
    console.log('-'.repeat(40));
    
    const [completeWorkflow] = await connection.execute(`
      SELECT 
        voucher_id,
        status,
        CASE 
          WHEN created_by IS NOT NULL THEN '✅' ELSE '❌' 
        END as has_creator,
        CASE 
          WHEN dispatch_to_audit_by IS NOT NULL THEN '✅' ELSE '❌' 
        END as has_dispatch_to_audit,
        CASE 
          WHEN received_by IS NOT NULL THEN '✅' ELSE '❌' 
        END as has_audit_receipt,
        CASE 
          WHEN pre_audited_by IS NOT NULL THEN '✅' ELSE '❌' 
        END as has_audit_processing,
        CASE 
          WHEN audit_dispatched_by IS NOT NULL THEN '✅' ELSE '❌' 
        END as has_audit_dispatch,
        CASE 
          WHEN department_received_by IS NOT NULL THEN '✅' ELSE '❌' 
        END as has_dept_receipt,
        CASE 
          WHEN reference_id IS NOT NULL THEN '✅' ELSE '❌' 
        END as has_reference_tracking
      FROM vouchers 
      WHERE status IN ('VOUCHER CERTIFIED', 'VOUCHER PROCESSING', 'VOUCHER REJECTED')
      ORDER BY voucher_id
      LIMIT 5
    `);

    if (completeWorkflow.length === 0) {
      console.log('📊 No completed workflow vouchers found');
    } else {
      console.log('📊 Workflow Tracking Completeness:');
      console.log('   Voucher ID | Status | Creator | To Audit | Audit Receipt | Audit Process | Audit Dispatch | Dept Receipt | Reference');
      console.log('   ' + '-'.repeat(120));
      completeWorkflow.forEach(voucher => {
        const row = `   ${voucher.voucher_id.padEnd(10)} | ${voucher.status.padEnd(16)} | ${voucher.has_creator.padEnd(7)} | ${voucher.has_dispatch_to_audit.padEnd(8)} | ${voucher.has_audit_receipt.padEnd(13)} | ${voucher.has_audit_processing.padEnd(13)} | ${voucher.has_audit_dispatch.padEnd(14)} | ${voucher.has_dept_receipt.padEnd(12)} | ${voucher.has_reference_tracking}`;
        console.log(row);
      });
    }

    // Step 3: Check offset tracking relationships
    console.log('\n3. OFFSET TRACKING RELATIONSHIPS:');
    console.log('-'.repeat(40));
    
    const [offsetRelationships] = await connection.execute(`
      SELECT 
        v1.voucher_id as processed_voucher,
        v1.status as processed_status,
        v1.reference_id as original_voucher_id,
        v2.voucher_id as found_original,
        v2.status as original_status,
        v2.deleted as original_deleted
      FROM vouchers v1
      LEFT JOIN vouchers v2 ON v1.reference_id = v2.voucher_id
      WHERE v1.reference_id IS NOT NULL
      ORDER BY v1.voucher_id
    `);

    if (offsetRelationships.length === 0) {
      console.log('🔗 No offset tracking relationships found');
      console.log('   This means no vouchers have been processed with the new offset logic yet');
    } else {
      console.log('🔗 Offset Tracking Relationships:');
      offsetRelationships.forEach((rel, index) => {
        console.log(`   ${index + 1}. Processed: ${rel.processed_voucher} (${rel.processed_status})`);
        console.log(`      References: ${rel.original_voucher_id}`);
        console.log(`      Original Found: ${rel.found_original ? 'YES' : 'NO'}`);
        if (rel.found_original) {
          console.log(`      Original Status: ${rel.original_status}`);
          console.log(`      Original Deleted: ${rel.original_deleted ? 'YES' : 'NO'}`);
        }
        console.log('      ---');
      });
    }

    console.log('\n4. EYE BUTTON TRACKING FEATURES:');
    console.log('-'.repeat(40));
    console.log('✅ ENHANCED TRACKING INFORMATION:');
    console.log('   1. Workflow Status & Timeline');
    console.log('      - Current status with workflow stage description');
    console.log('      - Completion indicators for processed vouchers');
    
    console.log('\n   2. Offset Tracking Information');
    console.log('      - Reference ID linking to original voucher');
    console.log('      - Clear indication of audit processing workflow');
    console.log('      - Explanation of offset logic for users');
    
    console.log('\n   3. Enhanced Audit Processing Details');
    console.log('      - Detailed certification information');
    console.log('      - Amount comparison (original vs certified)');
    console.log('      - Audit comments and processing notes');
    
    console.log('\n   4. Complete Department Receipt Information');
    console.log('      - Final workflow completion status');
    console.log('      - Clear indication of voucher replacement');
    console.log('      - Comprehensive tracking timeline');

    console.log('\n🎯 SUMMARY:');
    console.log('   Eye button details now show comprehensive tracking information');
    console.log('   that respects the updated workflow logic including:');
    console.log('   - Offset relationships between original and processed vouchers');
    console.log('   - Complete audit processing timeline');
    console.log('   - Enhanced workflow status indicators');
    console.log('   - Clear explanations for users about voucher replacements');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n✅ Database connection closed');
    }
  }
}

testEyeButtonTracking();
