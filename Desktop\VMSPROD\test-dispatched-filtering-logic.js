const mysql = require('mysql2/promise');

async function testDispatchedFilteringLogic() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root', 
    password: 'vms@2025@1989',
    database: 'vms_production'
  });
  
  console.log('🧪 TESTING DISPATCHED TAB FILTERING LOGIC');
  console.log('==========================================');
  
  // Step 1: Get raw database data
  console.log('\n1. RAW DATABASE DATA:');
  console.log('---------------------');
  
  const [dbVouchers] = await connection.execute(`
    SELECT voucher_id, department, original_department, status, dispatched, audit_dispatched_by
    FROM vouchers 
    WHERE original_department = 'FINANCE'
    ORDER BY voucher_id
  `);
  
  console.log(`📊 FINANCE vouchers in database: ${dbVouchers.length}`);
  dbVouchers.forEach((voucher, index) => {
    console.log(`   ${index + 1}. ${voucher.voucher_id}:`);
    console.log(`      original_department: ${voucher.original_department} (${typeof voucher.original_department})`);
    console.log(`      dispatched: ${voucher.dispatched} (${typeof voucher.dispatched})`);
    console.log(`      audit_dispatched_by: ${voucher.audit_dispatched_by || 'NULL'}`);
  });
  
  // Step 2: Simulate backend transformation
  console.log('\n2. BACKEND TRANSFORMATION:');
  console.log('---------------------------');
  
  const transformedVouchers = dbVouchers.map(voucher => ({
    ...voucher,
    voucherId: voucher.voucher_id,
    originalDepartment: voucher.original_department,
    dispatched: Boolean(voucher.dispatched), // Backend transformation
    auditDispatchedBy: voucher.audit_dispatched_by
  }));
  
  console.log('📊 After backend transformation:');
  transformedVouchers.forEach((voucher, index) => {
    console.log(`   ${index + 1}. ${voucher.voucherId}:`);
    console.log(`      originalDepartment: ${voucher.originalDepartment} (${typeof voucher.originalDepartment})`);
    console.log(`      dispatched: ${voucher.dispatched} (${typeof voucher.dispatched})`);
  });
  
  // Step 3: Simulate frontend normalization
  console.log('\n3. FRONTEND NORMALIZATION:');
  console.log('---------------------------');
  
  const normalizedVouchers = transformedVouchers.map(voucher => ({
    ...voucher,
    voucherId: voucher.voucherId || voucher.voucher_id,
    originalDepartment: voucher.originalDepartment || voucher.original_department,
    dispatched: Boolean(voucher.dispatched), // Frontend normalization (FIXED)
  }));
  
  console.log('📊 After frontend normalization:');
  normalizedVouchers.forEach((voucher, index) => {
    console.log(`   ${index + 1}. ${voucher.voucherId}:`);
    console.log(`      originalDepartment: ${voucher.originalDepartment} (${typeof voucher.originalDepartment})`);
    console.log(`      dispatched: ${voucher.dispatched} (${typeof voucher.dispatched})`);
  });
  
  // Step 4: Test filtering logic
  console.log('\n4. FILTERING LOGIC TEST:');
  console.log('------------------------');
  
  const department = 'FINANCE';
  const filteredVouchers = normalizedVouchers.filter(v => 
    v.originalDepartment === department && v.dispatched === true
  );
  
  console.log(`🔍 Filter: originalDepartment === '${department}' && dispatched === true`);
  console.log(`📊 Filtered result: ${filteredVouchers.length} vouchers`);
  
  if (filteredVouchers.length > 0) {
    console.log('✅ DISPATCHED tab should show these vouchers:');
    filteredVouchers.forEach((voucher, index) => {
      console.log(`   ${index + 1}. ${voucher.voucherId} - Dispatched by: ${voucher.auditDispatchedBy || 'N/A'}`);
    });
  } else {
    console.log('❌ No vouchers would appear in DISPATCHED tab');
    
    // Debug why filtering failed
    console.log('\n🔍 DEBUGGING FILTER FAILURE:');
    normalizedVouchers.forEach((voucher, index) => {
      const origDeptMatch = voucher.originalDepartment === department;
      const dispatchedMatch = voucher.dispatched === true;
      
      console.log(`   ${index + 1}. ${voucher.voucherId}:`);
      console.log(`      originalDepartment === '${department}': ${origDeptMatch}`);
      console.log(`      dispatched === true: ${dispatchedMatch}`);
      console.log(`      Both conditions: ${origDeptMatch && dispatchedMatch}`);
    });
  }
  
  // Step 5: Test exact use-voucher-tabs.ts logic
  console.log('\n5. EXACT FRONTEND HOOK LOGIC:');
  console.log('------------------------------');
  
  // Simulate the exact filtering from use-voucher-tabs.ts
  const dispatchedVouchersFromHook = normalizedVouchers.filter(v =>
    v.originalDepartment === department &&
    v.dispatched === true
  );
  
  console.log(`🔍 use-voucher-tabs.ts filter result: ${dispatchedVouchersFromHook.length} vouchers`);
  
  if (dispatchedVouchersFromHook.length > 0) {
    console.log('✅ Frontend hook should work correctly');
  } else {
    console.log('❌ Frontend hook would return empty array');
  }
  
  console.log('\n📋 SUMMARY:');
  console.log('-----------');
  console.log(`Database vouchers: ${dbVouchers.length}`);
  console.log(`Dispatched vouchers: ${filteredVouchers.length}`);
  console.log(`Expected DISPATCHED tab count: ${filteredVouchers.length}`);
  
  await connection.end();
}

testDispatchedFilteringLogic().catch(console.error);
