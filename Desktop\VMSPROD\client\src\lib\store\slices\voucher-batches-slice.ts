
import { StateCreator } from 'zustand';
import { AppState } from '../types';
import { formatCurrentDate } from '../utils';

export interface VoucherBatchesSlice {
  voucherBatches: AppState['voucherBatches'];
  fetchBatches: () => Promise<void>;
  createVoucherBatch: AppState['createVoucherBatch'];
  receiveVoucherBatch: AppState['receiveVoucherBatch'];
}

export const createVoucherBatchesSlice: StateCreator<AppState, [], [], VoucherBatchesSlice> = (set, get) => ({
  voucherBatches: [],

  // Fetch batches from API and sync with local state
  fetchBatches: async () => {
    try {
      console.log('🔄 Starting fetchBatches...');

      // FIXED: Check for current user instead of token
      const currentUser = get().currentUser;
      if (!currentUser) {
        console.log('❌ No current user found for fetchBatches');
        return;
      }

      console.log('🔄 Making API call to /api/batches...');
      const response = await fetch('/api/batches', {
        credentials: 'include' // FIXED: Use session cookies
      });

      console.log('🔄 API response status:', response.status);

      if (response.ok) {
        const batches = await response.json();
        console.log('✅ Fetched batches from API:', batches);
        console.log('✅ Number of batches fetched:', batches.length);

        // Convert API batches to local format
        const localBatches = batches.map((batch: any) => ({
          id: batch.id,
          department: batch.department,
          voucherIds: batch.voucherIds || [],
          vouchers: batch.vouchers || [],  // CRITICAL FIX: Include vouchers array
          sentBy: batch.sent_by,
          sentTime: batch.sent_time,
          received: batch.received,
          fromAudit: batch.from_audit
        }));

        console.log('✅ Converted to local format:', localBatches);
        set({ voucherBatches: localBatches });
        console.log('✅ Updated store with batches');
      } else {
        console.error('❌ API response not ok:', response.status, response.statusText);
        const errorText = await response.text();
        console.error('❌ Error response body:', errorText);
      }
    } catch (error) {
      console.error('❌ Error fetching batches:', error);
    }
  },
  createVoucherBatch: () => {
    // PRODUCTION: Local batch creation is disabled to prevent data inconsistency
    // All batch creation must go through the API to ensure database persistence
    throw new Error('Local batch creation is disabled. Use API-based batch creation only.');
  },
  receiveVoucherBatch: async (batchId, receivedVoucherIds, rejectedVoucherIds, rejectionComments = {}) => {
    console.log(`Receiving batch ${batchId} - Accepted: ${receivedVoucherIds.length}, Rejected: ${rejectedVoucherIds.length}`);

    // FIXED: Check for current user instead of token
    const currentUser = get().currentUser;
    if (!currentUser) {
      throw new Error('Authentication required. Please log in again.');
    }

    try {
      // FIXED: Use API to receive batch in database with session auth
      const response = await fetch(`/api/batches/${batchId}/receive`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
          // REMOVED: Authorization header - using session cookies
        },
        credentials: 'include', // FIXED: Include session cookies
        body: JSON.stringify({
          receivedVoucherIds,
          rejectedVoucherIds,
          rejectionComments
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Batch receiving failed: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const result = await response.json();
      console.log('✅ Batch received successfully via API:', result);

      // Update local state to match database
      const batch = get().voucherBatches.find(b => b.id === batchId);
      if (batch) {
        set((state) => ({
          voucherBatches: state.voucherBatches.map(b =>
            b.id === batchId ? { ...b, received: true } : b
          )
        }));
      }

      const currentUser = get().currentUser;
      const currentTime = formatCurrentDate();

      // ARCHITECTURAL FIX: Skip frontend voucher updates - backend handles all updates
      // The backend batch receiving endpoint already updates voucher statuses, departments, and all fields
      // Frontend updates cause 403 errors because vouchers are in different departments during transition
      console.log('🔄 BATCH RECEIVING: Backend handles all voucher updates - skipping frontend updates to prevent 403 errors');

      // Log what would have been updated for debugging
      receivedVoucherIds.forEach(id => {
        const voucher = get().vouchers.find(v => v.id === id);
        if (voucher) {
          if (batch?.fromAudit) {
            console.log(`📄 Voucher ${voucher.voucherId}: Backend will update status and transfer back to ${voucher.originalDepartment || 'original department'}`);
          } else {
            console.log(`📄 Voucher ${voucher.voucherId}: Backend will transfer to AUDIT and set status to AUDIT: PROCESSING`);
          }
        }
      });

      // ARCHITECTURAL FIX: Skip frontend updates for rejected vouchers too - backend handles all updates
      rejectedVoucherIds.forEach(id => {
        const voucher = get().vouchers.find(v => v.id === id);
        if (voucher) {
          const rejectionComment = rejectionComments[id] || "Rejected during batch receiving";
          console.log(`📄 Voucher ${voucher.voucherId}: Backend will reject with comment: ${rejectionComment}`);

          // Only update local blacklist - backend handles voucher status updates
          get().addBlacklistedVoucherId(voucher.voucherId);

          // Notify department (this doesn't require voucher updates)
          const departmentUser = get().users.find(u => u.department === voucher.originalDepartment || voucher.department);
          if (departmentUser) {
            get().addNotification({
              userId: departmentUser.id,
              message: `VOUCHER ${voucher.voucherId} REJECTED BY AUDIT: ${rejectionComment}`,
              voucherId: id,
              type: "VOUCHER_REJECTED"
            });
          }
        }
      });

      // CRITICAL FIX: Mark processed vouchers as "batch processed" to prevent duplicate dispatch
      console.log('🔄 AUDIT: Marking vouchers as batch processed to prevent duplicate dispatch...');
      const allProcessedVoucherIds = [...receivedVoucherIds, ...rejectedVoucherIds];
      allProcessedVoucherIds.forEach(voucherId => {
        get().updateVoucher(voucherId, {
          batchProcessed: true, // Flag to prevent duplicate dispatch
          batchProcessedTime: new Date().toISOString()
        });
      });

      // CRITICAL FIX: Refresh vouchers after batch processing to update department hubs
      console.log('🔄 AUDIT: Refreshing vouchers after batch processing to update department hubs...');

      // Add delay to ensure database transaction has committed
      console.log('⏳ AUDIT: Waiting 2 seconds for database transaction to commit...');
      await new Promise(resolve => setTimeout(resolve, 2000));

      // CRITICAL FIX: Always fetch ALL vouchers so departments can see vouchers sent to audit
      console.log('🔄 🚀🚀🚀 CACHE BUSTER: AUDIT batch processing - fetching ALL vouchers');
      await get().fetchVouchers(); // Always fetch all vouchers
      console.log('✅ AUDIT: Vouchers refreshed after batch processing');

      console.log(`✅ Successfully received batch ${batchId}`);

    } catch (error) {
      console.error('❌ Failed to receive batch:', error);
      throw error;
    }
  },
});
