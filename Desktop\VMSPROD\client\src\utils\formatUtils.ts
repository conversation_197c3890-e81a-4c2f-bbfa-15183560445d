/**
 * ========================================
 * VMS SINGLE DATE FORMAT STANDARD (FORMAT 3)
 * ========================================
 *
 * RULE: The entire VMS system uses ONLY this format:
 * DD-MMM-YYYY HH:MM:SS AM/PM
 * Example: 11-JUL-2025 10:46:23 PM
 *
 * This is the ONLY date formatting used throughout the system.
 * All other date functions are deprecated.
 */

/**
 * Creates current timestamp in VMS Format 3
 * Returns: DD-MMM-YYYY HH:MM:SS AM/PM
 */
export const formatCurrentDate = (): string => {
  const now = new Date();
  const day = now.getDate().toString().padStart(2, '0');
  const months = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN',
                 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
  const month = months[now.getMonth()];
  const year = now.getFullYear();

  let hours = now.getHours();
  const minutes = now.getMinutes().toString().padStart(2, '0');
  const seconds = now.getSeconds().toString().padStart(2, '0');
  const ampm = hours >= 12 ? 'PM' : 'AM';

  hours = hours % 12;
  hours = hours ? hours : 12;
  const hoursStr = hours.toString().padStart(2, '0');

  return `${day}-${month}-${year} ${hoursStr}:${minutes}:${seconds} ${ampm}`;
};

/**
 * Formats any date input to VMS Format 3
 * Input: Any date string, Date object, or timestamp
 * Output: DD-MMM-YYYY HH:MM:SS AM/PM
 */
export const formatVMSDateTime = (dateInput: string | Date | null | undefined): string => {
  if (!dateInput) return '-';

  try {
    const date = typeof dateInput === 'string' ? new Date(dateInput) : dateInput;

    if (isNaN(date.getTime())) return '-';

    const day = date.getDate().toString().padStart(2, '0');
    const months = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN',
                   'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
    const month = months[date.getMonth()];
    const year = date.getFullYear();

    let hours = date.getHours();
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');
    const ampm = hours >= 12 ? 'PM' : 'AM';

    hours = hours % 12;
    hours = hours ? hours : 12;
    const hoursStr = hours.toString().padStart(2, '0');

    return `${day}-${month}-${year} ${hoursStr}:${minutes}:${seconds} ${ampm}`;
  } catch (error) {
    console.error('Error formatting date:', error);
    return '-';
  }
};

/**
 * Date only version of Format 3 (without time)
 * Output: DD-MMM-YYYY
 */
export const formatVMSDate = (dateInput: string | Date | null | undefined): string => {
  if (!dateInput) return '-';

  try {
    const date = typeof dateInput === 'string' ? new Date(dateInput) : dateInput;

    if (isNaN(date.getTime())) return '-';

    const day = date.getDate().toString().padStart(2, '0');
    const months = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN',
                   'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
    const month = months[date.getMonth()];
    const year = date.getFullYear();

    return `${day}-${month}-${year}`;
  } catch (error) {
    console.error('Error formatting date:', error);
    return '-';
  }
};

/**
 * Legacy function - redirects to Format 3 standard
 */
export const formatDate = (dateString: string | undefined): string => {
  return formatVMSDateTime(dateString);
};

/**
 * Currency formatting
 */
export const formatCurrency = (amount: number, currency: string = 'USD') => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency
  }).format(amount);
};

/**
 * Number formatting with commas
 */
export const formatNumberWithCommas = (amount: number | undefined) => {
  if (amount === undefined) return '-';
  return new Intl.NumberFormat('en-US', {
    maximumFractionDigits: 2,
    minimumFractionDigits: 2
  }).format(amount);
};

/**
 * Time only version of Format 3
 * Output: HH:MM:SS AM/PM
 */
export const formatVMSTime = (dateInput: string | Date | null | undefined): string => {
  if (!dateInput) return '-';

  try {
    const date = typeof dateInput === 'string' ? new Date(dateInput) : dateInput;

    if (isNaN(date.getTime())) return '-';

    let hours = date.getHours();
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');
    const ampm = hours >= 12 ? 'PM' : 'AM';

    hours = hours % 12;
    hours = hours ? hours : 12;
    const hoursStr = hours.toString().padStart(2, '0');

    return `${hoursStr}:${minutes}:${seconds} ${ampm}`;
  } catch (error) {
    console.error('Error formatting time:', error);
    return '-';
  }
};
