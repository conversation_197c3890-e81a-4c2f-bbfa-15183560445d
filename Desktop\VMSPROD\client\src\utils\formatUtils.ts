
/**
 * SINGLE STANDARD DATE FORMAT FOR ENTIRE VMS SYSTEM
 * Creates ISO 8601 timestamp for database storage
 */
export const createVMSTimestamp = (): string => {
  return new Date().toISOString();
};

/**
 * SINGLE STANDARD DATE DISPLAY FORMAT FOR ENTIRE VMS SYSTEM
 * Converts any date input to: DD-MMM-YYYY HH:MM:SS AM/PM
 * Example: 11-JUL-2025 10:46:23 PM
 */
export const formatVMSDateTime = (dateInput: string | Date | null | undefined): string => {
  if (!dateInput) return '-';

  try {
    const date = typeof dateInput === 'string' ? new Date(dateInput) : dateInput;

    if (isNaN(date.getTime())) return '-';

    const day = date.getDate().toString().padStart(2, '0');
    const months = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN',
                   'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
    const month = months[date.getMonth()];
    const year = date.getFullYear();

    let hours = date.getHours();
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');
    const ampm = hours >= 12 ? 'PM' : 'AM';

    hours = hours % 12;
    hours = hours ? hours : 12;
    const hoursStr = hours.toString().padStart(2, '0');

    return `${day}-${month}-${year} ${hoursStr}:${minutes}:${seconds} ${ampm}`;
  } catch (error) {
    console.error('Error formatting date:', error);
    return '-';
  }
};

/**
 * LEGACY FUNCTION - Use formatVMSDateTime instead
 * @deprecated Use formatVMSDateTime for consistent formatting
 */
export const formatDate = (dateString: string | undefined) => {
  return formatVMSDateTime(dateString);
};

export const formatCurrency = (amount: number, currency: string = 'USD') => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency
  }).format(amount);
};

/**
 * Format date and time in VMS standard format: DD-MMM-YYYY HH:MM:SS AM/PM
 * Example: 11-JUL-2025 10:46:23 PM
 */
export const formatVMSDateTime = (dateInput: string | Date | null | undefined): string => {
  if (!dateInput) return 'Invalid Date';

  try {
    let date: Date;

    if (typeof dateInput === 'string') {
      // Handle the actual server format: "JUL 11, 2025 AT 10:46PM" (3-letter month, no space before AM/PM)
      const serverFormatMatch = dateInput.match(/(\w{3})\s+(\d{1,2}),\s+(\d{4})\s+AT\s+(\d{1,2}):(\d{2})(AM|PM)/i);
      if (serverFormatMatch) {
        const [, monthName, day, year, hour, minute, ampm] = serverFormatMatch;
        const monthMap: { [key: string]: number } = {
          'JAN': 0, 'FEB': 1, 'MAR': 2, 'APR': 3, 'MAY': 4, 'JUN': 5,
          'JUL': 6, 'AUG': 7, 'SEP': 8, 'OCT': 9, 'NOV': 10, 'DEC': 11
        };
        const monthIndex = monthMap[monthName.toUpperCase()];
        if (monthIndex !== undefined) {
          let hour24 = parseInt(hour);
          if (ampm.toUpperCase() === 'PM' && hour24 !== 12) hour24 += 12;
          if (ampm.toUpperCase() === 'AM' && hour24 === 12) hour24 = 0;

          date = new Date(parseInt(year), monthIndex, parseInt(day), hour24, parseInt(minute));
        } else {
          date = new Date(dateInput);
        }
      } else {
        // Try standard JavaScript Date parsing as fallback
        date = new Date(dateInput);
      }
    } else {
      date = dateInput;
    }

    if (isNaN(date.getTime())) return 'Invalid Date';

    const day = date.getDate().toString().padStart(2, '0');
    const months = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN',
                   'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
    const month = months[date.getMonth()];
    const year = date.getFullYear();

    // Format time in 12-hour format
    let hours = date.getHours();
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');
    const ampm = hours >= 12 ? 'PM' : 'AM';

    hours = hours % 12;
    hours = hours ? hours : 12; // 0 should be 12
    const hoursStr = hours.toString().padStart(2, '0');

    return `${day}-${month}-${year} ${hoursStr}:${minutes}:${seconds} ${ampm}`;
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid Date';
  }
};

/**
 * Format date only in VMS standard format: DD-MMM-YYYY
 * Example: 11-JUL-2025
 */
export const formatVMSDate = (dateInput: string | Date | null | undefined): string => {
  if (!dateInput) return 'Invalid Date';

  try {
    let date: Date;

    if (typeof dateInput === 'string') {
      // Handle the actual server format: "JUL 11, 2025 AT 10:46PM" (extract just the date part)
      const serverFormatMatch = dateInput.match(/(\w{3})\s+(\d{1,2}),\s+(\d{4})/i);
      if (serverFormatMatch) {
        const [, monthName, day, year] = serverFormatMatch;
        const monthMap: { [key: string]: number } = {
          'JAN': 0, 'FEB': 1, 'MAR': 2, 'APR': 3, 'MAY': 4, 'JUN': 5,
          'JUL': 6, 'AUG': 7, 'SEP': 8, 'OCT': 9, 'NOV': 10, 'DEC': 11
        };
        const monthIndex = monthMap[monthName.toUpperCase()];
        if (monthIndex !== undefined) {
          date = new Date(parseInt(year), monthIndex, parseInt(day));
        } else {
          date = new Date(dateInput);
        }
      } else {
        // Try standard JavaScript Date parsing as fallback
        date = new Date(dateInput);
      }
    } else {
      date = dateInput;
    }

    if (isNaN(date.getTime())) return 'Invalid Date';

    const day = date.getDate().toString().padStart(2, '0');
    const months = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN',
                   'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
    const month = months[date.getMonth()];
    const year = date.getFullYear();

    return `${day}-${month}-${year}`;
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid Date';
  }
};

/**
 * Format time only in VMS standard format: HH:MM:SS AM/PM
 * Example: 10:46:23 PM
 */
export const formatVMSTime = (dateInput: string | Date | null | undefined): string => {
  if (!dateInput) return 'N/A';

  try {
    const date = typeof dateInput === 'string' ? new Date(dateInput) : dateInput;

    if (isNaN(date.getTime())) return 'Invalid Time';

    let hours = date.getHours();
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');
    const ampm = hours >= 12 ? 'PM' : 'AM';

    hours = hours % 12;
    hours = hours ? hours : 12; // 0 should be 12
    const hoursStr = hours.toString().padStart(2, '0');

    return `${hoursStr}:${minutes}:${seconds} ${ampm}`;
  } catch (error) {
    console.error('Error formatting time:', error);
    return 'Invalid Time';
  }
};

export const formatNumberWithCommas = (amount: number | undefined, maxDecimals: number = 3) => {
  if (amount === undefined) return '-';
  return new Intl.NumberFormat('en-US', {
    maximumFractionDigits: maxDecimals,
    minimumFractionDigits: 0
  }).format(amount);
};
