const mysql = require('mysql2/promise');
const axios = require('axios');

async function testDuplicateNotificationFix() {
  console.log('🧪 TESTING COMPREHENSIVE DUPLICATE NOTIFICATION FIX');
  console.log('=' .repeat(60));

  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });

  try {
    // Clean up any existing test data
    console.log('🧹 Cleaning up test data...');
    await connection.execute('DELETE FROM notifications WHERE message LIKE "%test%"');
    await connection.execute('DELETE FROM batch_vouchers WHERE batch_id IN (SELECT id FROM voucher_batches WHERE department = "FINANCE" AND sent_time > DATE_SUB(NOW(), INTERVAL 1 HOUR))');
    await connection.execute('DELETE FROM voucher_batches WHERE department = "FINANCE" AND sent_time > DATE_SUB(NOW(), INTERVAL 1 HOUR)');
    await connection.execute('DELETE FROM vouchers WHERE voucher_id LIKE "TEST-%"');

    // Create a test voucher
    console.log('📄 Creating test voucher...');
    const testVoucherId = 'TEST-' + Date.now();
    const voucherUuid = require('crypto').randomUUID();
    
    await connection.execute(`
      INSERT INTO vouchers (
        id, voucher_id, date, claimant, description, amount, currency, 
        department, original_department, status, sent_to_audit, created_by
      ) VALUES (?, ?, CURDATE(), 'Test Claimant', 'Test voucher for duplicate prevention', 
        1000.00, 'GHS', 'FINANCE', 'FINANCE', 'PENDING RECEIPT', TRUE, 'AUDIT')
    `, [voucherUuid, testVoucherId]);

    console.log(`✅ Created test voucher: ${testVoucherId}`);

    // Count notifications before batch creation
    const [beforeNotifications] = await connection.execute(
      'SELECT COUNT(*) as count FROM notifications WHERE user_id = "FINANCE" AND type = "NEW_BATCH"'
    );
    const notificationsBefore = beforeNotifications[0].count;
    console.log(`📊 Notifications before: ${notificationsBefore}`);

    // Test 1: Create batch from audit to finance (this was causing duplicates)
    console.log('\n🧪 TEST 1: Creating batch from AUDIT to FINANCE...');
    
    const batchResponse = await axios.post('http://localhost:8080/api/batches', {
      department: 'FINANCE',
      voucherIds: [voucherUuid],
      fromAudit: true
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Cookie': 'session=test-audit-session'
      }
    });

    console.log(`✅ Batch created successfully: ${batchResponse.data.id}`);

    // Wait a moment for any async operations
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Count notifications after batch creation
    const [afterNotifications] = await connection.execute(
      'SELECT COUNT(*) as count FROM notifications WHERE user_id = "FINANCE" AND type = "NEW_BATCH"'
    );
    const notificationsAfter = afterNotifications[0].count;
    console.log(`📊 Notifications after: ${notificationsAfter}`);

    const notificationIncrease = notificationsAfter - notificationsBefore;
    console.log(`📈 Notification increase: ${notificationIncrease}`);

    // Test result
    if (notificationIncrease === 1) {
      console.log('✅ SUCCESS: Only ONE notification created (duplicate prevention working!)');
    } else if (notificationIncrease === 2) {
      console.log('❌ FAILURE: TWO notifications created (duplicates still occurring)');
    } else {
      console.log(`⚠️ UNEXPECTED: ${notificationIncrease} notifications created`);
    }

    // Test 2: Try to create the same batch again (should be prevented)
    console.log('\n🧪 TEST 2: Attempting to create duplicate batch...');
    
    try {
      const duplicateBatchResponse = await axios.post('http://localhost:8080/api/batches', {
        department: 'FINANCE',
        voucherIds: [voucherUuid],
        fromAudit: true
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Cookie': 'session=test-audit-session'
        }
      });

      console.log('⚠️ Duplicate batch was created (this might be expected if time window passed)');
    } catch (error) {
      if (error.response && error.response.status === 409) {
        console.log('✅ SUCCESS: Duplicate batch prevented with 409 Conflict response');
      } else {
        console.log('❌ Unexpected error:', error.message);
      }
    }

    // Test 3: Check notification details
    console.log('\n🧪 TEST 3: Checking notification details...');
    const [notifications] = await connection.execute(`
      SELECT id, user_id, message, batch_id, type, from_audit, timestamp 
      FROM notifications 
      WHERE user_id = "FINANCE" AND type = "NEW_BATCH" 
      ORDER BY timestamp DESC 
      LIMIT 5
    `);

    console.log('📋 Recent FINANCE notifications:');
    notifications.forEach((notif, index) => {
      console.log(`   ${index + 1}. ${notif.message} (${notif.timestamp})`);
      console.log(`      ID: ${notif.id}, Batch: ${notif.batch_id}, From Audit: ${notif.from_audit}`);
    });

    // Summary
    console.log('\n' + '=' .repeat(60));
    console.log('📊 COMPREHENSIVE FIX TEST SUMMARY:');
    console.log(`   • Notifications before: ${notificationsBefore}`);
    console.log(`   • Notifications after: ${notificationsAfter}`);
    console.log(`   • Increase: ${notificationIncrease}`);
    
    if (notificationIncrease === 1) {
      console.log('   • Result: ✅ DUPLICATE PREVENTION WORKING');
      console.log('   • Status: 🎉 COMPREHENSIVE FIX SUCCESSFUL');
    } else {
      console.log('   • Result: ❌ DUPLICATES STILL OCCURRING');
      console.log('   • Status: 🚨 ADDITIONAL INVESTIGATION NEEDED');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  } finally {
    await connection.end();
  }
}

testDuplicateNotificationFix().catch(console.error);
