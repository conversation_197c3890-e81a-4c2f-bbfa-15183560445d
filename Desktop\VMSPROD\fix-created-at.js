const mysql = require('mysql2/promise');

async function fixCreatedAt() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });

  console.log('🔄 Fixing created_at column...');
  
  try {
    // First, update NULL values
    await connection.execute(`
      UPDATE vouchers 
      SET created_at = NOW() 
      WHERE created_at IS NULL
    `);
    
    console.log('✅ Fixed NULL created_at values');
    
  } catch (error) {
    console.error('❌ Error fixing created_at:', error);
  } finally {
    await connection.end();
  }
}

fixCreatedAt().catch(console.error);
