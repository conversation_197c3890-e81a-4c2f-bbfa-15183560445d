import { useNavigate } from 'react-router-dom';
import { useAppStore } from '@/lib/store';
import { useDashboardState } from '@/hooks/use-dashboard-state';
import { DashboardHeader } from '@/components/dashboard/dashboard-header';
import { NewVoucherForm } from '@/components/dashboard/new-voucher-form';
import { DashboardContent } from '@/components/dashboard/dashboard-content';
import { DashboardModals } from '@/components/dashboard/dashboard-modals';
import { DashboardFooter } from '@/components/dashboard/dashboard-footer';
import { Department } from '@/lib/types';
import { useDepartmentData } from '@/hooks/use-department-data';
import { useEffect } from 'react';

interface GenericDepartmentDashboardProps {
  department: Department;
}

export function GenericDepartmentDashboard({ department }: GenericDepartmentDashboardProps) {
  const navigate = useNavigate();
  const fetchBatches = useAppStore((state) => state.fetchBatches);
  const {
    currentUser,
    selectedVouchers,
    setSelectedVouchers,
    showVoucherReceiving,
    setShowVoucherReceiving,
    receivingVoucherIds,
    setReceivingVoucherIds,
    dispatchedBy,
    setDispatchedBy,
    customDispatchName,
    setCustomDispatchName,
    viewingVoucher,
    setViewingVoucher,
    showBatchReceiving,
    setShowBatchReceiving,
    selectedBatchId,
    setSelectedBatchId,
    voucherView,
    setVoucherView,
    isNotificationBlinking,
    refreshTrigger,
    refreshData,
    handleDisabledFormClick
  } = useDashboardState();

  // CRITICAL FIX: Fetch batches when department dashboard loads
  useEffect(() => {
    if (currentUser && currentUser.department === department) {
      console.log(`🔄 ${department}: Fetching batches for department dashboard`);
      fetchBatches().catch((error) => {
        console.error(`❌ ${department}: Failed to fetch batches:`, error);
      });
    }
  }, [currentUser?.id, department, fetchBatches]);

  const { batchesArray } = useDepartmentData(department, refreshTrigger);

  // PRODUCTION FIX: Check if there are vouchers to receive including VOUCHER PROCESSING status
  const hasVouchersToReceive = batchesArray.length > 0 &&
    batchesArray.some(batch =>
      batch.vouchers.some(v => {
        const isProcessedVoucher = (
          v.certifiedBy ||
          v.status === "VOUCHER REJECTED" ||
          v.status === "VOUCHER PROCESSING" ||  // NEW: Vouchers dispatched from Audit
          v.isReturned ||
          v.pendingReturn ||
          (v.dispatched && v.auditDispatchedBy)  // Additional check for dispatched vouchers
        );

        // PRODUCTION FIX: Only show if not yet received by department
        const notYetReceived = !v.departmentReceiptTime;

        return isProcessedVoucher && notYetReceived;
      })
    );

  // Redirect if user is not from this department
  useEffect(() => {
    if (!currentUser) {
      navigate('/');
      return;
    }

    if (currentUser.department !== department) {
      if (currentUser.department === 'AUDIT') {
        navigate('/audit-dashboard');
      } else if (currentUser.department === 'ADMINISTRATOR') {
        navigate('/admin-dashboard');
      } else {
        navigate('/dashboard');
      }
    }
  }, [currentUser, department, navigate]);

  if (!currentUser) {
    return null;
  }

  return (
    <div className="flex flex-col h-screen bg-black text-white">
      <DashboardHeader />

      <div className="px-6 py-2 bg-black">
        <NewVoucherForm
          department={department}
          isDisabled={hasVouchersToReceive}
          onDisabledClick={handleDisabledFormClick}
          hidden={hasVouchersToReceive}
        />
      </div>

      <DashboardContent
        department={department}
        refreshTrigger={refreshTrigger}
        onRefresh={refreshData}
        onReceiveVouchers={(voucherIdsOrBatchId, isBatchId = false) => {
          console.log("🔄 DASHBOARD: Receive vouchers called with:", voucherIdsOrBatchId, "isBatchId:", isBatchId);

          if (isBatchId) {
            // PRODUCTION FIX: Use proper batch receiving for batches from Audit
            console.log("✅ DASHBOARD: Opening batch receiving for batch ID:", voucherIdsOrBatchId);
            setSelectedBatchId(voucherIdsOrBatchId);
            setShowBatchReceiving(true);
          } else {
            // Fallback to old voucher receiving for individual vouchers
            console.log("⚠️ DASHBOARD: Using fallback voucher receiving for voucher IDs:", voucherIdsOrBatchId);
            setReceivingVoucherIds(voucherIdsOrBatchId);
            setShowVoucherReceiving(true);
          }
        }}
        selectedVouchers={selectedVouchers}
        dispatchedBy={dispatchedBy}
        customDispatchName={customDispatchName}
        onDispatcherChange={setDispatchedBy}
        onCustomDispatchNameChange={setCustomDispatchName}
        onSendToAudit={async () => {
          const sendVouchersToAudit = useAppStore.getState().sendVouchersToAudit;

          if (selectedVouchers.length > 0 && (dispatchedBy || customDispatchName)) {
            const finalDispatchedBy = dispatchedBy || customDispatchName.toUpperCase();
            try {
              // CRITICAL FIX: Await the operation to ensure it completes before updating UI
              await sendVouchersToAudit(department, selectedVouchers, finalDispatchedBy);

              setSelectedVouchers([]);
              setDispatchedBy('');
              setCustomDispatchName('');
              setVoucherView('processing');

              // CRITICAL FIX: Refresh data after sending to ensure frontend shows updated state
              refreshData();
            } catch (error) {
              console.error('Error sending vouchers to audit:', error);
            }
          }
        }}
        onSelectionChange={setSelectedVouchers}
        onViewVoucher={setViewingVoucher}
        voucherView={voucherView}
        onVoucherViewChange={setVoucherView}
        isNotificationBlinking={isNotificationBlinking}
      />

      <DashboardModals
        department={department}
        showVoucherReceiving={showVoucherReceiving}
        setShowVoucherReceiving={setShowVoucherReceiving}
        receivingVoucherIds={receivingVoucherIds}
        viewingVoucher={viewingVoucher}
        setViewingVoucher={setViewingVoucher}
        showBatchReceiving={showBatchReceiving}
        setShowBatchReceiving={setShowBatchReceiving}
        selectedBatchId={selectedBatchId}
        onRefresh={refreshData}
      />

      <DashboardFooter />
    </div>
  );
}
