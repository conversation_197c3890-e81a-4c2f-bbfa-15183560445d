
import { useState, useEffect } from 'react';
import { Department, Voucher } from '@/lib/types';
import { useAppStore } from '@/lib/store';
import { VOUCHER_STATUSES } from '@/lib/constants/voucher-statuses';

export const useVoucherTabs = (department: Department) => {
  const vouchers = useAppStore((state) => state.vouchers);
  const fetchVouchers = useAppStore((state) => state.fetchVouchers);
  const [activeTab, setActiveTab] = useState('new-vouchers');
  const [forceUpdate, setForceUpdate] = useState(0);

  // Fetch vouchers on department change
  useEffect(() => {
    fetchVouchers(); // Fetch all vouchers for real-time updates
  }, [department]);

  // REAL-TIME TAB UPDATES: Force re-render when vouchers change
  useEffect(() => {
    setForceUpdate(prev => prev + 1);
  }, [vouchers, department]);

  // Watch for workStarted and dispatched field changes for real-time tab updates
  const relevantVouchers = vouchers.filter(v => v.originalDepartment === department);
  const workStartedStates = relevantVouchers.map(v => `${v.id}:${v.workStarted}`).join('|');
  const dispatchedStates = relevantVouchers.map(v => `${v.id}:${v.dispatched}`).join('|');

  useEffect(() => {
    setForceUpdate(prev => prev + 1);
  }, [workStartedStates, dispatchedStates]);

  // SIMPLE FILTERING: Clear business rules
  const newVouchers = vouchers.filter(v => {
    // For audit dashboard viewing department hubs (e.g., FINANCE VOUCHER HUB)
    return v.originalDepartment === department &&
           v.department === 'AUDIT' &&
           v.status === VOUCHER_STATUSES.AUDIT_PROCESSING &&
           v.receivedByAudit === true &&
           v.workStarted !== true &&  // No work started yet
           !v.dispatched;
  });

  // SIMPLE FILTERING: PENDING DISPATCH = work has started
  const pendingDispatchVouchers = vouchers.filter(v => {
    return v.originalDepartment === department &&
           v.department === 'AUDIT' &&
           v.status === VOUCHER_STATUSES.AUDIT_PROCESSING &&
           v.receivedByAudit === true &&
           v.workStarted === true &&  // Work has started
           !v.dispatched;
  });
  // SIMPLE FILTERING: Other tabs
  const dispatchedVouchers = vouchers.filter(v =>
    v.originalDepartment === department &&
    v.dispatched === true
  );

  const returnedVouchers = vouchers.filter(v =>
    v.originalDepartment === department &&
    v.status === VOUCHER_STATUSES.VOUCHER_RETURNED
  );

  const rejectedVouchers = vouchers.filter(v =>
    v.originalDepartment === department &&
    v.status === VOUCHER_STATUSES.VOUCHER_REJECTED
  );

  // Tab counts for debugging (removed console logging to prevent loops)

  return {
    activeTab,
    setActiveTab,
    newVouchers,
    pendingDispatchVouchers,
    dispatchedVouchers,
    returnedVouchers,
    rejectedVouchers
  };
};
