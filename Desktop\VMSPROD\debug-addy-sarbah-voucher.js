const mysql = require('mysql2/promise');

async function debugAddySabahVoucher() {
  console.log('🔍 DEBUGGING ADDY SABAH VOUCHER WORKFLOW ISSUE');
  console.log('='.repeat(80));
  
  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'vms@2025@1989',
      database: 'vms_production'
    });

    // Step 1: Find the Addy Sabah voucher
    console.log('\n1. FINDING ADDY SABAH VOUCHER...');
    console.log('-'.repeat(50));
    
    const [vouchers] = await connection.execute(`
      SELECT
        id, voucher_id, claimant, department, original_department, status,
        sent_to_audit, received_by_audit, work_started, pre_audited_amount,
        pre_audited_by, certified_by, dispatched, pending_return, is_returned,
        comment, created_at
      FROM vouchers 
      WHERE claimant LIKE '%ADDY SABAH%'
      ORDER BY created_at DESC
      LIMIT 1
    `);

    if (vouchers.length === 0) {
      console.log('❌ No voucher found with claimant "ADDY SABAH"');
      return;
    }

    const voucher = vouchers[0];
    console.log('✅ Found Addy Sabah voucher:');
    console.log(`   🆔 Voucher ID: ${voucher.voucher_id}`);
    console.log(`   👤 Claimant: ${voucher.claimant}`);
    console.log(`   🏢 Department: ${voucher.department}`);
    console.log(`   🏢 Original Department: ${voucher.original_department}`);
    console.log(`   📊 Status: ${voucher.status}`);
    console.log(`   📤 Sent to Audit: ${voucher.sent_to_audit ? 'YES' : 'NO'}`);
    console.log(`   📥 Received by Audit: ${voucher.received_by_audit ? 'YES' : 'NO'}`);
    console.log(`   🔧 Work Started: ${voucher.work_started ? 'YES' : 'NO'}`);
    console.log(`   💰 Pre-audited Amount: ${voucher.pre_audited_amount || 'NOT SET'}`);
    console.log(`   👨‍💼 Pre-audited By: ${voucher.pre_audited_by || 'NOT SET'}`);
    console.log(`   ✅ Certified By: ${voucher.certified_by || 'NOT SET'}`);
    console.log(`   📤 Dispatched: ${voucher.dispatched ? 'YES' : 'NO'}`);
    console.log(`   🔄 Pending Return: ${voucher.pending_return ? 'YES' : 'NO'}`);
    console.log(`   ↩️  Is Returned: ${voucher.is_returned ? 'YES' : 'NO'}`);
    console.log(`   💬 Comment: ${voucher.comment || 'NO COMMENT'}`);

    // Step 2: Analyze which tab it should appear in
    console.log('\n2. TAB ANALYSIS...');
    console.log('-'.repeat(50));
    
    // Check NEW VOUCHERS tab criteria
    const shouldBeInNewVouchers = 
      voucher.original_department === 'FINANCE' &&
      voucher.department === 'AUDIT' &&
      voucher.status === 'AUDIT: PROCESSING' &&
      voucher.received_by_audit === 1 &&
      voucher.work_started !== 1 &&
      voucher.dispatched !== 1;
    
    console.log(`📋 NEW VOUCHERS tab criteria:`);
    console.log(`   ✓ Original Department = FINANCE: ${voucher.original_department === 'FINANCE' ? 'YES' : 'NO'}`);
    console.log(`   ✓ Department = AUDIT: ${voucher.department === 'AUDIT' ? 'YES' : 'NO'}`);
    console.log(`   ✓ Status = AUDIT: PROCESSING: ${voucher.status === 'AUDIT: PROCESSING' ? 'YES' : 'NO'}`);
    console.log(`   ✓ Received by Audit: ${voucher.received_by_audit === 1 ? 'YES' : 'NO'}`);
    console.log(`   ✓ Work NOT Started: ${voucher.work_started !== 1 ? 'YES' : 'NO'}`);
    console.log(`   ✓ NOT Dispatched: ${voucher.dispatched !== 1 ? 'YES' : 'NO'}`);
    console.log(`   🎯 SHOULD BE IN NEW VOUCHERS: ${shouldBeInNewVouchers ? 'YES' : 'NO'}`);

    // Check PENDING DISPATCH tab criteria
    const shouldBeInPendingDispatch = 
      voucher.original_department === 'FINANCE' &&
      voucher.department === 'AUDIT' &&
      voucher.status === 'AUDIT: PROCESSING' &&
      voucher.received_by_audit === 1 &&
      voucher.work_started === 1 &&
      voucher.dispatched !== 1;
    
    console.log(`\n📤 PENDING DISPATCH tab criteria:`);
    console.log(`   ✓ Original Department = FINANCE: ${voucher.original_department === 'FINANCE' ? 'YES' : 'NO'}`);
    console.log(`   ✓ Department = AUDIT: ${voucher.department === 'AUDIT' ? 'YES' : 'NO'}`);
    console.log(`   ✓ Status = AUDIT: PROCESSING: ${voucher.status === 'AUDIT: PROCESSING' ? 'YES' : 'NO'}`);
    console.log(`   ✓ Received by Audit: ${voucher.received_by_audit === 1 ? 'YES' : 'NO'}`);
    console.log(`   ✓ Work Started: ${voucher.work_started === 1 ? 'YES' : 'NO'}`);
    console.log(`   ✓ NOT Dispatched: ${voucher.dispatched !== 1 ? 'YES' : 'NO'}`);
    console.log(`   🎯 SHOULD BE IN PENDING DISPATCH: ${shouldBeInPendingDispatch ? 'YES' : 'NO'}`);

    // Step 3: Count all FINANCE vouchers in each category
    console.log('\n3. FINANCE VOUCHER COUNTS...');
    console.log('-'.repeat(50));
    
    const [newVoucherCount] = await connection.execute(`
      SELECT COUNT(*) as count
      FROM vouchers 
      WHERE original_department = 'FINANCE'
        AND department = 'AUDIT'
        AND status = 'AUDIT: PROCESSING'
        AND received_by_audit = 1
        AND (work_started IS NULL OR work_started = 0)
        AND (dispatched IS NULL OR dispatched = 0)
    `);
    
    const [pendingDispatchCount] = await connection.execute(`
      SELECT COUNT(*) as count
      FROM vouchers 
      WHERE original_department = 'FINANCE'
        AND department = 'AUDIT'
        AND status = 'AUDIT: PROCESSING'
        AND received_by_audit = 1
        AND work_started = 1
        AND (dispatched IS NULL OR dispatched = 0)
    `);

    console.log(`📋 NEW VOUCHERS count: ${newVoucherCount[0].count}`);
    console.log(`📤 PENDING DISPATCH count: ${pendingDispatchCount[0].count}`);
    console.log(`📊 Total should be: ${newVoucherCount[0].count + pendingDispatchCount[0].count}`);

    // Step 4: Show all FINANCE vouchers with their work_started status
    console.log('\n4. ALL FINANCE VOUCHERS IN AUDIT...');
    console.log('-'.repeat(50));
    
    const [allFinanceVouchers] = await connection.execute(`
      SELECT voucher_id, claimant, work_started, pre_audited_amount, pre_audited_by, dispatched
      FROM vouchers 
      WHERE original_department = 'FINANCE'
        AND department = 'AUDIT'
        AND status = 'AUDIT: PROCESSING'
        AND received_by_audit = 1
      ORDER BY voucher_id
    `);

    allFinanceVouchers.forEach((v, index) => {
      const tab = v.work_started === 1 ? 'PENDING DISPATCH' : 'NEW VOUCHERS';
      console.log(`   ${index + 1}. ${v.voucher_id} - ${v.claimant} - Work Started: ${v.work_started || 0} - Tab: ${tab}`);
    });

    // Step 5: Recommendations
    console.log('\n5. DIAGNOSIS & RECOMMENDATIONS...');
    console.log('-'.repeat(50));
    
    if (!shouldBeInNewVouchers && !shouldBeInPendingDispatch) {
      console.log('❌ ISSUE: Voucher doesn\'t meet criteria for either tab!');
      console.log('🔧 POSSIBLE CAUSES:');
      console.log('   - Status is not "AUDIT: PROCESSING"');
      console.log('   - Department is not "AUDIT"');
      console.log('   - Original department is not "FINANCE"');
      console.log('   - Not received by audit');
    } else if (shouldBeInNewVouchers) {
      console.log('✅ Voucher SHOULD be in NEW VOUCHERS tab');
      console.log('🔧 If it\'s not showing, check frontend filtering logic');
    } else if (shouldBeInPendingDispatch) {
      console.log('✅ Voucher SHOULD be in PENDING DISPATCH tab');
      console.log('🔧 If it\'s not showing, check frontend filtering logic');
    }

    // Step 6: Check if work_started needs to be set
    if (voucher.pre_audited_amount && !voucher.work_started) {
      console.log('\n⚠️  POTENTIAL FIX NEEDED:');
      console.log('   Voucher has pre_audited_amount but work_started is not set');
      console.log('   This should move it to PENDING DISPATCH tab');
      
      console.log('\n🔧 APPLYING FIX: Setting work_started = 1...');
      await connection.execute(`
        UPDATE vouchers 
        SET work_started = 1 
        WHERE id = ?
      `, [voucher.id]);
      
      console.log('✅ Fixed: work_started flag set to 1');
      
      // Verify the fix
      const [updatedVoucher] = await connection.execute(`
        SELECT work_started FROM vouchers WHERE id = ?
      `, [voucher.id]);
      
      console.log(`✅ Verification: work_started is now ${updatedVoucher[0].work_started}`);
    }

    console.log('\n🔍 DEBUGGING COMPLETE!');
    console.log('='.repeat(80));

  } catch (error) {
    console.error('❌ Debug error:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the debug
debugAddySabahVoucher().catch(console.error);
