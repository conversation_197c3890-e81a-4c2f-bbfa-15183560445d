const mysql = require('mysql2/promise');

async function fixVoucherDisplayIssue() {
  console.log('🔧 FIXING VOUCHER DISPLAY ISSUE...\n');
  
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });

  try {
    // Step 1: Verify voucher data exists
    console.log('1. VERIFYING VOUCHER DATA...');
    const [vouchers] = await connection.execute(`
      SELECT COUNT(*) as total, 
             COUNT(CASE WHEN deleted = FALSE THEN 1 END) as active,
             COUNT(CASE WHEN department = 'FINANCE' AND deleted = FALSE THEN 1 END) as finance_current,
             COUNT(CASE WHEN original_department = 'FINANCE' AND deleted = FALSE THEN 1 END) as finance_original
      FROM vouchers
    `);
    
    const stats = vouchers[0];
    console.log(`📊 Total vouchers: ${stats.total}`);
    console.log(`📊 Active vouchers: ${stats.active}`);
    console.log(`📊 FINANCE current dept: ${stats.finance_current}`);
    console.log(`📊 FINANCE original dept: ${stats.finance_original}`);

    // Step 2: Check if original_department column exists and is populated
    console.log('\n2. CHECKING original_department COLUMN...');
    const [columns] = await connection.execute('DESCRIBE vouchers');
    const hasOriginalDept = columns.some(col => col.Field === 'original_department');
    
    if (!hasOriginalDept) {
      console.log('❌ original_department column missing! Adding it...');
      await connection.execute('ALTER TABLE vouchers ADD COLUMN original_department VARCHAR(50)');
      console.log('✅ original_department column added');
      
      // Populate original_department for existing vouchers
      await connection.execute(`
        UPDATE vouchers 
        SET original_department = department 
        WHERE original_department IS NULL
      `);
      console.log('✅ original_department populated for existing vouchers');
    } else {
      console.log('✅ original_department column exists');
      
      // Check if it's populated
      const [unpopulated] = await connection.execute(`
        SELECT COUNT(*) as count 
        FROM vouchers 
        WHERE original_department IS NULL AND deleted = FALSE
      `);
      
      if (unpopulated[0].count > 0) {
        console.log(`🔧 Populating ${unpopulated[0].count} vouchers with missing original_department...`);
        await connection.execute(`
          UPDATE vouchers 
          SET original_department = department 
          WHERE original_department IS NULL
        `);
        console.log('✅ original_department populated');
      }
    }

    // Step 3: Verify the API query logic
    console.log('\n3. TESTING API QUERY LOGIC...');
    
    // Test FINANCE user query (should see vouchers where department = 'FINANCE' OR original_department = 'FINANCE')
    const [financeVouchers] = await connection.execute(`
      SELECT id, voucher_id, claimant, department, original_department, status, deleted
      FROM vouchers 
      WHERE (department = 'FINANCE' OR original_department = 'FINANCE') AND deleted = FALSE 
      ORDER BY voucher_id
    `);
    
    console.log(`📊 FINANCE user should see: ${financeVouchers.length} vouchers`);
    if (financeVouchers.length > 0) {
      console.log('📋 Sample FINANCE vouchers:');
      financeVouchers.slice(0, 3).forEach((v, i) => {
        console.log(`   ${i+1}. ${v.voucher_id} - ${v.claimant}`);
        console.log(`      Current: ${v.department} | Original: ${v.original_department}`);
        console.log(`      Status: ${v.status}`);
      });
    }

    // Test AUDIT user query (should see all vouchers)
    const [auditVouchers] = await connection.execute(`
      SELECT COUNT(*) as count
      FROM vouchers 
      WHERE deleted = FALSE
    `);
    
    console.log(`📊 AUDIT user should see: ${auditVouchers[0].count} vouchers`);

    // Step 4: Check for any data integrity issues
    console.log('\n4. CHECKING DATA INTEGRITY...');
    
    // Check for vouchers with invalid statuses
    const [invalidStatus] = await connection.execute(`
      SELECT COUNT(*) as count
      FROM vouchers 
      WHERE status NOT IN ('PENDING', 'AUDIT: PROCESSING', 'VOUCHER_RETURNED', 'VOUCHER_REJECTED', 'DISPATCHED')
      AND deleted = FALSE
    `);
    
    if (invalidStatus[0].count > 0) {
      console.log(`⚠️ Found ${invalidStatus[0].count} vouchers with invalid status`);
    } else {
      console.log('✅ All voucher statuses are valid');
    }

    // Check for vouchers with missing required fields
    const [missingFields] = await connection.execute(`
      SELECT COUNT(*) as count
      FROM vouchers 
      WHERE (claimant IS NULL OR claimant = '' OR 
             department IS NULL OR department = '' OR
             status IS NULL OR status = '')
      AND deleted = FALSE
    `);
    
    if (missingFields[0].count > 0) {
      console.log(`⚠️ Found ${missingFields[0].count} vouchers with missing required fields`);
    } else {
      console.log('✅ All vouchers have required fields');
    }

    // Step 5: Test the exact API query for FINANCE department
    console.log('\n5. TESTING EXACT API QUERIES...');
    
    // This is the exact query the API uses for FINANCE users
    const [apiFinanceQuery] = await connection.execute(`
      SELECT * FROM vouchers 
      WHERE (department = ? OR original_department = ?) AND deleted = FALSE 
      ORDER BY voucher_id
    `, ['FINANCE', 'FINANCE']);
    
    console.log(`📊 API query for FINANCE returns: ${apiFinanceQuery.length} vouchers`);

    // This is the exact query the API uses for AUDIT users
    const [apiAuditQuery] = await connection.execute(`
      SELECT * FROM vouchers 
      WHERE deleted = FALSE 
      ORDER BY department, voucher_id
    `);
    
    console.log(`📊 API query for AUDIT returns: ${apiAuditQuery.length} vouchers`);

    // Step 6: Show the latest voucher details
    console.log('\n6. LATEST VOUCHER DETAILS...');
    const [latestVoucher] = await connection.execute(`
      SELECT * FROM vouchers 
      ORDER BY created_at DESC 
      LIMIT 1
    `);
    
    if (latestVoucher.length > 0) {
      const v = latestVoucher[0];
      console.log('📋 Latest voucher (FINJUL0014):');
      console.log(`   ID: ${v.id}`);
      console.log(`   Voucher ID: ${v.voucher_id}`);
      console.log(`   Claimant: ${v.claimant}`);
      console.log(`   Department: ${v.department}`);
      console.log(`   Original Department: ${v.original_department}`);
      console.log(`   Status: ${v.status}`);
      console.log(`   Deleted: ${v.deleted}`);
      console.log(`   Created: ${v.created_at}`);
    }

    console.log('\n🎉 VOUCHER DISPLAY ANALYSIS COMPLETED!');
    console.log('\n📋 SUMMARY:');
    console.log(`   - Database has ${stats.active} active vouchers`);
    console.log(`   - FINANCE users should see ${financeVouchers.length} vouchers`);
    console.log(`   - AUDIT users should see ${auditVouchers[0].count} vouchers`);
    console.log(`   - Latest voucher: FINJUL0014 (${latestVoucher[0]?.status})`);

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await connection.end();
  }
}

fixVoucherDisplayIssue().catch(console.error);
