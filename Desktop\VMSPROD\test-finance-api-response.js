const mysql = require('mysql2/promise');

async function testFinanceAPIResponse() {
  console.log('🔍 TESTING FINANCE API RESPONSE');
  console.log('='.repeat(50));

  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'vms@2025@1989',
      database: 'vms_production'
    });
    
    console.log('✅ Connected to database');

    // Check what the API should return for Finance department
    console.log('\n1. WHAT API SHOULD RETURN FOR FINANCE:');
    console.log('-'.repeat(50));
    
    const [financeBatches] = await connection.execute(`
      SELECT 
        id,
        department,
        sent_by,
        sent_time,
        received,
        from_audit,
        (SELECT COUNT(*) FROM batch_vouchers WHERE batch_id = voucher_batches.id) as voucher_count
      FROM voucher_batches 
      WHERE department = 'FINANCE'
      ORDER BY sent_time DESC
    `);

    console.log(`💰 Finance API should return: ${financeBatches.length} batches`);
    
    if (financeBatches.length === 0) {
      console.log('❌ NO BATCHES FOUND FOR FINANCE!');
      console.log('   This means no batches have department = "FINANCE"');
      return;
    }

    financeBatches.forEach((batch, index) => {
      const direction = batch.from_audit ? 'FROM AUDIT' : 'TO AUDIT';
      const status = batch.received ? 'RECEIVED' : 'UNRECEIVED';
      console.log(`   ${index + 1}. ${batch.id.substring(0, 8)}... - ${direction} - ${status}`);
      console.log(`      sent_by: ${batch.sent_by}`);
      console.log(`      vouchers: ${batch.voucher_count}`);
      console.log(`      sent_time: ${batch.sent_time}`);
    });

    // Check what should appear in Finance dashboard
    console.log('\n2. WHAT SHOULD APPEAR IN FINANCE DASHBOARD:');
    console.log('-'.repeat(50));
    
    const batchesFromAudit = financeBatches.filter(batch => 
      batch.from_audit === 1 && batch.received === 0
    );
    
    console.log(`📥 Batches FROM audit (unreceived): ${batchesFromAudit.length}`);
    
    if (batchesFromAudit.length > 0) {
      console.log('   ✅ These should show in Finance dashboard:');
      batchesFromAudit.forEach((batch, index) => {
        console.log(`   ${index + 1}. Batch ${batch.id.substring(0, 8)}... (${batch.voucher_count} vouchers)`);
      });
    } else {
      console.log('   ❌ NO batches to show in Finance dashboard');
      console.log('   Possible reasons:');
      console.log('   - All batches are marked as received');
      console.log('   - No batches have from_audit = 1');
      console.log('   - Batches have wrong department');
    }

    // Check the most recent batch
    console.log('\n3. MOST RECENT BATCH ANALYSIS:');
    console.log('-'.repeat(50));
    
    const [latestBatch] = await connection.execute(`
      SELECT 
        id,
        department,
        sent_by,
        sent_time,
        received,
        from_audit
      FROM voucher_batches 
      ORDER BY sent_time DESC 
      LIMIT 1
    `);

    if (latestBatch.length > 0) {
      const batch = latestBatch[0];
      console.log(`📦 Latest batch: ${batch.id.substring(0, 8)}...`);
      console.log(`   department: "${batch.department}"`);
      console.log(`   from_audit: ${batch.from_audit}`);
      console.log(`   received: ${batch.received}`);
      console.log(`   sent_by: "${batch.sent_by}"`);
      console.log(`   sent_time: ${batch.sent_time}`);
      
      if (batch.department === 'FINANCE' && batch.from_audit === 1 && batch.received === 0) {
        console.log('   ✅ This batch SHOULD appear in Finance dashboard');
      } else {
        console.log('   ❌ This batch will NOT appear in Finance dashboard');
        console.log(`   Reason: dept=${batch.department}, from_audit=${batch.from_audit}, received=${batch.received}`);
      }
    }

    // Check if there are any API transformation issues
    console.log('\n4. API TRANSFORMATION CHECK:');
    console.log('-'.repeat(50));
    
    console.log('🔧 API should transform:');
    console.log('   from_audit → fromAudit');
    console.log('   sent_by → sentBy');
    console.log('   sent_time → sentTime');
    console.log('');
    console.log('🎯 Frontend expects:');
    console.log('   batch.fromAudit === true');
    console.log('   batch.received === false');
    console.log('   batch.department === "FINANCE"');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n✅ Database connection closed');
    }
  }
}

testFinanceAPIResponse();
