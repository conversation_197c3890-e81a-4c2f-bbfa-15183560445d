const fetch = require('node-fetch');

async function testBatchAPI() {
  try {
    console.log('🧪 Testing VMS Batch API...');
    
    // Test the batch creation endpoint
    const testData = {
      department: 'FINANCE',
      voucherIds: ['test-voucher-1', 'test-voucher-2'],
      fromAudit: true
    };
    
    console.log('📤 Sending test batch creation request...');
    console.log('Data:', JSON.stringify(testData, null, 2));
    
    const response = await fetch('http://localhost:8080/api/batches', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testData)
    });
    
    console.log(`📡 Response status: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const result = await response.json();
      console.log('✅ Batch API is working!');
      console.log('Response:', JSON.stringify(result, null, 2));
    } else {
      const errorText = await response.text();
      console.log('❌ Batch API failed:');
      console.log('Error:', errorText);
    }
    
  } catch (error) {
    console.error('💥 Test failed:', error.message);
  }
}

// Run the test
testBatchAPI();
