const mysql = require('mysql2/promise');
const axios = require('axios');

async function diagnoseVoucherDisplay() {
  console.log('🔍 COMPREHENSIVE VOUCHER DISPLAY DIAGNOSIS...\n');
  
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });

  try {
    // Step 1: Check database vouchers
    console.log('1. DATABASE ANALYSIS');
    console.log('='.repeat(50));
    
    const [allVouchers] = await connection.execute(`
      SELECT id, voucher_id, claimant, department, original_department, status, sent_to_audit, deleted, created_at
      FROM vouchers 
      ORDER BY created_at DESC
    `);
    
    console.log(`📊 Total vouchers in database: ${allVouchers.length}`);
    console.log(`📊 Non-deleted vouchers: ${allVouchers.filter(v => !v.deleted).length}`);
    
    // Group by department
    const byDept = {};
    allVouchers.forEach(v => {
      if (!byDept[v.department]) byDept[v.department] = [];
      byDept[v.department].push(v);
    });
    
    console.log('\n📊 Vouchers by current department:');
    Object.keys(byDept).forEach(dept => {
      console.log(`   ${dept}: ${byDept[dept].length} vouchers`);
    });
    
    // Group by original department
    const byOrigDept = {};
    allVouchers.forEach(v => {
      const origDept = v.original_department || v.department;
      if (!byOrigDept[origDept]) byOrigDept[origDept] = [];
      byOrigDept[origDept].push(v);
    });
    
    console.log('\n📊 Vouchers by original department:');
    Object.keys(byOrigDept).forEach(dept => {
      console.log(`   ${dept}: ${byOrigDept[dept].length} vouchers`);
    });

    // Step 2: Test API authentication
    console.log('\n\n2. API AUTHENTICATION TEST');
    console.log('='.repeat(50));
    
    try {
      // Test login for FINANCE user
      const loginResponse = await axios.post('http://localhost:8080/api/auth/login', {
        department: 'FINANCE',
        username: 'FELIX AYISI',
        password: '123'
      }, {
        withCredentials: true,
        timeout: 5000
      });
      
      console.log('✅ FINANCE login successful');
      
      // Extract cookies
      const cookies = loginResponse.headers['set-cookie'];
      const cookieHeader = cookies ? cookies.map(cookie => cookie.split(';')[0]).join('; ') : '';
      
      // Test vouchers API for FINANCE
      const financeVouchersResponse = await axios.get('http://localhost:8080/api/vouchers', {
        withCredentials: true,
        headers: {
          'Cookie': cookieHeader
        },
        timeout: 5000
      });
      
      console.log(`📊 FINANCE API returns: ${financeVouchersResponse.data.length} vouchers`);
      
      if (financeVouchersResponse.data.length > 0) {
        console.log('\n📋 FINANCE vouchers from API:');
        financeVouchersResponse.data.slice(0, 5).forEach((v, i) => {
          console.log(`   ${i+1}. ${v.voucher_id} - ${v.claimant} (${v.status})`);
        });
      } else {
        console.log('❌ No vouchers returned for FINANCE user');
      }
      
      // Test with department parameter
      const financeVouchersWithParam = await axios.get('http://localhost:8080/api/vouchers?department=FINANCE', {
        withCredentials: true,
        headers: {
          'Cookie': cookieHeader
        },
        timeout: 5000
      });
      
      console.log(`📊 FINANCE API with ?department=FINANCE returns: ${financeVouchersWithParam.data.length} vouchers`);
      
    } catch (authError) {
      console.log('❌ Authentication failed:', authError.message);
      
      // Check if it's a database issue
      if (authError.response && authError.response.status === 500) {
        console.log('🔍 Checking for database schema issues...');
        
        // Check if departments table exists
        try {
          const [tables] = await connection.execute("SHOW TABLES LIKE 'departments'");
          if (tables.length === 0) {
            console.log('❌ departments table is missing!');
            console.log('🔧 This is likely causing authentication failures');
          }
        } catch (e) {
          console.log('❌ Error checking departments table:', e.message);
        }
      }
    }

    // Step 3: Test AUDIT user
    console.log('\n\n3. AUDIT USER TEST');
    console.log('='.repeat(50));
    
    try {
      const auditLoginResponse = await axios.post('http://localhost:8080/api/auth/login', {
        department: 'AUDIT',
        username: 'SAMUEL ASIEDU',
        password: '123'
      }, {
        withCredentials: true,
        timeout: 5000
      });
      
      console.log('✅ AUDIT login successful');
      
      const auditCookies = auditLoginResponse.headers['set-cookie'];
      const auditCookieHeader = auditCookies ? auditCookies.map(cookie => cookie.split(';')[0]).join('; ') : '';
      
      const auditVouchersResponse = await axios.get('http://localhost:8080/api/vouchers', {
        withCredentials: true,
        headers: {
          'Cookie': auditCookieHeader
        },
        timeout: 5000
      });
      
      console.log(`📊 AUDIT API returns: ${auditVouchersResponse.data.length} vouchers`);
      
      if (auditVouchersResponse.data.length > 0) {
        console.log('\n📋 AUDIT vouchers from API:');
        auditVouchersResponse.data.slice(0, 5).forEach((v, i) => {
          console.log(`   ${i+1}. ${v.voucher_id} - ${v.claimant} (${v.status})`);
        });
      }
      
    } catch (auditError) {
      console.log('❌ AUDIT authentication failed:', auditError.message);
    }

    // Step 4: Check users table
    console.log('\n\n4. USERS TABLE ANALYSIS');
    console.log('='.repeat(50));
    
    const [users] = await connection.execute('SELECT id, username, department, is_active FROM users');
    console.log(`📊 Total users: ${users.length}`);
    users.forEach(user => {
      console.log(`   ${user.username} - ${user.department} (Active: ${user.is_active})`);
    });

    // Step 5: Check table structure
    console.log('\n\n5. TABLE STRUCTURE CHECK');
    console.log('='.repeat(50));
    
    const [tables] = await connection.execute('SHOW TABLES');
    console.log('📊 Available tables:');
    tables.forEach(table => {
      console.log(`   ${Object.values(table)[0]}`);
    });

  } catch (error) {
    console.error('❌ Diagnosis error:', error);
  } finally {
    await connection.end();
  }
}

diagnoseVoucherDisplay().catch(console.error);
