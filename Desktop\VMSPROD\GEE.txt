Using single-server production mode
index-BrYahXS1-1752244376099.js:90 Starting app initialization...
index-BrYahXS1-1752244376099.js:90 Users already initialized (3 users found)
index-BrYahXS1-1752244376099.js:90 Global toast function initialized
index-BrYahXS1-1752244376099.js:90 Global toast function initialized
index-BrYahXS1-1752244376099.js:90 🔄 App initialization - On login page, skipping fetchCurrentUser
index-BrYahXS1-1752244376099.js:90 ❌ App initialization - User not authenticated or fetchCurrentUser failed
index-BrYahXS1-1752244376099.js:90 App initialization completed
index-BrYahXS1-1752244376099.js:90 Fetched users for login: 3
index-BrYahXS1-1752244376099.js:90 🔐 VMS Login attempt: Object
index-BrYahXS1-1752244376099.js:90 ✅ Login successful: FELIX AYISI (FINANCE)
index-BrYahXS1-1752244376099.js:90 🔐 Session managed via cookies
index-BrYahXS1-1752244376099.js:90 🔄 Fetching vouchers immediately after login for: FINANCE
index-BrYahXS1-1752244376099.js:90 Fetching vouchers for department: FINANCE
index-BrYahXS1-1752244376099.js:90 Current voucher count before fetch: 2
index-BrYahXS1-1752244376099.js:90 Fetching vouchers with params: Objectdepartment: "FINANCE"timestamp: 1752244732877[[Prototype]]: Object
index-BrYahXS1-1752244376099.js:90 Fetching vouchers from API for department: FINANCE at 2025-07-11T14:38:52.877Z
index-BrYahXS1-1752244376099.js:90 Received 2 vouchers from API for department: FINANCE
index-BrYahXS1-1752244376099.js:90 First few vouchers: Array(2)
index-BrYahXS1-1752244376099.js:90 Successfully mapped 2 vouchers for department: FINANCE
index-BrYahXS1-1752244376099.js:90 Received 2 vouchers from API for department FINANCE
index-BrYahXS1-1752244376099.js:90 Missing from current: 0, Missing from new: 0
index-BrYahXS1-1752244376099.js:90 First few normalized vouchers: Array(2)
index-BrYahXS1-1752244376099.js:90 Successfully updated store with 2 vouchers for department FINANCE
index-BrYahXS1-1752244376099.js:90 ✅ Vouchers fetched successfully after login
index-BrYahXS1-1752244376099.js:90 Department users for dispatch dropdown: Array(1)0: "FELIX AYISI"length: 1[[Prototype]]: Array(0)
index-BrYahXS1-1752244376099.js:90 🔄 FINANCE: Fetching batches for department: FINANCE
index-BrYahXS1-1752244376099.js:90 🔄 Starting fetchBatches...
index-BrYahXS1-1752244376099.js:90 🔄 Making API call to /api/batches...
index-BrYahXS1-1752244376099.js:90 Department users for dispatch dropdown: Array(1)0: "FELIX AYISI"length: 1[[Prototype]]: Array(0)
index-BrYahXS1-1752244376099.js:90 🔄 API response status: 200
index-BrYahXS1-1752244376099.js:90 ✅ Fetched batches from API: Array(2)
index-BrYahXS1-1752244376099.js:90 ✅ Number of batches fetched: 2
index-BrYahXS1-1752244376099.js:90 ✅ Converted to local format: Array(2)
index-BrYahXS1-1752244376099.js:90 ✅ Updated store with batches
index-BrYahXS1-1752244376099.js:90 Department users for dispatch dropdown: Array(1)
index-BrYahXS1-1752244376099.js:90 Department users for dispatch dropdown: Array(1)
index-BrYahXS1-1752244376099.js:90 🔌 Connecting WebSocket with user: FELIX AYISI (FINANCE)
index-BrYahXS1-1752244376099.js:90 🔌 WEBSOCKET CONNECTION - Production single-server mode
index-BrYahXS1-1752244376099.js:90 🔌 Creating Socket.IO connection to: /
index-BrYahXS1-1752244376099.js:90 🔌 LAN-based connection - no authentication required
index-BrYahXS1-1752244376099.js:90 Socket.IO connection config: Object
index-BrYahXS1-1752244376099.js:90 🔌 Connecting WebSocket for FELIX AYISI (FINANCE)
index-BrYahXS1-1752244376099.js:90 Socket connection initialized with token and path configuration
index-BrYahXS1-1752244376099.js:90 🎉 WEBSOCKET CONNECTED! Socket ID: agNLVoZWbzjH-3qcAABR
index-BrYahXS1-1752244376099.js:90 🔌 WebSocket connection successful!
index-BrYahXS1-1752244376099.js:90 🔍 DEBUG: Current user on socket connect: FELIX AYISI (FINANCE)
index-BrYahXS1-1752244376099.js:90 🔍 DEBUG: Full user object: Object
index-BrYahXS1-1752244376099.js:90 ✅ Explicitly joining department:FINANCE room
index-BrYahXS1-1752244376099.js:90 📤 Sending join_department event with data: Object
index-BrYahXS1-1752244376099.js:90 Fetching vouchers for department: FINANCE
index-BrYahXS1-1752244376099.js:90 Current voucher count before fetch: 2
index-BrYahXS1-1752244376099.js:90 Fetching vouchers with params: Object
index-BrYahXS1-1752244376099.js:90 Fetching vouchers from API for department: FINANCE at 2025-07-11T14:38:53.032Z
index-BrYahXS1-1752244376099.js:90 Socket connected - notifications will be received via socket events
index-BrYahXS1-1752244376099.js:90 Received locks update: Object
index-BrYahXS1-1752244376099.js:90 Received force refresh: Object
index-BrYahXS1-1752244376099.js:90 Force refresh DISABLED to prevent infinite loop: FINANCE
index-BrYahXS1-1752244376099.js:90 Department users for dispatch dropdown: Array(1)
index-BrYahXS1-1752244376099.js:90 Received 2 vouchers from API for department: FINANCE
index-BrYahXS1-1752244376099.js:90 First few vouchers: Array(2)
index-BrYahXS1-1752244376099.js:90 Successfully mapped 2 vouchers for department: FINANCE
index-BrYahXS1-1752244376099.js:90 Received 2 vouchers from API for department FINANCE
index-BrYahXS1-1752244376099.js:90 Missing from current: 0, Missing from new: 0
index-BrYahXS1-1752244376099.js:90 First few normalized vouchers: Array(2)
index-BrYahXS1-1752244376099.js:90 Successfully updated store with 2 vouchers for department FINANCE
index-BrYahXS1-1752244376099.js:90 Department users for dispatch dropdown: Array(1)
index-BrYahXS1-1752244376099.js:90 🚀🚀🚀 CACHE BUSTER: Refreshed ALL vouchers after socket connection
index-BrYahXS1-1752244376099.js:90 Users fetched from backend: Array(3)
index-BrYahXS1-1752244376099.js:90 Department users for dispatch dropdown: Array(1)
index-BrYahXS1-1752244376099.js:90 Refreshed users after socket connection
index-BrYahXS1-1752244376099.js:90 Department users for dispatch dropdown: Array(1)
index-BrYahXS1-1752244376099.js:90 Heartbeat acknowledged: {timestamp: 1752244763028}
index-BrYahXS1-1752244376099.js:90 Heartbeat acknowledged: {timestamp: 1752244763035}
index-BrYahXS1-1752244376099.js:90 Heartbeat acknowledged: {timestamp: 1752244793024}
index-BrYahXS1-1752244376099.js:90 Heartbeat acknowledged: {timestamp: 1752244793044}
index-BrYahXS1-1752244376099.js:90 Heartbeat acknowledged: {timestamp: 1752244823023}
index-BrYahXS1-1752244376099.js:90 Heartbeat acknowledged: {timestamp: 1752244823034}
index-BrYahXS1-1752244376099.js:90 Heartbeat acknowledged: {timestamp: 1752244853779}
index-BrYahXS1-1752244376099.js:90 Heartbeat acknowledged: {timestamp: 1752244853782}
index-BrYahXS1-1752244376099.js:90 Heartbeat acknowledged: {timestamp: 1752244883026}
index-BrYahXS1-1752244376099.js:90 Heartbeat acknowledged: {timestamp: 1752244883035}