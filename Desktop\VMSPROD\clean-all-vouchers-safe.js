const mysql = require('mysql2/promise');

// Database configuration (matching server config)
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: 'vms@2025@1989',
  database: 'vms_clean_db'
};

async function cleanAllVouchersSafe() {
  let connection;
  
  try {
    console.log('🧹 Starting safe voucher system cleanup...');
    
    // Connect to database
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Connected to database');

    // Check what tables exist
    console.log('\n🔍 Checking existing tables...');
    const [tables] = await connection.execute('SHOW TABLES');
    const tableNames = tables.map(row => Object.values(row)[0]);
    console.log('📋 Found tables:', tableNames);

    // Start transaction
    await connection.beginTransaction();
    console.log('\n🔄 Starting transaction...');

    let totalCleared = 0;

    // Clear tables in safe order (respecting foreign key constraints)
    const tablesToClear = [
      'batch_vouchers',
      'voucher_batches', 
      'vouchers',
      'notifications'
    ];

    for (const tableName of tablesToClear) {
      if (tableNames.includes(tableName)) {
        try {
          const [result] = await connection.execute(`DELETE FROM ${tableName}`);
          console.log(`   ✅ Cleared ${result.affectedRows} records from ${tableName}`);
          totalCleared += result.affectedRows;
        } catch (error) {
          console.log(`   ⚠️  Could not clear ${tableName}: ${error.message}`);
        }
      } else {
        console.log(`   ⏭️  Table ${tableName} does not exist, skipping`);
      }
    }

    // Reset auto-increment counters for existing tables
    console.log('\n🔄 Resetting auto-increment counters...');
    for (const tableName of tablesToClear) {
      if (tableNames.includes(tableName)) {
        try {
          await connection.execute(`ALTER TABLE ${tableName} AUTO_INCREMENT = 1`);
          console.log(`   ✅ Reset auto-increment for ${tableName}`);
        } catch (error) {
          console.log(`   ⚠️  Could not reset auto-increment for ${tableName}: ${error.message}`);
        }
      }
    }

    // Verify cleanup
    console.log('\n🔍 Verifying cleanup...');
    for (const tableName of tablesToClear) {
      if (tableNames.includes(tableName)) {
        try {
          const [count] = await connection.execute(`SELECT COUNT(*) as count FROM ${tableName}`);
          console.log(`   📊 ${tableName}: ${count[0].count} records remaining`);
        } catch (error) {
          console.log(`   ⚠️  Could not count ${tableName}: ${error.message}`);
        }
      }
    }

    // Commit transaction
    await connection.commit();
    console.log('\n✅ Transaction committed successfully');

    console.log('\n🎉 SAFE VOUCHER SYSTEM CLEANUP COMPLETED!');
    console.log(`📝 Total records cleared: ${totalCleared}`);
    console.log('\n🚀 System is now ready for fresh voucher creation!');

  } catch (error) {
    console.error('\n❌ Error during cleanup:', error);
    
    if (connection) {
      try {
        await connection.rollback();
        console.log('🔄 Transaction rolled back');
      } catch (rollbackError) {
        console.error('❌ Rollback failed:', rollbackError);
      }
    }
    
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the cleanup
cleanAllVouchersSafe()
  .then(() => {
    console.log('\n✨ Safe cleanup completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Safe cleanup failed:', error.message);
    process.exit(1);
  });
