{"hash": "25d8d33a", "configHash": "bf40ec02", "lockfileHash": "debd8a76", "browserHash": "07744b7e", "optimized": {"react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "5c9d2912", "needsInterop": true}, "@radix-ui/react-alert-dialog": {"src": "../../@radix-ui/react-alert-dialog/dist/index.mjs", "file": "@radix-ui_react-alert-dialog.js", "fileHash": "8e8d1fa0", "needsInterop": false}, "@radix-ui/react-checkbox": {"src": "../../@radix-ui/react-checkbox/dist/index.mjs", "file": "@radix-ui_react-checkbox.js", "fileHash": "41c21e4b", "needsInterop": false}, "@radix-ui/react-collapsible": {"src": "../../@radix-ui/react-collapsible/dist/index.mjs", "file": "@radix-ui_react-collapsible.js", "fileHash": "5fa35200", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "4467f935", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "4a757863", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "cf4452cd", "needsInterop": false}, "@radix-ui/react-popover": {"src": "../../@radix-ui/react-popover/dist/index.mjs", "file": "@radix-ui_react-popover.js", "fileHash": "3ef8bbba", "needsInterop": false}, "@radix-ui/react-progress": {"src": "../../@radix-ui/react-progress/dist/index.mjs", "file": "@radix-ui_react-progress.js", "fileHash": "3cb73932", "needsInterop": false}, "@radix-ui/react-scroll-area": {"src": "../../@radix-ui/react-scroll-area/dist/index.mjs", "file": "@radix-ui_react-scroll-area.js", "fileHash": "c8e425d4", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "67cfd0fb", "needsInterop": false}, "@radix-ui/react-separator": {"src": "../../@radix-ui/react-separator/dist/index.mjs", "file": "@radix-ui_react-separator.js", "fileHash": "e11b8559", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "fd56a0ed", "needsInterop": false}, "@radix-ui/react-switch": {"src": "../../@radix-ui/react-switch/dist/index.mjs", "file": "@radix-ui_react-switch.js", "fileHash": "6d6feec0", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "72f301a0", "needsInterop": false}, "@radix-ui/react-toast": {"src": "../../@radix-ui/react-toast/dist/index.mjs", "file": "@radix-ui_react-toast.js", "fileHash": "581cb8fb", "needsInterop": false}, "@radix-ui/react-tooltip": {"src": "../../@radix-ui/react-tooltip/dist/index.mjs", "file": "@radix-ui_react-tooltip.js", "fileHash": "45aa2e99", "needsInterop": false}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "26999727", "needsInterop": false}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "486b4fd2", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "e431230c", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "9c51f724", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "763a26ee", "needsInterop": false}, "react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "3fcde0fd", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "39d13d5b", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "f10b8661", "needsInterop": false}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "f0f85934", "needsInterop": true}, "recharts": {"src": "../../recharts/es6/index.js", "file": "recharts.js", "fileHash": "6d4b98a2", "needsInterop": false}, "socket.io-client": {"src": "../../socket.io-client/build/esm/index.js", "file": "socket__io-client.js", "fileHash": "e0b5f4c5", "needsInterop": false}, "sonner": {"src": "../../sonner/dist/index.mjs", "file": "sonner.js", "fileHash": "17d2e254", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "f4ba9d7f", "needsInterop": false}, "zustand": {"src": "../../zustand/esm/index.mjs", "file": "zustand.js", "fileHash": "851bec34", "needsInterop": false}, "zustand/middleware": {"src": "../../zustand/esm/middleware.mjs", "file": "zustand_middleware.js", "fileHash": "7948ae73", "needsInterop": false}}, "chunks": {"chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-5QRQSW7Z": {"file": "chunk-5QRQSW7Z.js"}, "chunk-6SPNF6KQ": {"file": "chunk-6SPNF6KQ.js"}, "chunk-3GV3KT4M": {"file": "chunk-3GV3KT4M.js"}, "chunk-D5WW72FL": {"file": "chunk-D5WW72FL.js"}, "chunk-HUHORXLV": {"file": "chunk-HUHORXLV.js"}, "chunk-3PHEZ67U": {"file": "chunk-3PHEZ67U.js"}, "chunk-BO2EVO7P": {"file": "chunk-BO2EVO7P.js"}, "chunk-ZZUMGTHG": {"file": "chunk-ZZUMGTHG.js"}, "chunk-DDW565K2": {"file": "chunk-DDW565K2.js"}, "chunk-AJMUNMLZ": {"file": "chunk-AJMUNMLZ.js"}, "chunk-I6MWER2B": {"file": "chunk-I6MWER2B.js"}, "chunk-6ZMM2PAV": {"file": "chunk-6ZMM2PAV.js"}, "chunk-O2UA4OQB": {"file": "chunk-O2UA4OQB.js"}, "chunk-MZLEVI2I": {"file": "chunk-MZLEVI2I.js"}, "chunk-ILYE3ZA7": {"file": "chunk-ILYE3ZA7.js"}, "chunk-H55D7VYG": {"file": "chunk-H55D7VYG.js"}, "chunk-R6S4VRB5": {"file": "chunk-R6S4VRB5.js"}, "chunk-4WIT4MX7": {"file": "chunk-4WIT4MX7.js"}, "chunk-S77I6LSE": {"file": "chunk-S77I6LSE.js"}, "chunk-3TFVT2CW": {"file": "chunk-3TFVT2CW.js"}, "chunk-4MBMRILA": {"file": "chunk-4MBMRILA.js"}}}