{"hash": "25d8d33a", "configHash": "bf40ec02", "lockfileHash": "debd8a76", "browserHash": "cc3792a1", "optimized": {"react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "863f62d1", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "7d0ae563", "needsInterop": true}, "sonner": {"src": "../../sonner/dist/index.mjs", "file": "sonner.js", "fileHash": "2e88d285", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "ac71993d", "needsInterop": false}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "d9366420", "needsInterop": false}, "react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "19b27e30", "needsInterop": true}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "baf13d45", "needsInterop": false}, "@radix-ui/react-tooltip": {"src": "../../@radix-ui/react-tooltip/dist/index.mjs", "file": "@radix-ui_react-tooltip.js", "fileHash": "9a6ab0ef", "needsInterop": false}, "socket.io-client": {"src": "../../socket.io-client/build/esm/index.js", "file": "socket__io-client.js", "fileHash": "427e12d4", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "f1063dc0", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "34abe6c7", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "e91d695c", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "57843af3", "needsInterop": false}, "@radix-ui/react-checkbox": {"src": "../../@radix-ui/react-checkbox/dist/index.mjs", "file": "@radix-ui_react-checkbox.js", "fileHash": "5e08255f", "needsInterop": false}, "@radix-ui/react-toast": {"src": "../../@radix-ui/react-toast/dist/index.mjs", "file": "@radix-ui_react-toast.js", "fileHash": "b4a05a97", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "6e59b5d5", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "020dacf8", "needsInterop": false}, "zustand": {"src": "../../zustand/esm/index.mjs", "file": "zustand.js", "fileHash": "c7fe6b4e", "needsInterop": false}, "zustand/middleware": {"src": "../../zustand/esm/middleware.mjs", "file": "zustand_middleware.js", "fileHash": "8d687872", "needsInterop": false}, "@radix-ui/react-collapsible": {"src": "../../@radix-ui/react-collapsible/dist/index.mjs", "file": "@radix-ui_react-collapsible.js", "fileHash": "bfaad815", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "41e767e4", "needsInterop": false}, "recharts": {"src": "../../recharts/es6/index.js", "file": "recharts.js", "fileHash": "061c134f", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "aa4b52a4", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "c81a025b", "needsInterop": false}, "@radix-ui/react-progress": {"src": "../../@radix-ui/react-progress/dist/index.mjs", "file": "@radix-ui_react-progress.js", "fileHash": "b08f3739", "needsInterop": false}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "df52fcaf", "needsInterop": false}, "@radix-ui/react-scroll-area": {"src": "../../@radix-ui/react-scroll-area/dist/index.mjs", "file": "@radix-ui_react-scroll-area.js", "fileHash": "114e00b5", "needsInterop": false}, "@radix-ui/react-separator": {"src": "../../@radix-ui/react-separator/dist/index.mjs", "file": "@radix-ui_react-separator.js", "fileHash": "cd096bb5", "needsInterop": false}, "@radix-ui/react-alert-dialog": {"src": "../../@radix-ui/react-alert-dialog/dist/index.mjs", "file": "@radix-ui_react-alert-dialog.js", "fileHash": "70101924", "needsInterop": false}, "@radix-ui/react-switch": {"src": "../../@radix-ui/react-switch/dist/index.mjs", "file": "@radix-ui_react-switch.js", "fileHash": "8dcdd260", "needsInterop": false}}, "chunks": {"chunk-P7KTXUGZ": {"file": "chunk-P7KTXUGZ.js"}, "chunk-BWNED4GO": {"file": "chunk-BWNED4GO.js"}, "chunk-6ZMM2PAV": {"file": "chunk-6ZMM2PAV.js"}, "chunk-DLHDYQ6Y": {"file": "chunk-DLHDYQ6Y.js"}, "chunk-HD5O4SDY": {"file": "chunk-HD5O4SDY.js"}, "chunk-DDW565K2": {"file": "chunk-DDW565K2.js"}, "chunk-XRKT4IKI": {"file": "chunk-XRKT4IKI.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-3R7R7DDP": {"file": "chunk-3R7R7DDP.js"}, "chunk-ZVSM3TEM": {"file": "chunk-ZVSM3TEM.js"}, "chunk-CAQHVCY5": {"file": "chunk-CAQHVCY5.js"}, "chunk-OBORSFJS": {"file": "chunk-OBORSFJS.js"}, "chunk-VUBXF3TL": {"file": "chunk-VUBXF3TL.js"}, "chunk-X65VR5TF": {"file": "chunk-X65VR5TF.js"}, "chunk-FRW6FMH6": {"file": "chunk-FRW6FMH6.js"}, "chunk-37JZFKIX": {"file": "chunk-37JZFKIX.js"}, "chunk-FLYY6CEL": {"file": "chunk-FLYY6CEL.js"}, "chunk-XGN23A4O": {"file": "chunk-XGN23A4O.js"}, "chunk-R6S4VRB5": {"file": "chunk-R6S4VRB5.js"}, "chunk-DSFGRTI6": {"file": "chunk-DSFGRTI6.js"}, "chunk-3TFVT2CW": {"file": "chunk-3TFVT2CW.js"}, "chunk-4MBMRILA": {"file": "chunk-4MBMRILA.js"}}}