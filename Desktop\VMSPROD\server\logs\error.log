{"level":"error","message":"listen EADDRINUSE: address already in use :::8080","pid":30844,"service":"vms-server","stack":"Error: listen EADDRINUSE: address already in use :::8080\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\VMS 5.0\\VMSPROD\\server\\dist\\index.js:284:16)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-15T00:42:21.718Z"}
{"level":"error","message":"💥 FATAL: Recovery failed, exiting...","service":"vms-server","timestamp":"2025-06-15 00:42:26"}
{"ip":"::1","level":"error","message":"Expected property name or '}' in JSON at position 1 (line 1 column 2)","method":"POST","requestId":"unknown","service":"vms-server","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-07-01T11:28:15.051Z","url":"/api/auth/login","userAgent":"curl/8.13.0"}
{"ip":"::1","level":"error","message":"Expected property name or '}' in JSON at position 1 (line 1 column 2)","method":"POST","requestId":"unknown","service":"vms-server","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-07-01T11:28:21.983Z","url":"/api/auth/login","userAgent":"curl/8.13.0"}
{"ip":"::1","level":"error","message":"Expected property name or '}' in JSON at position 1 (line 1 column 2)","method":"POST","requestId":"unknown","service":"vms-server","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-07-01T13:02:41.338Z","url":"/api/auth/login","userAgent":"curl/8.13.0"}
{"ip":"::1","level":"error","message":"Expected property name or '}' in JSON at position 1 (line 1 column 2)","method":"POST","requestId":"unknown","service":"vms-server","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-07-01T13:02:58.484Z","url":"/api/auth/login","userAgent":"curl/8.13.0"}
{"level":"error","message":"listen EADDRINUSE: address already in use :::8080","pid":23864,"service":"vms-server","stack":"Error: listen EADDRINUSE: address already in use :::8080\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\dist\\index.js:284:16)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-01T13:10:02.850Z"}
{"level":"error","message":"💥 FATAL: Recovery failed, exiting...","service":"vms-server","timestamp":"2025-07-01 13:10:07"}
{"ip":"::1","level":"error","message":"Expected property name or '}' in JSON at position 1 (line 1 column 2)","method":"POST","requestId":"unknown","service":"vms-server","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-07-01T13:47:09.310Z","url":"/api/auth/login","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-GB) WindowsPowerShell/5.1.26100.4484"}
{"level":"error","message":"listen EADDRINUSE: address already in use :::8080","pid":20192,"service":"vms-server","stack":"Error: listen EADDRINUSE: address already in use :::8080\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\dist\\index.js:284:16)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-01T15:47:11.280Z"}
{"level":"error","message":"💥 FATAL: Recovery failed, exiting...","service":"vms-server","timestamp":"2025-07-01 15:47:16"}
{"ip":"::1","level":"error","message":"Expected property name or '}' in JSON at position 1 (line 1 column 2)","method":"POST","requestId":"unknown","service":"vms-server","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-07-01T19:13:41.653Z","url":"/api/auth/login","userAgent":"curl/8.13.0"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'flags' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET batch_id = 'a10e4402-fdfd-4a68-8905-f597ac2ee508', status = 'PENDING RECEIPT', flags = '{\\\"sentToAudit\\\":true,\\\"dispatched\\\":false,\\\"certified\\\":false,\\\"rejected\\\":false,\\\"isReturned\\\":false,\\\"pendingReturn\\\":false,\\\"receivedByAudit\\\":false}', sent_to_audit = TRUE, dispatch_to_audit_by = 'SAMUEL ASIEDU', dispatch_time = NOW() WHERE id = 'afa82636-da04-4509-9ac7-ee0d68d07a7d'","sqlMessage":"Unknown column 'flags' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'flags' in 'field list'\n    at PromisePoolConnection.query (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\mysql2\\lib\\promise\\connection.js:29:22)\n    at C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\dist\\routes\\batches.js:144:34\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-01 20:03:31"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'flags' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET batch_id = 'e02ad75f-3417-4955-ae6f-dc61992c6455', status = 'PENDING RECEIPT', flags = '{\\\"sentToAudit\\\":true,\\\"dispatched\\\":false,\\\"certified\\\":false,\\\"rejected\\\":false,\\\"isReturned\\\":false,\\\"pendingReturn\\\":false,\\\"receivedByAudit\\\":false}', sent_to_audit = TRUE, dispatch_to_audit_by = 'SAMUEL ASIEDU', dispatch_time = NOW() WHERE id = 'afa82636-da04-4509-9ac7-ee0d68d07a7d'","sqlMessage":"Unknown column 'flags' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'flags' in 'field list'\n    at PromisePoolConnection.query (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\mysql2\\lib\\promise\\connection.js:29:22)\n    at C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\dist\\routes\\batches.js:144:34\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-01 20:04:22"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'flags' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET batch_id = '7f8f6101-cd72-4435-a6cb-1afc05d9d4ee', status = 'PENDING RECEIPT', flags = '{\\\"sentToAudit\\\":true,\\\"dispatched\\\":false,\\\"certified\\\":false,\\\"rejected\\\":false,\\\"isReturned\\\":false,\\\"pendingReturn\\\":false,\\\"receivedByAudit\\\":false}', sent_to_audit = TRUE, dispatch_to_audit_by = 'SAMUEL ASIEDU', dispatch_time = NOW() WHERE id = 'afa82636-da04-4509-9ac7-ee0d68d07a7d'","sqlMessage":"Unknown column 'flags' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'flags' in 'field list'\n    at PromisePoolConnection.query (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\mysql2\\lib\\promise\\connection.js:29:22)\n    at C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\dist\\routes\\batches.js:144:34\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-01 20:06:59"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'created_at' in 'where clause'","service":"vms-server","sql":"\n      SELECT * FROM vouchers\n      WHERE created_by = ?\n        AND description = ?\n        AND amount = ?\n        AND department = ?\n        AND created_at >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)\n        AND deleted = FALSE\n      ORDER BY created_at DESC\n      LIMIT 1\n    ","sqlMessage":"Unknown column 'created_at' in 'where clause'","sqlState":"42S22","stack":"Error: Unknown column 'created_at' in 'where clause'\n    at PromisePool.execute (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at query (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\dist\\database\\db.js:379:38)\n    at C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\dist\\routes\\vouchers.js:154:58\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\express\\lib\\router\\index.js:280:10)","timestamp":"2025-07-02 06:17:20"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'created_at' in 'where clause'","service":"vms-server","sql":"\n      SELECT * FROM vouchers\n      WHERE created_by = ?\n        AND description = ?\n        AND amount = ?\n        AND department = ?\n        AND created_at >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)\n        AND deleted = FALSE\n      ORDER BY created_at DESC\n      LIMIT 1\n    ","sqlMessage":"Unknown column 'created_at' in 'where clause'","sqlState":"42S22","stack":"Error: Unknown column 'created_at' in 'where clause'\n    at PromisePool.execute (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at query (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\dist\\database\\db.js:379:38)\n    at C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\dist\\routes\\vouchers.js:154:58\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\express\\lib\\router\\index.js:280:10)","timestamp":"2025-07-02 06:17:20"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'role' in 'field list'","service":"vms-server","sql":"SELECT user_id, user_name, department, role FROM active_sessions WHERE id = ? AND is_active = TRUE","sqlMessage":"Unknown column 'role' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'role' in 'field list'\n    at PromisePool.execute (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at query (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\dist\\database\\db.js:408:38)\n    at Array.<anonymous> (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\dist\\socket\\socketHandlers.js:49:66)\n    at run (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\socket.io\\dist\\namespace.js:130:19)\n    at Namespace.run (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\socket.io\\dist\\namespace.js:141:9)\n    at Namespace._add (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\socket.io\\dist\\namespace.js:218:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-05 20:07:10"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'received_by_audit' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET\n         status = 'AUDIT: PROCESSING',\n         flags = '{\\\"sentToAudit\\\":true,\\\"dispatched\\\":false,\\\"certified\\\":false,\\\"rejected\\\":false,\\\"isReturned\\\":false,\\\"pendingReturn\\\":false,\\\"receivedByAudit\\\":true}',\n         received_by = 'SAMUEL ASIEDU',\n         receipt_time = NOW(),\n         received_by_audit = true,\n         pre_audited_amount = NULL,\n         pre_audited_by = NULL,\n         certified_by = NULL,\n         work_started = FALSE,\n         comment = NULL\n         WHERE id = 'ed27d6a8-0b83-484c-93e4-fecb94842e8e'","sqlMessage":"Unknown column 'received_by_audit' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'received_by_audit' in 'field list'\n    at PromisePoolConnection.query (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\mysql2\\lib\\promise\\connection.js:29:22)\n    at C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\dist\\routes\\batches.js:318:30\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-05 20:43:09"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'received_by_audit' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET\n         status = 'AUDIT: PROCESSING',\n         flags = '{\\\"sentToAudit\\\":true,\\\"dispatched\\\":false,\\\"certified\\\":false,\\\"rejected\\\":false,\\\"isReturned\\\":false,\\\"pendingReturn\\\":false,\\\"receivedByAudit\\\":true}',\n         received_by = 'SAMUEL ASIEDU',\n         receipt_time = NOW(),\n         received_by_audit = true,\n         pre_audited_amount = NULL,\n         pre_audited_by = NULL,\n         certified_by = NULL,\n         work_started = FALSE,\n         comment = NULL\n         WHERE id = 'ed27d6a8-0b83-484c-93e4-fecb94842e8e'","sqlMessage":"Unknown column 'received_by_audit' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'received_by_audit' in 'field list'\n    at PromisePoolConnection.query (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\mysql2\\lib\\promise\\connection.js:29:22)\n    at C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\dist\\routes\\batches.js:318:30\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-05 20:43:14"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'received_by_audit' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET\n         status = 'AUDIT: PROCESSING',\n         flags = '{\\\"sentToAudit\\\":true,\\\"dispatched\\\":false,\\\"certified\\\":false,\\\"rejected\\\":false,\\\"isReturned\\\":false,\\\"pendingReturn\\\":false,\\\"receivedByAudit\\\":true}',\n         received_by = 'SAMUEL ASIEDU',\n         receipt_time = NOW(),\n         received_by_audit = true,\n         pre_audited_amount = NULL,\n         pre_audited_by = NULL,\n         certified_by = NULL,\n         work_started = FALSE,\n         comment = NULL\n         WHERE id = 'be1a0ec4-cd52-4dfa-a688-703af678c793'","sqlMessage":"Unknown column 'received_by_audit' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'received_by_audit' in 'field list'\n    at PromisePoolConnection.query (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\mysql2\\lib\\promise\\connection.js:29:22)\n    at C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\dist\\routes\\batches.js:318:30\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-05 20:43:31"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'received_by_audit' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET\n         status = 'AUDIT: PROCESSING',\n         flags = '{\\\"sentToAudit\\\":true,\\\"dispatched\\\":false,\\\"certified\\\":false,\\\"rejected\\\":false,\\\"isReturned\\\":false,\\\"pendingReturn\\\":false,\\\"receivedByAudit\\\":true}',\n         received_by = 'SAMUEL ASIEDU',\n         receipt_time = NOW(),\n         received_by_audit = true,\n         pre_audited_amount = NULL,\n         pre_audited_by = NULL,\n         certified_by = NULL,\n         work_started = FALSE,\n         comment = NULL\n         WHERE id = '07053e98-7721-436b-b57b-490566869f83'","sqlMessage":"Unknown column 'received_by_audit' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'received_by_audit' in 'field list'\n    at PromisePoolConnection.query (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\mysql2\\lib\\promise\\connection.js:29:22)\n    at C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\dist\\routes\\batches.js:318:30\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-07 21:37:03"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'received_by_audit' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET\n         status = 'AUDIT: PROCESSING',\n         flags = '{\\\"sentToAudit\\\":true,\\\"dispatched\\\":false,\\\"certified\\\":false,\\\"rejected\\\":false,\\\"isReturned\\\":false,\\\"pendingReturn\\\":false,\\\"receivedByAudit\\\":true}',\n         received_by = 'SAMUEL ASIEDU',\n         receipt_time = NOW(),\n         received_by_audit = true,\n         pre_audited_amount = NULL,\n         pre_audited_by = NULL,\n         certified_by = NULL,\n         work_started = FALSE,\n         comment = NULL\n         WHERE id = 'a0abf5e2-f57d-4ee9-aaab-ea433bb83e99'","sqlMessage":"Unknown column 'received_by_audit' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'received_by_audit' in 'field list'\n    at PromisePoolConnection.query (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\mysql2\\lib\\promise\\connection.js:29:22)\n    at C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\dist\\routes\\batches.js:318:30\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-07 22:23:56"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'voucher_ids' in 'field list'","service":"vms-server","sql":"INSERT INTO voucher_batches (\n          id, department, voucher_ids, sent_by, sent_time, received, from_audit\n        ) VALUES ('94443387-487e-4314-8fc2-d0b8e694e4c7', 'FINANCE', '[\\\"f4afb231-dc76-4d74-9d14-dcb88dd4b945\\\"]', 'SAMUEL ASIEDU', NOW(), FALSE, TRUE)","sqlMessage":"Unknown column 'voucher_ids' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'voucher_ids' in 'field list'\n    at PromisePoolConnection.query (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\mysql2\\lib\\promise\\connection.js:29:22)\n    at C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\dist\\routes\\batches.js:422:30\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-07 22:40:41"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'received_by_audit' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET\n           status = 'AUDIT: PROCESSING',\n           flags = '{\\\"sentToAudit\\\":true,\\\"dispatched\\\":false,\\\"certified\\\":false,\\\"rejected\\\":false,\\\"isReturned\\\":false,\\\"pendingReturn\\\":false,\\\"receivedByAudit\\\":true}',\n           department = 'AUDIT',\n           received_by = 'SAMUEL ASIEDU',\n           receipt_time = NOW(),\n           received_by_audit = TRUE,\n           pre_audited_amount = NULL,\n           pre_audited_by = NULL,\n           certified_by = NULL,\n           work_started = FALSE,\n           comment = NULL\n           WHERE id = 'e20267f3-d5fc-4fa1-adf8-4b3e3c169087'","sqlMessage":"Unknown column 'received_by_audit' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'received_by_audit' in 'field list'\n    at PromisePoolConnection.query (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\mysql2\\lib\\promise\\connection.js:29:22)\n    at C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\dist\\routes\\batches.js:347:34\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-08 16:03:56"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'received_by_audit' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET\n           status = 'AUDIT: PROCESSING',\n           flags = '{\\\"sentToAudit\\\":true,\\\"dispatched\\\":false,\\\"certified\\\":false,\\\"rejected\\\":false,\\\"isReturned\\\":false,\\\"pendingReturn\\\":false,\\\"receivedByAudit\\\":true}',\n           department = 'AUDIT',\n           received_by = 'SAMUEL ASIEDU',\n           receipt_time = NOW(),\n           received_by_audit = TRUE,\n           pre_audited_amount = NULL,\n           pre_audited_by = NULL,\n           certified_by = NULL,\n           work_started = FALSE,\n           comment = NULL\n           WHERE id = 'e20267f3-d5fc-4fa1-adf8-4b3e3c169087'","sqlMessage":"Unknown column 'received_by_audit' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'received_by_audit' in 'field list'\n    at PromisePoolConnection.query (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\mysql2\\lib\\promise\\connection.js:29:22)\n    at C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\dist\\routes\\batches.js:347:34\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-08 16:04:01"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'work_started' in 'field list'","service":"vms-server","sql":"UPDATE vouchers SET\n           status = 'AUDIT: PROCESSING',\n           flags = '{\\\"sentToAudit\\\":true,\\\"dispatched\\\":false,\\\"certified\\\":false,\\\"rejected\\\":false,\\\"isReturned\\\":false,\\\"pendingReturn\\\":false,\\\"receivedByAudit\\\":true}',\n           department = 'AUDIT',\n           original_department = COALESCE(original_department, department),\n           received_by = 'SAMUEL ASIEDU',\n           receipt_time = NOW(),\n           received_by_audit = TRUE,\n           pre_audited_amount = NULL,\n           pre_audited_by = NULL,\n           certified_by = NULL,\n           work_started = FALSE,\n           comment = NULL\n           WHERE id = '6e9027fb-cf60-4171-8526-66fa1bc84b9e'","sqlMessage":"Unknown column 'work_started' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'work_started' in 'field list'\n    at PromisePoolConnection.query (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\mysql2\\lib\\promise\\connection.js:29:22)\n    at C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\dist\\routes\\batches.js:349:34\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-08 16:10:14"}
{"code":"ER_DUP_ENTRY","errno":1062,"level":"error","message":"Duplicate entry 'FINJUL0001' for key 'vouchers.voucher_id'","service":"vms-server","sql":"INSERT INTO vouchers (\n          id, voucher_id, date, claimant, description, amount, currency, department,\n          original_department, dispatched_by, dispatch_time, status, sent_to_audit, created_by,\n          tax_type, tax_details, tax_amount, comment, idempotency_key, deleted\n        ) VALUES ('4eca4296-0bf6-4493-9a54-d17bbee1b836', 'FINJUL0001', 'JULY 08, 2025 AT 04:24 PM', 'YOYO', 'TEST', 4500, 'GHS', 'FINANCE', 'FINANCE', '', '', 'PENDING', false, 'FELIX AYISI', NULL, NULL, NULL, NULL, 'FINANCE-1751991894742-szwzxbdkf', false)","sqlMessage":"Duplicate entry 'FINJUL0001' for key 'vouchers.voucher_id'","sqlState":"23000","stack":"Error: Duplicate entry 'FINJUL0001' for key 'vouchers.voucher_id'\n    at PromisePoolConnection.query (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\mysql2\\lib\\promise\\connection.js:29:22)\n    at C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\dist\\routes\\vouchers.js:379:30\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-08 16:24:54"}
{"code":"ER_DUP_ENTRY","errno":1062,"level":"error","message":"Duplicate entry 'FINJUL0001' for key 'vouchers.voucher_id'","service":"vms-server","sql":"INSERT INTO vouchers (\n          id, voucher_id, date, claimant, description, amount, currency, department,\n          original_department, dispatched_by, dispatch_time, status, sent_to_audit, created_by,\n          tax_type, tax_details, tax_amount, comment, idempotency_key, deleted\n        ) VALUES ('83ef06cc-99b0-4837-96f7-f490ae5701ed', 'FINJUL0001', 'JULY 08, 2025 AT 07:14 PM', 'ted', 'test', 1000, 'GHS', 'FINANCE', 'FINANCE', '', '', 'PENDING', false, 'FELIX AYISI', NULL, NULL, NULL, NULL, 'FINANCE-1752002051081-fyt6zaczw', false)","sqlMessage":"Duplicate entry 'FINJUL0001' for key 'vouchers.voucher_id'","sqlState":"23000","stack":"Error: Duplicate entry 'FINJUL0001' for key 'vouchers.voucher_id'\n    at PromisePoolConnection.query (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\mysql2\\lib\\promise\\connection.js:29:22)\n    at C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\dist\\routes\\vouchers.js:379:30\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-08 19:14:11"}
{"code":"ER_DUP_ENTRY","errno":1062,"level":"error","message":"Duplicate entry 'FINJUL0001' for key 'vouchers.voucher_id'","service":"vms-server","sql":"INSERT INTO vouchers (\n          id, voucher_id, date, claimant, description, amount, currency, department,\n          original_department, dispatched_by, dispatch_time, status, sent_to_audit, created_by,\n          tax_type, tax_details, tax_amount, comment, idempotency_key, deleted\n        ) VALUES ('517a2830-caf0-4de2-9682-aed87f525d8a', 'FINJUL0001', 'JULY 08, 2025 AT 07:52 PM', 'chris', 'test', 250, 'GHS', 'FINANCE', 'FINANCE', '', '', 'PENDING', false, 'FELIX AYISI', NULL, NULL, NULL, NULL, 'FINANCE-1752004334352-5db6excdb', false)","sqlMessage":"Duplicate entry 'FINJUL0001' for key 'vouchers.voucher_id'","sqlState":"23000","stack":"Error: Duplicate entry 'FINJUL0001' for key 'vouchers.voucher_id'\n    at PromisePoolConnection.query (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\mysql2\\lib\\promise\\connection.js:29:22)\n    at C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\dist\\routes\\vouchers.js:379:30\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-08 19:52:14"}
{"ip":"::1","level":"error","message":"Expected property name or '}' in JSON at position 1 (line 1 column 2)","method":"POST","requestId":"unknown","service":"vms-server","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-07-10T15:14:52.735Z","url":"/api/auth/login","userAgent":"curl/8.13.0"}
{"ip":"::1","level":"error","message":"Expected property name or '}' in JSON at position 1 (line 1 column 2)","method":"POST","requestId":"unknown","service":"vms-server","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-07-10T15:15:06.078Z","url":"/api/auth/login","userAgent":"curl/8.13.0"}
{"ip":"::1","level":"error","message":"Expected property name or '}' in JSON at position 1 (line 1 column 2)","method":"POST","requestId":"unknown","service":"vms-server","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)\n    at invokeCallback (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-07-10T15:42:50.457Z","url":"/api/auth/login","userAgent":"curl/8.13.0"}
{"code":"ENOENT","errno":-4058,"expose":false,"level":"error","message":"Error serving index.html: ENOENT: no such file or directory, stat 'C:\\Users\\<USER>\\Desktop\\VMSPROD\\client\\dist\\index.html'","path":"C:\\Users\\<USER>\\Desktop\\VMSPROD\\client\\dist\\index.html","service":"vms-server","stack":"Error: ENOENT: no such file or directory, stat 'C:\\Users\\<USER>\\Desktop\\VMSPROD\\client\\dist\\index.html'","status":404,"statusCode":404,"syscall":"stat","timestamp":"2025-07-10 16:32:43"}
{"code":"ENOENT","errno":-4058,"expose":false,"level":"error","message":"Error serving index.html: ENOENT: no such file or directory, stat 'C:\\Users\\<USER>\\Desktop\\VMSPROD\\client\\dist\\index.html'","path":"C:\\Users\\<USER>\\Desktop\\VMSPROD\\client\\dist\\index.html","service":"vms-server","stack":"Error: ENOENT: no such file or directory, stat 'C:\\Users\\<USER>\\Desktop\\VMSPROD\\client\\dist\\index.html'","status":404,"statusCode":404,"syscall":"stat","timestamp":"2025-07-10 16:33:30"}
{"code":"ENOENT","errno":-4058,"expose":false,"level":"error","message":"Error serving index.html: ENOENT: no such file or directory, stat 'C:\\Users\\<USER>\\Desktop\\VMSPROD\\client\\dist\\index.html'","path":"C:\\Users\\<USER>\\Desktop\\VMSPROD\\client\\dist\\index.html","service":"vms-server","stack":"Error: ENOENT: no such file or directory, stat 'C:\\Users\\<USER>\\Desktop\\VMSPROD\\client\\dist\\index.html'","status":404,"statusCode":404,"syscall":"stat","timestamp":"2025-07-10 16:33:35"}
{"code":"ENOENT","errno":-4058,"expose":false,"level":"error","message":"Error serving index.html: ENOENT: no such file or directory, stat 'C:\\Users\\<USER>\\Desktop\\VMSPROD\\client\\dist\\index.html'","path":"C:\\Users\\<USER>\\Desktop\\VMSPROD\\client\\dist\\index.html","service":"vms-server","stack":"Error: ENOENT: no such file or directory, stat 'C:\\Users\\<USER>\\Desktop\\VMSPROD\\client\\dist\\index.html'","status":404,"statusCode":404,"syscall":"stat","timestamp":"2025-07-10 16:33:37"}
{"code":"ENOENT","errno":-4058,"expose":false,"level":"error","message":"Error serving index.html: ENOENT: no such file or directory, stat 'C:\\Users\\<USER>\\Desktop\\VMSPROD\\client\\dist\\index.html'","path":"C:\\Users\\<USER>\\Desktop\\VMSPROD\\client\\dist\\index.html","service":"vms-server","stack":"Error: ENOENT: no such file or directory, stat 'C:\\Users\\<USER>\\Desktop\\VMSPROD\\client\\dist\\index.html'","status":404,"statusCode":404,"syscall":"stat","timestamp":"2025-07-10 16:33:50"}
{"code":"ENOENT","errno":-4058,"expose":false,"level":"error","message":"Error serving index.html: ENOENT: no such file or directory, stat 'C:\\Users\\<USER>\\Desktop\\VMSPROD\\client\\dist\\index.html'","path":"C:\\Users\\<USER>\\Desktop\\VMSPROD\\client\\dist\\index.html","service":"vms-server","stack":"Error: ENOENT: no such file or directory, stat 'C:\\Users\\<USER>\\Desktop\\VMSPROD\\client\\dist\\index.html'","status":404,"statusCode":404,"syscall":"stat","timestamp":"2025-07-10 16:34:18"}
{"code":"ENOENT","errno":-4058,"expose":false,"level":"error","message":"Error serving index.html: ENOENT: no such file or directory, stat 'C:\\Users\\<USER>\\Desktop\\VMSPROD\\client\\dist\\index.html'","path":"C:\\Users\\<USER>\\Desktop\\VMSPROD\\client\\dist\\index.html","service":"vms-server","stack":"Error: ENOENT: no such file or directory, stat 'C:\\Users\\<USER>\\Desktop\\VMSPROD\\client\\dist\\index.html'","status":404,"statusCode":404,"syscall":"stat","timestamp":"2025-07-10 19:17:36"}
{"code":"ENOENT","errno":-4058,"expose":false,"level":"error","message":"Error serving index.html: ENOENT: no such file or directory, stat 'C:\\Users\\<USER>\\Desktop\\VMSPROD\\client\\dist\\index.html'","path":"C:\\Users\\<USER>\\Desktop\\VMSPROD\\client\\dist\\index.html","service":"vms-server","stack":"Error: ENOENT: no such file or directory, stat 'C:\\Users\\<USER>\\Desktop\\VMSPROD\\client\\dist\\index.html'","status":404,"statusCode":404,"syscall":"stat","timestamp":"2025-07-10 19:24:30"}
{"code":"ENOENT","errno":-4058,"expose":false,"level":"error","message":"Error serving index.html: ENOENT: no such file or directory, stat 'C:\\Users\\<USER>\\Desktop\\VMSPROD\\client\\dist\\index.html'","path":"C:\\Users\\<USER>\\Desktop\\VMSPROD\\client\\dist\\index.html","service":"vms-server","stack":"Error: ENOENT: no such file or directory, stat 'C:\\Users\\<USER>\\Desktop\\VMSPROD\\client\\dist\\index.html'","status":404,"statusCode":404,"syscall":"stat","timestamp":"2025-07-10 19:25:47"}
{"code":"ENOENT","errno":-4058,"expose":false,"level":"error","message":"Error serving index.html: ENOENT: no such file or directory, stat 'C:\\Users\\<USER>\\Desktop\\VMSPROD\\client\\dist\\index.html'","path":"C:\\Users\\<USER>\\Desktop\\VMSPROD\\client\\dist\\index.html","service":"vms-server","stack":"Error: ENOENT: no such file or directory, stat 'C:\\Users\\<USER>\\Desktop\\VMSPROD\\client\\dist\\index.html'","status":404,"statusCode":404,"syscall":"stat","timestamp":"2025-07-10 19:26:04"}
